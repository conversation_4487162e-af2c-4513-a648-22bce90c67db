<?php
session_start();
require_once '../../../koneksi.php';

// Cek session dan level
if (!isset($_SESSION['kd_user']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Ambil provinsi_id dari session
$provinsi_id = $_SESSION['provinsi_id'];

try {
    // Cek koneksi database
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Validasi input
    if (!isset($_POST['sk_akreditasi_id']) || !isset($_POST['nama_file'])) {
        echo json_encode(['success' => false, 'message' => 'Parameter tidak lengkap']);
        exit;
    }

    $sk_akreditasi_id = intval($_POST['sk_akreditasi_id']);
    $nama_file = $conn->real_escape_string($_POST['nama_file']);

    if ($sk_akreditasi_id <= 0 || empty($nama_file)) {
        echo json_encode(['success' => false, 'message' => 'Parameter tidak valid']);
        exit;
    }

    // Cek apakah SK ada dan milik provinsi yang sama
    $check_query = "SELECT sk_akreditasi_id, nama_file
                    FROM e_arsip_sk_akreditasi
                    WHERE sk_akreditasi_id = $sk_akreditasi_id
                      AND provinsi_id = $provinsi_id";
    
    $check_result = $conn->query($check_query);

    if (!$check_result || $check_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'SK tidak ditemukan atau tidak memiliki akses']);
        exit;
    }

    $sk_data = $check_result->fetch_assoc();

    // Path file yang akan dihapus
    $file_path = '../../../../simak/files/sk_akreditasi/' . $nama_file;

    // Hapus record dari database
    $delete_query = "DELETE FROM e_arsip_sk_akreditasi WHERE sk_akreditasi_id = $sk_akreditasi_id";
    
    if ($conn->query($delete_query)) {
        // Hapus file fisik jika ada
        if (file_exists($file_path)) {
            if (unlink($file_path)) {
                echo json_encode([
                    'success' => true, 
                    'message' => 'SK Akreditasi berhasil dihapus'
                ]);
            } else {
                // File tidak bisa dihapus, tapi record sudah terhapus
                error_log("Failed to delete file: $file_path");
                echo json_encode([
                    'success' => true, 
                    'message' => 'SK Akreditasi berhasil dihapus (file mungkin sudah tidak ada)'
                ]);
            }
        } else {
            // File tidak ada, tapi record sudah terhapus
            echo json_encode([
                'success' => true, 
                'message' => 'SK Akreditasi berhasil dihapus (file sudah tidak ada)'
            ]);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Gagal menghapus SK dari database']);
    }

} catch (Exception $e) {
    error_log("Delete SK Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
}

$conn->close();
?>
