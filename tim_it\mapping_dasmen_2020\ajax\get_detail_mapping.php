<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$id_mapping = isset($_POST['id_mapping']) ? intval($_POST['id_mapping']) : 0;

if ($id_mapping <= 0) {
    echo json_encode(['success' => false, 'message' => 'ID mapping tidak valid']);
    exit;
}

try {
    // Query untuk mengambil detail mapping dengan join ke tabel terkait
    $query = "SELECT 
                m.id_mapping,
                m.sekolah_id,
                m.kd_asesor1,
                m.kd_asesor2,
                m.tgl_audit_dokumen,
                m.tgl_mulai_visitasi,
                m.tgl_akhir_visitasi,
                m.tahap,
                m.no_surat_audit_dokumen,
                m.tgl_surat_audit_dokumen,
                m.no_surat,
                m.tgl_surat,
                m.tahun_akreditasi,
                m.model_pembelajaran,
                m.cara_visitasi,
                m.team,
                m.file_hasil_asesmen_kecukupan_1,
                m.tgl_file_hasil_asesmen_kecukupan_1,
                m.jam_file_hasil_asesmen_kecukupan_1,
                m.file_hasil_asesmen_kecukupan_2,
                m.tgl_file_hasil_asesmen_kecukupan_2,
                m.jam_file_hasil_asesmen_kecukupan_2,
                m.file_kesesuaian_dokumen_1,
                m.file_kesesuaian_dokumen_2,
                m.file_rekapitulasi_hasil_asesmen_kecukupan,
                m.file_pakta_integritas_1,
                m.file_pakta_integritas_2,
                m.file_laporan_individu_1,
                m.file_laporan_individu_2,
                m.file_laporan_kelompok,
                m.tgl_file_laporan_kelompok,
                m.jam_file_laporan_kelompok,
                m.file_rekomendasi,
                m.file_berita_acara_visitasi,
                m.file_foto_visitasi,
                m.provinsi_id,
                
                -- Data Sekolah
                s.nama_sekolah,
                s.npsn,
                s.jenjang_id,
                s.rumpun,
                s.alamat,
                s.kota_id,
                s.desa_kelurahan,
                s.kecamatan,
                s.nama_kepsek,
                s.no_hp_kepsek,
                s.no_wa_kepsek,
                s.nama_operator,
                s.no_hp_operator,
                s.no_wa_operator,
                s.email,
                
                -- Data Jenjang
                j.nm_jenjang,
                
                -- Data Kab/Kota Sekolah
                k.nm_kota,
                
                -- Data Asesor 1
                a1.nia1,
                a1.nm_asesor1,
                a1.no_hp as no_hp_asesor1,
                a1.kota_id1,
                
                -- Data Asesor 2
                a2.nia2,
                a2.nm_asesor2,
                a2.no_hp as no_hp_asesor2,
                a2.kota_id2,
                
                -- Data Kab/Kota Asesor 1
                k1.nm_kota as nm_kota_asesor1,
                
                -- Data Kab/Kota Asesor 2
                k2.nm_kota as nm_kota_asesor2
                
              FROM mapping m
              LEFT JOIN sekolah s ON m.sekolah_id = s.sekolah_id
              LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
              LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
              LEFT JOIN asesor_1 a1 ON m.kd_asesor1 = a1.kd_asesor1
              LEFT JOIN asesor_2 a2 ON m.kd_asesor2 = a2.kd_asesor2
              LEFT JOIN kab_kota k1 ON a1.kota_id1 = k1.kota_id
              LEFT JOIN kab_kota k2 ON a2.kota_id2 = k2.kota_id
              WHERE m.id_mapping = ? AND m.provinsi_id = ?";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ii", $id_mapping, $_SESSION['provinsi_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Data mapping tidak ditemukan']);
        exit;
    }
    
    $data = $result->fetch_assoc();
    
    echo json_encode([
        'success' => true,
        'data' => $data
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
