<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi required fields
    if (!isset($_POST['id_mapping']) || empty(trim($_POST['id_mapping']))) {
        throw new Exception('ID mapping harus diisi');
    }
    
    // Sanitasi input
    $id_mapping = intval($_POST['id_mapping']);
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Validasi ID mapping
    if ($id_mapping <= 0) {
        throw new Exception('ID mapping tidak valid');
    }
    
    // Debug: Log values
    error_log("Hapus Mapping Visitasi - ID: $id_mapping, Provinsi: $provinsi_id");
    
    // Cek apakah data mapping exists dan milik provinsi yang benar
    $check_query = "SELECT m.id_mapping, m.sekolah_id, m.tahun_akreditasi, m.tahap,
                           s.nama_sekolah, s.npsn,
                           a1.nm_asesor1, a2.nm_asesor2
                    FROM mapping m
                    LEFT JOIN sekolah s ON m.sekolah_id = s.sekolah_id
                    LEFT JOIN asesor_1 a1 ON m.kd_asesor1 = a1.kd_asesor1
                    LEFT JOIN asesor_2 a2 ON m.kd_asesor2 = a2.kd_asesor2
                    WHERE m.id_mapping = ? AND m.provinsi_id = ?";
    
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("ii", $id_mapping, $provinsi_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows === 0) {
        throw new Exception('Data mapping tidak ditemukan atau Anda tidak memiliki akses untuk menghapus data ini');
    }
    
    $mapping_data = $check_result->fetch_assoc();
    
    // Begin transaction untuk hard delete
    $conn->autocommit(false);
    
    // Hard delete dari tabel mapping
    $delete_query = "DELETE FROM mapping WHERE id_mapping = ? AND provinsi_id = ?";
    $delete_stmt = $conn->prepare($delete_query);
    $delete_stmt->bind_param("ii", $id_mapping, $provinsi_id);
    
    if (!$delete_stmt->execute()) {
        throw new Exception('Gagal menghapus data mapping: ' . $conn->error);
    }
    
    // Cek apakah ada row yang terhapus
    if ($delete_stmt->affected_rows === 0) {
        throw new Exception('Tidak ada data yang dihapus. Data mungkin sudah tidak ada atau Anda tidak memiliki akses');
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Log successful deletion
    error_log("Hapus Mapping Visitasi Success - ID: $id_mapping, Sekolah: " . $mapping_data['nama_sekolah'] . ", User: " . $_SESSION['nm_user']);
    
    echo json_encode([
        'success' => true,
        'message' => 'Data mapping visitasi berhasil dihapus',
        'data' => [
            'id_mapping' => $id_mapping,
            'nama_sekolah' => $mapping_data['nama_sekolah'],
            'npsn' => $mapping_data['npsn'],
            'nm_asesor1' => $mapping_data['nm_asesor1'],
            'nm_asesor2' => $mapping_data['nm_asesor2'],
            'tahun_akreditasi' => $mapping_data['tahun_akreditasi'],
            'tahap' => $mapping_data['tahap']
        ]
    ]);
    
} catch (Exception $e) {
    // Rollback transaction
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Hapus Mapping Visitasi Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
