<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi required fields
    if (!isset($_POST['id_mapping']) || empty(trim($_POST['id_mapping']))) {
        throw new Exception('ID mapping harus diisi');
    }
    
    // Sanitasi input
    $id_mapping = intval($_POST['id_mapping']);
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Validasi ID mapping
    if ($id_mapping <= 0) {
        throw new Exception('ID mapping tidak valid');
    }
    
    // Debug: Log values
    error_log("Hapus Mapping KPA - ID: $id_mapping, Provinsi: $provinsi_id");
    
    // Cek apakah data mapping exists dan milik provinsi yang benar
    $check_query = "SELECT mp.id_mapping, mp.sekolah_id, mp.tahun_akreditasi, mp.tahap,
                           s.nama_sekolah, s.npsn,
                           a.nm_asesor
                    FROM mapping_paud_kpa mp
                    LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
                    LEFT JOIN asesor a ON mp.kd_asesor = a.kd_asesor
                    WHERE mp.id_mapping = ? AND mp.provinsi_id = ?";
    
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("ii", $id_mapping, $provinsi_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows === 0) {
        throw new Exception('Data mapping tidak ditemukan atau Anda tidak memiliki akses untuk menghapus data ini');
    }
    
    $mapping_data = $check_result->fetch_assoc();
    
    // Begin transaction untuk hard delete
    $conn->autocommit(false);
    
    // Hard delete dari tabel mapping_paud_kpa
    $delete_query = "DELETE FROM mapping_paud_kpa WHERE id_mapping = ? AND provinsi_id = ?";
    $delete_stmt = $conn->prepare($delete_query);
    $delete_stmt->bind_param("ii", $id_mapping, $provinsi_id);
    
    if (!$delete_stmt->execute()) {
        throw new Exception('Gagal menghapus data mapping KPA: ' . $conn->error);
    }
    
    // Cek apakah ada row yang terhapus
    if ($delete_stmt->affected_rows === 0) {
        throw new Exception('Tidak ada data yang dihapus. Data mungkin sudah tidak ada atau Anda tidak memiliki akses');
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Log successful deletion
    error_log("Hapus Mapping KPA Success - ID: $id_mapping, Sekolah: " . $mapping_data['nama_sekolah'] . ", Asesor: " . $mapping_data['nm_asesor'] . ", User: " . $_SESSION['nm_user']);
    
    echo json_encode([
        'success' => true,
        'message' => 'Data mapping KPA berhasil dihapus',
        'data' => [
            'id_mapping' => $id_mapping,
            'nama_sekolah' => $mapping_data['nama_sekolah'],
            'npsn' => $mapping_data['npsn'],
            'nm_asesor' => $mapping_data['nm_asesor'],
            'tahun_akreditasi' => $mapping_data['tahun_akreditasi'],
            'tahap' => $mapping_data['tahap']
        ]
    ]);
    
} catch (Exception $e) {
    // Rollback transaction
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Hapus Mapping KPA Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
