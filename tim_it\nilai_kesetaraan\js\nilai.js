/**
 * JavaScript untuk modul Data Nilai Akreditasi
 */

$(document).ready(function() {
    // Inisialisasi DataTable
    initDataTable();
    
    // Event handlers
    initEventHandlers();
});

/**
 * Inisialisasi DataTable
 */
function initDataTable() {
    $('#table-nilai').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: 'ajax/get_nilai.php',
            type: 'POST',
            error: function(xhr, error, thrown) {
                console.error('DataTables error:', error);
                showAlert('error', 'Gagal memuat data nilai akreditasi');
            }
        },
        columns: [
            { 
                data: null,
                orderable: false,
                searchable: false,
                render: function(data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { 
                data: 'npsn',
                name: 'npsn',
                className: 'text-center',
                render: function(data, type, row) {
                    return '<strong>' + data + '</strong>';
                }
            },
            { 
                data: 'nama_sekolah',
                name: 'nama_sekolah',
                render: function(data, type, row) {
                    if (data && data.length > 40) {
                        return data.substring(0, 40) + '...';
                    }
                    return data || '-';
                }
            },
            { 
                data: 'nm_jenjang',
                name: 'nm_jenjang',
                className: 'text-center'
            },
            { 
                data: 'nm_kota',
                name: 'nm_kota',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            {
                data: 'nilai_akhir',
                name: 'nilai_akhir',
                className: 'text-center',
                render: function(data, type, row) {
                    return '<span class="badge badge-primary">' + data + '</span>';
                }
            },
            {
                data: 'program',
                name: 'program',
                className: 'text-center',
                render: function(data, type, row) {
                    var badgeClass = 'badge-secondary';
                    if (data == 'Paket A') {
                        badgeClass = 'badge-success';
                    } else if (data == 'Paket B') {
                        badgeClass = 'badge-info';
                    } else if (data == 'Paket C') {
                        badgeClass = 'badge-warning';
                    } else if (data == 'Satdik') {
                        badgeClass = 'badge-dark';
                    }
                    return '<span class="badge ' + badgeClass + '">' + (data || '-') + '</span>';
                }
            },
            {
                data: 'peringkat',
                name: 'peringkat',
                className: 'text-center',
                render: function(data, type, row) {
                    var badgeClass = 'badge-secondary';
                    if (data == 'A') {
                        badgeClass = 'badge-success';
                    } else if (data == 'B') {
                        badgeClass = 'badge-info';
                    } else if (data == 'C') {
                        badgeClass = 'badge-warning';
                    } else if (data == 'TT') {
                        badgeClass = 'badge-danger';
                    }
                    return '<span class="badge ' + badgeClass + '">' + data + '</span>';
                }
            },
            {
                data: 'status',
                name: 'status',
                className: 'text-center',
                render: function(data, type, row) {
                    if (data == 'Terakreditasi') {
                        return '<span class="badge badge-success">' + data + '</span>';
                    } else if (data == 'Tidak Terakreditasi') {
                        return '<span class="badge badge-danger">' + data + '</span>';
                    } else {
                        return '<span class="badge badge-warning">' + data + '</span>';
                    }
                }
            },
            { 
                data: 'tahun_akreditasi',
                name: 'tahun_akreditasi',
                className: 'text-center'
            },
            { 
                data: 'tahun_berakhir',
                name: 'tahun_berakhir',
                className: 'text-center'
            },
            {
                data: null,
                orderable: false,
                searchable: false,
                className: 'text-center',
                render: function(data, type, row) {
                    return '<button class="btn btn-info btn-sm" onclick="showDetail(' +
                           row.sekolah_id + ', \'' + row.nama_sekolah.replace(/'/g, "\\'") + '\')" title="Detail Riwayat">' +
                           '<i class="fas fa-eye"></i></button>';
                }
            }
        ],
        order: [[9, 'desc']], // Order by tahun_akreditasi DESC (kolom ke-9 setelah penambahan program)
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: "Memuat data...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(difilter dari _MAX_ total data)",
            loadingRecords: "Memuat data...",
            zeroRecords: "Tidak ada data yang ditemukan",
            emptyTable: "Tidak ada data tersedia",
            paginate: {
                first: "Pertama",
                last: "Terakhir",
                next: "Selanjutnya",
                previous: "Sebelumnya"
            }
        },
        responsive: true,
        autoWidth: false,
        columnDefs: [
            {
                targets: 0, // Kolom NO
                width: '40px'
            },
            {
                targets: 1, // Kolom NPSN
                width: '80px'
            },
            {
                targets: 3, // Kolom JENJANG
                width: '80px'
            },
            {
                targets: 5, // Kolom NILAI
                width: '80px'
            },
            {
                targets: 6, // Kolom PROGRAM PAKET
                width: '100px'
            },
            {
                targets: 7, // Kolom PERINGKAT
                width: '80px'
            },
            {
                targets: 8, // Kolom STATUS
                width: '100px'
            },
            {
                targets: 9, // Kolom TAHUN
                width: '80px'
            },
            {
                targets: 10, // Kolom TAHUN BERAKHIR
                width: '80px'
            },
            {
                targets: 11, // Kolom AKSI
                width: '80px'
            }
        ]
    });
}

/**
 * Inisialisasi event handlers
 */
function initEventHandlers() {
    // Event handler untuk tombol tambah
    $('#btn-add').on('click', function() {
        showTambahModal();
    });

    // Event handler untuk form tambah
    $('#form-tambah-nilai').on('submit', function(e) {
        e.preventDefault();
        simpanNilai();
    });

    // Event handler untuk input NPSN (validasi sekolah)
    $(document).on('blur', '#npsn', function() {
        var npsn = $(this).val().trim();
        if (npsn) {
            validateNPSN(npsn);
        }
    });

    // Event handler untuk input NPSN saat enter
    $(document).on('keypress', '#npsn', function(e) {
        if (e.which == 13) { // Enter key
            var npsn = $(this).val().trim();
            if (npsn) {
                validateNPSN(npsn);
            }
        }
    });

    // Event handler untuk form edit nested
    $('#form-edit-nilai-nested').on('submit', function(e) {
        e.preventDefault();
        updateNilaiNested();
    });

    // Event handler untuk konfirmasi delete
    $('#btn-confirm-delete').on('click', function() {
        var hasilId = $(this).data('hasil-id');
        if (hasilId) {
            executeDelete(hasilId);
        }
    });

    // Event handler untuk export nilai terakhir
    $('#btn-export-terakhir').on('click', function() {
        exportNilaiTerakhir();
    });

    // Event handler untuk export keseluruhan
    $('#btn-export-keseluruhan').on('click', function() {
        exportKeseluruhan();
    });
}

/**
 * Fungsi untuk menampilkan detail riwayat nilai akreditasi
 */
function showDetail(sekolahId, namaSekolah) {
    // Show loading
    $('#modal-detail-content').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Memuat data...</div>');
    $('#modal-detail-label').html('<i class="fas fa-eye"></i> Detail Riwayat Nilai Akreditasi - ' + namaSekolah);

    // Simpan sekolah_id di modal data attribute untuk refresh nanti
    $('#modal-detail').data('sekolah-id', sekolahId);
    $('#modal-detail').modal('show');

    // Load detail via AJAX
    $.ajax({
        url: 'ajax/get_detail_nilai.php',
        type: 'POST',
        data: { sekolah_id: sekolahId },
        dataType: 'html',
        success: function(response) {
            $('#modal-detail-content').html(response);
        },
        error: function(xhr, status, error) {
            $('#modal-detail-content').html('<div class="alert alert-danger">Gagal memuat detail data</div>');
        }
    });
}



/**
 * Fungsi untuk menampilkan alert
 */
function showAlert(type, message) {
    var alertClass = 'alert-info';
    var icon = 'fas fa-info-circle';
    var title = 'Informasi';

    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            icon = 'fas fa-check-circle';
            title = 'Berhasil';
            break;
        case 'error':
            alertClass = 'alert-danger';
            icon = 'fas fa-exclamation-circle';
            title = 'Error';
            break;
        case 'warning':
            alertClass = 'alert-warning';
            icon = 'fas fa-exclamation-triangle';
            title = 'Peringatan';
            break;
    }

    // Update modal notification
    $('#modal-notification-header').removeClass().addClass('modal-header ' + alertClass);
    $('#modal-notification-icon').removeClass().addClass(icon);
    $('#modal-notification-text').text(title);
    $('#modal-notification-message').text(message);

    // Show modal
    $('#modal-notification').modal('show');

    // Auto hide after 3 seconds for info messages
    if (type === 'info') {
        setTimeout(function() {
            $('#modal-notification').modal('hide');
        }, 3000);
    }
}

/**
 * Fungsi untuk menampilkan modal tambah
 */
function showTambahModal() {
    // Load form tambah
    loadTambahForm();

    // Show modal
    $('#modal-tambah').modal('show');
}

/**
 * Fungsi untuk load form tambah
 */
function loadTambahForm() {
    $.ajax({
        url: 'ajax/get_dropdown_options.php',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var formHtml = generateTambahForm(response.data);
                $('#modal-tambah .modal-body').html(formHtml);

                // Initialize Select2 if available
                if ($.fn.select2) {
                    $('.select2').select2({
                        theme: 'bootstrap4',
                        width: '100%'
                    });
                }
            } else {
                $('#modal-tambah .modal-body').html('<div class="alert alert-danger">Gagal memuat form</div>');
            }
        },
        error: function() {
            $('#modal-tambah .modal-body').html('<div class="alert alert-danger">Gagal memuat form</div>');
        }
    });
}

/**
 * Fungsi untuk generate form tambah
 */
function generateTambahForm(dropdownData) {
    var progAhliOptions = '<option value="">-- Silahkan Pilih --</option>';
    if (dropdownData.prog_ahli) {
        dropdownData.prog_ahli.forEach(function(item) {
            progAhliOptions += '<option value="' + item.id_prog_ahli + '">' + item.nm_prog_ahli + '</option>';
        });
    }

    return `
        <div class="row">
            <!-- Kolom Kiri -->
            <div class="col-md-6">
                <div class="form-group">
                    <label for="npsn">NPSN <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="npsn" name="npsn" required maxlength="20">
                    <small class="form-text text-muted">Masukkan NPSN sekolah</small>
                    <div id="npsn-info" class="mt-2"></div>
                </div>

                <div class="form-group">
                    <label for="id_prog_ahli">Untuk Penilaian <span class="text-danger">*</span></label>
                    <select id="id_prog_ahli" name="id_prog_ahli" class="form-control select2" required>
                        ${progAhliOptions}
                    </select>
                </div>

                <div class="form-group">
                    <label for="nilai_akhir">Nilai Akhir</label>
                    <input type="number" class="form-control" id="nilai_akhir" name="nilai_akhir" min="0" max="100" placeholder="Kosongkan jika belum ada nilai">
                    <small class="form-text text-muted">Boleh dikosongkan atau diisi 0 jika belum ada nilai</small>
                </div>

                <div class="form-group">
                    <label for="program">Program Paket <span class="text-danger">*</span></label>
                    <select class="form-control" id="program" name="program" required>
                        <option value="">-- Pilih Program Paket --</option>
                        <option value="Paket A">Paket A</option>
                        <option value="Paket B">Paket B</option>
                        <option value="Paket C">Paket C</option>
                        <option value="Satdik">Satdik</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="peringkat">Peringkat <span class="text-danger">*</span></label>
                    <select class="form-control" id="peringkat" name="peringkat" required>
                        <option value="">-- Pilih Peringkat --</option>
                        <option value="A">A</option>
                        <option value="B">B</option>
                        <option value="C">C</option>
                        <option value="TT">TT (Tidak Terakreditasi)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="status">Status Akreditasi <span class="text-danger">*</span></label>
                    <select class="form-control" id="status" name="status" required>
                        <option value="">-- Pilih Status --</option>
                        <option value="Terakreditasi">Terakreditasi</option>
                        <option value="Tidak Terakreditasi">Tidak Terakreditasi</option>
                    </select>
                </div>
            </div>

            <!-- Kolom Kanan -->
            <div class="col-md-6">
                <div class="form-group">
                    <label for="tahun_akreditasi">Tahun Akreditasi <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="tahun_akreditasi" name="tahun_akreditasi" required min="2000" max="2050">
                </div>

                <div class="form-group">
                    <label for="tahun_berakhir">Tahun Akreditasi Berakhir <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="tahun_berakhir" name="tahun_berakhir" required min="2000" max="2050">
                </div>

                <div class="form-group">
                    <label for="tgl_sk_penetapan">Tanggal SK Penetapan</label>
                    <input type="date" class="form-control" id="tgl_sk_penetapan" name="tgl_sk_penetapan">
                </div>

                <div class="form-group">
                    <label for="no_sk">Nomor SK Hasil Akreditasi</label>
                    <input type="text" class="form-control" id="no_sk" name="no_sk" maxlength="50">
                </div>
            </div>
        </div>

        <!-- Hidden field untuk sekolah_id -->
        <input type="hidden" id="sekolah_id" name="sekolah_id" value="">
    `;
}

/**
 * Fungsi untuk validasi NPSN
 */
function validateNPSN(npsn) {
    $.ajax({
        url: 'ajax/validate_npsn.php',
        type: 'POST',
        data: { npsn: npsn },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $('#npsn-info').html('<div class="alert alert-success alert-sm"><i class="fas fa-check"></i> ' + response.data.nama_sekolah + '</div>');
                $('#sekolah_id').val(response.data.sekolah_id);
            } else {
                $('#npsn-info').html('<div class="alert alert-danger alert-sm"><i class="fas fa-times"></i> ' + response.message + '</div>');
                $('#sekolah_id').val('');
            }
        },
        error: function() {
            $('#npsn-info').html('<div class="alert alert-danger alert-sm"><i class="fas fa-times"></i> Gagal validasi NPSN</div>');
            $('#sekolah_id').val('');
        }
    });
}

/**
 * Fungsi untuk simpan data nilai
 */
function simpanNilai() {
    // Cek apakah sekolah_id sudah terisi
    var sekolahId = $('#sekolah_id').val();
    var npsn = $('#npsn').val().trim();

    if (!sekolahId && npsn) {
        // Jika sekolah_id kosong tapi NPSN ada, validasi dulu
        showAlert('warning', 'Sedang memvalidasi NPSN, silakan tunggu...');
        validateNPSN(npsn);

        // Tunggu sebentar lalu coba lagi
        setTimeout(function() {
            if ($('#sekolah_id').val()) {
                simpanNilai(); // Rekursif call setelah validasi
            } else {
                showAlert('error', 'NPSN tidak valid atau belum divalidasi');
                $('#btn-simpan').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Data');
            }
        }, 1000);
        return;
    }

    // Disable tombol submit
    $('#btn-simpan').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    // Serialize form data
    var formData = $('#form-tambah-nilai').serialize();



    $.ajax({
        url: 'ajax/simpan_nilai.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Tutup modal
                $('#modal-tambah').modal('hide');

                // Reload DataTable
                refreshMainTable();

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat menyimpan data');
        },
        complete: function() {
            // Enable tombol submit
            $('#btn-simpan').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Data');
        }
    });
}

/**
 * Fungsi untuk update data nilai dari modal nested
 */
function updateNilaiNested() {
    // Disable tombol submit
    $('#btn-update-nested').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    // Serialize form data
    var formData = $('#form-edit-nilai-nested').serialize();

    $.ajax({
        url: 'ajax/update_nilai.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Smooth close modal edit nested dengan fade effect
                $('#modal-edit-nested').on('hidden.bs.modal', function() {
                    // Remove event listener to prevent multiple bindings
                    $(this).off('hidden.bs.modal');

                    // Refresh tabel utama (server-side reload) dengan delay kecil
                    setTimeout(function() {
                        refreshMainTable();
                    }, 100);

                    // Refresh modal detail dengan smooth animation
                    var sekolahId = $('#modal-detail').data('sekolah-id');
                    if (sekolahId) {
                        setTimeout(function() {
                            refreshModalDetail(sekolahId);
                        }, 300);
                    }
                });

                // Close modal with fade effect
                $('#modal-edit-nested').modal('hide');

                // Show subtle success message (tidak menggunakan alert modal yang mengganggu)
                showSuccessToast(response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat menyimpan data');
        },
        complete: function() {
            // Enable tombol submit
            $('#btn-update-nested').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Perubahan');
        }
    });
}

/**
 * Fungsi untuk refresh tabel utama (server-side reload)
 */
function refreshMainTable() {
    if ($.fn.DataTable.isDataTable('#table-nilai')) {
        $('#table-nilai').DataTable().ajax.reload(null, false);
    }
}

/**
 * Fungsi untuk refresh modal detail setelah edit dengan smooth animation
 */
function refreshModalDetail(sekolahId) {

    // Fade out content dengan smooth transition
    $('#modal-detail-content').fadeOut(300, function() {
        // Show professional loading animation dengan progress indicator
        $(this).html(`
            <div class="text-center py-5">
                <div class="mb-4">
                    <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                        <span class="sr-only">Loading...</span>
                    </div>
                </div>
                <div class="progress mb-3" style="height: 4px; max-width: 300px; margin: 0 auto;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary"
                         role="progressbar" style="width: 0%"></div>
                </div>
                <div class="mt-3">
                    <h6 class="text-muted mb-1">
                        <i class="fas fa-sync-alt fa-spin mr-2"></i>
                        Memperbarui data riwayat...
                    </h6>
                    <small class="text-muted">Mohon tunggu sebentar</small>
                </div>
            </div>
        `).fadeIn(200);

        // Animate progress bar
        var progressBar = $(this).find('.progress-bar');
        progressBar.animate({width: '30%'}, 200)
                  .animate({width: '60%'}, 300)
                  .animate({width: '90%'}, 200);

        // Reload detail via AJAX
        $.ajax({
            url: 'ajax/get_detail_nilai.php',
            type: 'POST',
            data: { sekolah_id: sekolahId },
            dataType: 'html',
            success: function(response) {
                // Fade out loading, then fade in new content
                $('#modal-detail-content').fadeOut(200, function() {
                    $(this).html(response).fadeIn(400, function() {
                        // Add success highlight animation dengan staggered effect
                        $(this).find('table tbody tr').each(function(index) {
                            $(this).hide().delay(index * 50).fadeIn(300);
                        });

                        // Highlight updated row dengan subtle glow effect
                        setTimeout(function() {
                            $('#modal-detail-content table tbody tr:first').addClass('table-success')
                                .css({
                                    'box-shadow': '0 0 10px rgba(40, 167, 69, 0.3)',
                                    'border-left': '4px solid #28a745'
                                });

                            // Remove highlight after 3 seconds
                            setTimeout(function() {
                                $('#modal-detail-content table tbody tr:first').removeClass('table-success')
                                    .css({
                                        'box-shadow': '',
                                        'border-left': ''
                                    });
                            }, 3000);
                        }, 500);

                        // Show subtle success indicator
                        showSuccessToast('Data berhasil diperbarui');
                    });
                });
            },
            error: function(xhr, status, error) {
                $('#modal-detail-content').fadeOut(200, function() {
                    $(this).html(`
                        <div class="alert alert-danger fade-in">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            Gagal memuat ulang detail data
                        </div>
                    `).fadeIn(300);
                });
            }
        });
    });
}

/**
 * Fungsi untuk menampilkan success toast yang smooth
 */
function showSuccessToast(message) {
    // Remove existing toast if any
    $('.success-toast').remove();

    // Create toast element
    var toast = $(`
        <div class="success-toast position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
            <div class="alert alert-success alert-dismissible fade show shadow-sm" role="alert" style="min-width: 300px;">
                <i class="fas fa-check-circle mr-2"></i>
                ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        </div>
    `);

    // Add to body with animation
    $('body').append(toast);
    toast.hide().slideDown(300);

    // Auto hide after 3 seconds
    setTimeout(function() {
        toast.slideUp(300, function() {
            $(this).remove();
        });
    }, 3000);
}

/**
 * Fungsi untuk edit nilai dari modal detail (nested modal)
 */
function editNilaiFromDetail(hasilId) {
    // Show loading di modal edit nested
    $('#modal-edit-nested-content').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Memuat form edit...</div>');
    $('#modal-edit-nested').modal('show');

    // Load form edit via AJAX
    $.ajax({
        url: 'ajax/get_edit_nilai_form.php',
        type: 'POST',
        data: { id_hasil_akreditasi: hasilId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var formHtml = generateEditForm(response.data, response.prog_ahli_options, response.tgl_sk_formatted);
                $('#modal-edit-nested-content').html(formHtml);

                // Initialize Select2 if available
                if ($.fn.select2) {
                    $('.select2').select2({
                        theme: 'bootstrap4',
                        width: '100%'
                    });
                }
            } else {
                $('#modal-edit-nested-content').html('<div class="alert alert-danger">' + response.message + '</div>');
            }
        },
        error: function(xhr, status, error) {
            $('#modal-edit-nested-content').html('<div class="alert alert-danger">Gagal memuat form edit</div>');
        }
    });
}

/**
 * Fungsi untuk generate form edit
 */
function generateEditForm(data, progAhliOptions, tglSkFormatted) {
    var progAhliOptionsHtml = '<option value="">-- Silahkan Pilih --</option>';
    progAhliOptions.forEach(function(item) {
        var selected = (item.id_prog_ahli == data.id_prog_ahli) ? 'selected' : '';
        progAhliOptionsHtml += '<option value="' + item.id_prog_ahli + '" ' + selected + '>' + item.nm_prog_ahli + '</option>';
    });

    return `
        <!-- Info Sekolah (Read Only) -->
        <div class="alert alert-info">
            <h6><i class="fas fa-school"></i> Informasi Sekolah</h6>
            <div class="row">
                <div class="col-md-3"><strong>NPSN:</strong> ${data.npsn}</div>
                <div class="col-md-6"><strong>Nama Sekolah:</strong> ${data.nama_sekolah}</div>
                <div class="col-md-3"><strong>Jenjang:</strong> ${data.nm_jenjang}</div>
            </div>
        </div>

        <div class="row">
            <!-- Kolom Kiri -->
            <div class="col-md-6">
                <div class="form-group">
                    <label for="edit_id_prog_ahli">Untuk Penilaian <span class="text-danger">*</span></label>
                    <select id="edit_id_prog_ahli" name="id_prog_ahli" class="form-control select2" required>
                        ${progAhliOptionsHtml}
                    </select>
                </div>

                <div class="form-group">
                    <label for="edit_nilai_akhir">Nilai Akhir</label>
                    <input type="number" class="form-control" id="edit_nilai_akhir" name="nilai_akhir"
                           min="0" max="100" value="${data.nilai_akhir || ''}"
                           placeholder="Kosongkan jika belum ada nilai">
                    <small class="form-text text-muted">Boleh dikosongkan atau diisi 0 jika belum ada nilai</small>
                </div>

                <div class="form-group">
                    <label for="edit_program">Program Paket <span class="text-danger">*</span></label>
                    <select class="form-control" id="edit_program" name="program" required>
                        <option value="">-- Pilih Program Paket --</option>
                        <option value="Paket A" ${data.program == 'Paket A' ? 'selected' : ''}>Paket A</option>
                        <option value="Paket B" ${data.program == 'Paket B' ? 'selected' : ''}>Paket B</option>
                        <option value="Paket C" ${data.program == 'Paket C' ? 'selected' : ''}>Paket C</option>
                        <option value="Satdik" ${data.program == 'Satdik' ? 'selected' : ''}>Satdik</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="edit_peringkat">Peringkat <span class="text-danger">*</span></label>
                    <select class="form-control" id="edit_peringkat" name="peringkat" required>
                        <option value="">-- Pilih Peringkat --</option>
                        <option value="A" ${data.peringkat == 'A' ? 'selected' : ''}>A</option>
                        <option value="B" ${data.peringkat == 'B' ? 'selected' : ''}>B</option>
                        <option value="C" ${data.peringkat == 'C' ? 'selected' : ''}>C</option>
                        <option value="TT" ${data.peringkat == 'TT' ? 'selected' : ''}>TT (Tidak Terakreditasi)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="edit_status">Status Akreditasi <span class="text-danger">*</span></label>
                    <select class="form-control" id="edit_status" name="status" required>
                        <option value="">-- Pilih Status --</option>
                        <option value="Terakreditasi" ${data.status == 'Terakreditasi' ? 'selected' : ''}>Terakreditasi</option>
                        <option value="Tidak Terakreditasi" ${data.status == 'Tidak Terakreditasi' ? 'selected' : ''}>Tidak Terakreditasi</option>
                    </select>
                </div>
            </div>

            <!-- Kolom Kanan -->
            <div class="col-md-6">
                <div class="form-group">
                    <label for="edit_tahun_akreditasi">Tahun Akreditasi <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="edit_tahun_akreditasi" name="tahun_akreditasi"
                           required min="2000" max="2050" value="${data.tahun_akreditasi}">
                </div>

                <div class="form-group">
                    <label for="edit_tahun_berakhir">Tahun Akreditasi Berakhir <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="edit_tahun_berakhir" name="tahun_berakhir"
                           required min="2000" max="2050" value="${data.tahun_berakhir}">
                </div>

                <div class="form-group">
                    <label for="edit_tgl_sk_penetapan">Tanggal SK Penetapan</label>
                    <input type="date" class="form-control" id="edit_tgl_sk_penetapan" name="tgl_sk_penetapan"
                           value="${tglSkFormatted}">
                </div>

                <div class="form-group">
                    <label for="edit_no_sk">Nomor SK Hasil Akreditasi</label>
                    <input type="text" class="form-control" id="edit_no_sk" name="no_sk"
                           maxlength="50" value="${data.no_sk || ''}">
                </div>
            </div>
        </div>

        <!-- Hidden field untuk ID -->
        <input type="hidden" name="id_hasil_akreditasi" value="${data.id_hasil_akreditasi}">
    `;
}

/**
 * Fungsi untuk hapus nilai dari modal detail dengan konfirmasi
 */
function deleteNilaiFromDetail(hasilId, tahunAkreditasi) {

    // Get info sekolah dari modal detail yang sedang terbuka
    var namaSekolah = $('#modal-detail-label').text().replace('Detail Riwayat Nilai Akreditasi - ', '');

    // Set info data yang akan dihapus
    $('#delete-info-content').html(`
        <div class="row">
            <div class="col-sm-4"><strong>Sekolah:</strong></div>
            <div class="col-sm-8">${namaSekolah}</div>
        </div>
        <div class="row">
            <div class="col-sm-4"><strong>Tahun Akreditasi:</strong></div>
            <div class="col-sm-8">${tahunAkreditasi}</div>
        </div>
    `);

    // Simpan ID untuk proses delete
    $('#btn-confirm-delete').data('hasil-id', hasilId);

    // Show modal konfirmasi
    $('#modal-confirm-delete').modal('show');
}

/**
 * Fungsi untuk eksekusi delete setelah konfirmasi
 */
function executeDelete(hasilId) {

    // Disable tombol dan show loading
    $('#btn-confirm-delete').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menghapus...');

    $.ajax({
        url: 'ajax/delete_nilai.php',
        type: 'POST',
        data: { id_hasil_akreditasi: hasilId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Tutup modal konfirmasi
                $('#modal-confirm-delete').modal('hide');

                // Refresh tabel utama (server-side reload) dengan delay kecil
                setTimeout(function() {
                    refreshMainTable();
                }, 100);

                // Refresh modal detail dengan smooth animation
                var sekolahId = $('#modal-detail').data('sekolah-id');
                if (sekolahId) {
                    setTimeout(function() {
                        refreshModalDetail(sekolahId);
                    }, 300);
                }

                // Show success toast
                showSuccessToast(response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat menghapus data');
        },
        complete: function() {
            // Enable tombol kembali
            $('#btn-confirm-delete').prop('disabled', false).html('<i class="fas fa-trash"></i> Ya, Hapus Permanen');
        }
    });
}

/**
 * Fungsi untuk export nilai terakhir ke Excel
 */
function exportNilaiTerakhir() {
    // Show loading
    showAlert('info', 'Sedang memproses export Excel nilai terakhir...');

    // Disable tombol export
    $('#btn-export-terakhir').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');

    // Get data from server
    $.ajax({
        url: 'ajax/get_export_nilai_terakhir.php',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Prepare data for Excel
                var excelData = [];

                // Add header row
                excelData.push([
                    'NO',
                    'NPSN',
                    'NAMA SEKOLAH',
                    'JENJANG',
                    'KAB/KOTA',
                    'NILAI AKHIR',
                    'PROGRAM PAKET',
                    'PERINGKAT',
                    'STATUS',
                    'TANGGAL SK',
                    'NOMOR SK',
                    'TAHUN AKREDITASI',
                    'TAHUN BERAKHIR',
                    'UNTUK AKREDITASI'
                ]);

                // Add data rows
                response.data.forEach(function(row, index) {
                    excelData.push([
                        index + 1,
                        row.npsn || '',
                        row.nama_sekolah || '',
                        row.nm_jenjang || '',
                        row.nm_kota || '',
                        row.nilai_akhir || '',
                        row.program || '',
                        row.peringkat || '',
                        row.status || '',
                        row.tgl_sk_formatted || '',
                        row.no_sk || '',
                        row.tahun_akreditasi || '',
                        row.tahun_berakhir || '',
                        row.nm_prog_ahli || ''
                    ]);
                });

                // Create workbook and worksheet
                var wb = XLSX.utils.book_new();
                var ws = XLSX.utils.aoa_to_sheet(excelData);

                // Set column widths
                ws['!cols'] = [
                    {wch: 5},   // NO
                    {wch: 12},  // NPSN
                    {wch: 30},  // NAMA SEKOLAH
                    {wch: 10},  // JENJANG
                    {wch: 15},  // KAB/KOTA
                    {wch: 12},  // NILAI AKHIR
                    {wch: 12},  // PROGRAM PAKET
                    {wch: 10},  // PERINGKAT
                    {wch: 15},  // STATUS
                    {wch: 12},  // TANGGAL SK
                    {wch: 20},  // NOMOR SK
                    {wch: 15},  // TAHUN AKREDITASI
                    {wch: 15},  // TAHUN BERAKHIR
                    {wch: 25}   // UNTUK AKREDITASI
                ];

                // Add worksheet to workbook
                XLSX.utils.book_append_sheet(wb, ws, "Nilai Akreditasi Terakhir");

                // Generate filename with current date
                var today = new Date();
                var dateStr = today.getFullYear() + '-' +
                             String(today.getMonth() + 1).padStart(2, '0') + '-' +
                             String(today.getDate()).padStart(2, '0');
                var filename = 'Nilai_Akreditasi_Terakhir_' + dateStr + '.xlsx';

                // Save file
                XLSX.writeFile(wb, filename);

                // Show success message
                showAlert('success', 'File Excel berhasil didownload: ' + filename);

            } else {
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat export data');
        },
        complete: function() {
            // Enable tombol export kembali
            $('#btn-export-terakhir').prop('disabled', false).html('<i class="fas fa-file-excel"></i> Export Nilai Terakhir');
        }
    });
}

/**
 * Fungsi untuk export keseluruhan data ke Excel
 */
function exportKeseluruhan() {
    // Show loading
    showAlert('info', 'Sedang memproses export Excel keseluruhan data...');

    // Disable tombol export
    $('#btn-export-keseluruhan').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');

    // Get data from server
    $.ajax({
        url: 'ajax/get_export_keseluruhan.php',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Prepare data for Excel
                var excelData = [];

                // Add header row
                excelData.push([
                    'NO',
                    'NPSN',
                    'NAMA SEKOLAH',
                    'JENJANG',
                    'KAB/KOTA',
                    'NILAI AKHIR',
                    'PROGRAM PAKET',
                    'PERINGKAT',
                    'STATUS',
                    'TANGGAL SK',
                    'NOMOR SK',
                    'TAHUN AKREDITASI',
                    'TAHUN BERAKHIR',
                    'UNTUK AKREDITASI'
                ]);

                // Add data rows
                response.data.forEach(function(row, index) {
                    excelData.push([
                        index + 1,
                        row.npsn || '',
                        row.nama_sekolah || '',
                        row.nm_jenjang || '',
                        row.nm_kota || '',
                        row.nilai_akhir || '',
                        row.program || '',
                        row.peringkat || '',
                        row.status || '',
                        row.tgl_sk_formatted || '',
                        row.no_sk || '',
                        row.tahun_akreditasi || '',
                        row.tahun_berakhir || '',
                        row.nm_prog_ahli || ''
                    ]);
                });

                // Create workbook and worksheet
                var wb = XLSX.utils.book_new();
                var ws = XLSX.utils.aoa_to_sheet(excelData);

                // Set column widths
                ws['!cols'] = [
                    {wch: 5},   // NO
                    {wch: 12},  // NPSN
                    {wch: 30},  // NAMA SEKOLAH
                    {wch: 10},  // JENJANG
                    {wch: 15},  // KAB/KOTA
                    {wch: 12},  // NILAI AKHIR
                    {wch: 12},  // PROGRAM PAKET
                    {wch: 10},  // PERINGKAT
                    {wch: 15},  // STATUS
                    {wch: 12},  // TANGGAL SK
                    {wch: 20},  // NOMOR SK
                    {wch: 15},  // TAHUN AKREDITASI
                    {wch: 15},  // TAHUN BERAKHIR
                    {wch: 25}   // UNTUK AKREDITASI
                ];

                // Add worksheet to workbook
                XLSX.utils.book_append_sheet(wb, ws, "Nilai Akreditasi Keseluruhan");

                // Generate filename with current date
                var today = new Date();
                var dateStr = today.getFullYear() + '-' +
                             String(today.getMonth() + 1).padStart(2, '0') + '-' +
                             String(today.getDate()).padStart(2, '0');
                var filename = 'Nilai_Akreditasi_Keseluruhan_' + dateStr + '.xlsx';

                // Save file
                XLSX.writeFile(wb, filename);

                // Show success message
                showAlert('success', 'File Excel berhasil didownload: ' + filename);

            } else {
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat export data');
        },
        complete: function() {
            // Enable tombol export kembali
            $('#btn-export-keseluruhan').prop('disabled', false).html('<i class="fas fa-download"></i> Export Keseluruhan');
        }
    });
}
