<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi parameter
    if (!isset($_GET['npsn']) || empty(trim($_GET['npsn']))) {
        throw new Exception('NPSN harus diisi');
    }
    
    $npsn = $conn->real_escape_string(trim($_GET['npsn']));
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Query lookup NPSN
    $query = "SELECT s.sekolah_id, s.npsn, s.nama_sekolah, s.alamat, s.kecamatan,
                     j.nm_jenjang, k.nm_kota
              FROM sekolah s
              LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
              LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
              WHERE s.npsn = ? AND s.provinsi_id = ? AND s.soft_delete = '1'";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("si", $npsn, $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $data = $result->fetch_assoc();
        
        echo json_encode([
            'success' => true,
            'data' => [
                'sekolah_id' => $data['sekolah_id'],
                'npsn' => $data['npsn'],
                'nama_sekolah' => $data['nama_sekolah'],
                'alamat' => $data['alamat'],
                'kecamatan' => $data['kecamatan'],
                'nm_jenjang' => $data['nm_jenjang'],
                'nm_kota' => $data['nm_kota']
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'NPSN tidak ditemukan atau tidak aktif'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
