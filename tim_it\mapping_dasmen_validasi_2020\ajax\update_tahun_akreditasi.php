<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input
    if (!isset($_POST['nama_tahun']) || empty(trim($_POST['nama_tahun']))) {
        throw new Exception('Tahun akreditasi harus diisi');
    }
    
    $nama_tahun = intval(trim($_POST['nama_tahun']));
    $id_mapping_validasi_tahun = isset($_POST['id_mapping_validasi_tahun']) && !empty($_POST['id_mapping_validasi_tahun'])
                                ? intval($_POST['id_mapping_validasi_tahun'])
                                : null;
    $provinsi_id = $_SESSION['provinsi_id'];

    // Validasi tahun (minimal 4 digit)
    if ($nama_tahun < 1000 || $nama_tahun > 9999) {
        throw new Exception('Tahun akreditasi harus berupa 4 digit angka');
    }
    
    // Begin transaction
    $conn->autocommit(false);
    
    if ($id_mapping_validasi_tahun) {
        // Update existing record
        $query = "UPDATE mapping_validasi_tahun 
                  SET nama_tahun = ? 
                  WHERE id_mapping_validasi_tahun = ? AND provinsi_id = ?";
        
        $stmt = $conn->prepare($query);
        $stmt->bind_param("iii", $nama_tahun, $id_mapping_validasi_tahun, $provinsi_id);
        
        if (!$stmt->execute()) {
            throw new Exception('Gagal mengupdate tahun akreditasi: ' . $conn->error);
        }
        
        $message = 'Tahun akreditasi berhasil diupdate';
        
    } else {
        // Insert new record
        $query = "INSERT INTO mapping_validasi_tahun (nama_tahun, provinsi_id) 
                  VALUES (?, ?)";
        
        $stmt = $conn->prepare($query);
        $stmt->bind_param("ii", $nama_tahun, $provinsi_id);
        
        if (!$stmt->execute()) {
            throw new Exception('Gagal menyimpan tahun akreditasi: ' . $conn->error);
        }
        
        $message = 'Tahun akreditasi berhasil disimpan';
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => [
            'nama_tahun' => $nama_tahun,
            'provinsi_id' => $provinsi_id
        ]
    ]);
    
} catch (Exception $e) {
    // Rollback transaction
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
