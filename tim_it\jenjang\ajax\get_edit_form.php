<?php
/**
 * AJAX handler untuk mendapatkan form edit jenjang dengan data yang sudah ter-populate
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input
    if (!isset($_POST['id_jenjang']) || empty($_POST['id_jenjang'])) {
        throw new Exception('ID jenjang harus diisi');
    }
    
    $id_jenjang = intval($_POST['id_jenjang']);
    
    // Query untuk mendapatkan data jenjang
    $sql = "SELECT * FROM jenjang WHERE id_jenjang = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id_jenjang);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        throw new Exception('Data jenjang tidak ditemukan');
    }
    
    $row = $result->fetch_assoc();
    
    // Generate form HTML dengan data yang sudah ter-populate
    ob_start();
?>
<div class="modal-header bg-success">
    <h5 class="modal-title">
        <i class="fas fa-edit"></i> Edit Data Jenjang
    </h5>
    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<form id="form-edit-jenjang" novalidate>
    <div class="modal-body">
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label for="edit_jenjang_id">Kode Jenjang <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="edit_jenjang_id" name="jenjang_id" 
                           value="<?php echo htmlspecialchars($row['jenjang_id']); ?>" required maxlength="2">
                    <small class="form-text text-muted">Maksimal 2 karakter (contoh: 1, 2, SD, SMP)</small>
                </div>
                
                <div class="form-group">
                    <label for="edit_nm_jenjang">Nama Jenjang <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="edit_nm_jenjang" name="nm_jenjang" 
                           value="<?php echo htmlspecialchars($row['nm_jenjang']); ?>" required maxlength="15">
                    <small class="form-text text-muted">Maksimal 15 karakter</small>
                </div>
            </div>
        </div>
        
        <!-- Hidden fields -->
        <input type="hidden" name="id_jenjang" value="<?php echo $row['id_jenjang']; ?>">
        <input type="hidden" name="original_jenjang_id" value="<?php echo $row['jenjang_id']; ?>">
        <input type="hidden" name="original_nm_jenjang" value="<?php echo $row['nm_jenjang']; ?>">
    </div>
    
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">
            <i class="fas fa-times"></i> Batal
        </button>
        <button type="button" class="btn btn-warning" id="btn-update">
            <i class="fas fa-save"></i> Update Data
        </button>
    </div>
</form>
<?php
    $form_html = ob_get_clean();
    
    // Return response
    echo json_encode([
        'success' => true,
        'html' => $form_html,
        'data' => $row
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
