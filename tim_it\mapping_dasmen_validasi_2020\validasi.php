<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');
?>

// Include header
<?php include '../header.php'; ?>

<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

<!-- Content Wrapper -->
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Mapping Asesor Validasi Dasmen</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../../dashboard.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="#">IASP 2020</a></li>
                        <li class="breadcrumb-item active">Mapping Validasi</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Alert untuk notifikasi -->
            <div id="alert-container"></div>
            
            <!-- Card untuk tabel -->
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-clipboard-check"></i> Data Mapping Asesor Validasi Dasmen
                            </h3>
                        </div>
                        <div class="col-md-6 text-right">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-primary btn-sm" id="btn-input-mapping">
                                    <i class="fas fa-plus"></i> Input Mapping Validasi
                                </button>
                                <button type="button" class="btn btn-success btn-sm" id="btn-export-excel">
                                    <i class="fas fa-file-excel"></i> Export Excel
                                </button>
                                <button type="button" class="btn btn-info btn-sm" id="btn-import-excel">
                                    <i class="fas fa-file-upload"></i> Import Excel
                                </button>
                                <button type="button" class="btn btn-warning btn-sm" id="btn-tahun-akreditasi">
                                    <i class="fas fa-calendar-alt"></i> Tahun Akreditasi
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">

                    <!-- Tabel Data -->
                    <div class="table-responsive">
                        <table id="table-mapping-validasi" class="table table-bordered table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th width="5%">NO <br> &nbsp; </th>
                                    <th width="8%">NPSN <br> &nbsp; </th>
                                    <th width="20%">NAMA <br> SEKOLAH</th>
                                    <th width="8%">JENJANG <br> &nbsp; </th>
                                    <th width="12%">KAB/KOTA <br> &nbsp; </th>
                                    <th width="8%">NIA <br> ASESOR 1</th>
                                    <th width="15%">NAMA <br> ASESOR 1</th>
                                    <th width="8%">NIA <br> ASESOR 2</th>
                                    <th width="15%">NAMA <br> ASESOR 2</th>
                                    <th width="8%">TAHUN <br> VALIDASI</th>
                                    <th width="8%">TAHAP <br> VISITASI</th>
                                    <th width="8%">AKSI <br> &nbsp; </th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data akan dimuat via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Modal Input Mapping Validasi -->
<div class="modal fade" id="modal-input-mapping" tabindex="-1" role="dialog" aria-labelledby="modalInputMappingLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <h5 class="modal-title" id="modalInputMappingLabel">
                    <i class="fas fa-plus"></i> Input Data Mapping Validasi
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-input-mapping">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="npsn_sekolah">NPSN Sekolah <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="npsn_sekolah" name="npsn_sekolah"
                                       placeholder="Masukkan NPSN" required>
                                <small class="form-text text-muted">NPSN akan dicari otomatis untuk mendapatkan data sekolah</small>
                                <input type="hidden" id="sekolah_id" name="sekolah_id">
                            </div>

                            <div class="form-group">
                                <label for="nia_asesor1">NIA Asesor 1 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nia_asesor1" name="nia_asesor1"
                                       placeholder="Masukkan NIA Asesor 1" required>
                                <small class="form-text text-muted">NIA akan dicari otomatis untuk mendapatkan kode asesor</small>
                                <input type="hidden" id="kd_asesor1" name="kd_asesor1">
                            </div>

                            <div class="form-group">
                                <label for="nia_asesor2">NIA Asesor 2 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nia_asesor2" name="nia_asesor2"
                                       placeholder="Masukkan NIA Asesor 2" required>
                                <small class="form-text text-muted">NIA akan dicari otomatis untuk mendapatkan kode asesor</small>
                                <input type="hidden" id="kd_asesor2" name="kd_asesor2">
                            </div>

                            <div class="form-group">
                                <label for="tgl_mulai_validasi">Tanggal Mulai Validasi <small class="text-muted">(opsional)</small></label>
                                <input type="date" class="form-control" id="tgl_mulai_validasi" name="tgl_mulai_validasi">
                            </div>

                            <div class="form-group">
                                <label for="tgl_akhir_validasi">Tanggal Akhir Validasi <small class="text-muted">(opsional)</small></label>
                                <input type="date" class="form-control" id="tgl_akhir_validasi" name="tgl_akhir_validasi">
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="tahun_akreditasi">Tahun Akreditasi <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="tahun_akreditasi" name="tahun_akreditasi"
                                       min="1000" max="9999" placeholder="Contoh: 2024" required>
                            </div>

                            <div class="form-group">
                                <label for="tahap">Tahap Ke <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="tahap" name="tahap"
                                       min="1" placeholder="Contoh: 1" required>
                            </div>

                            <div class="form-group">
                                <label for="no_surat_validasi">Nomor Surat Tugas Validasi <small class="text-muted">(opsional)</small></label>
                                <input type="text" class="form-control" id="no_surat_validasi" name="no_surat_validasi"
                                       placeholder="Masukkan nomor surat">
                            </div>

                            <div class="form-group">
                                <label for="tgl_surat_validasi">Tanggal Surat Tugas Validasi <small class="text-muted">(opsional)</small></label>
                                <input type="date" class="form-control" id="tgl_surat_validasi" name="tgl_surat_validasi">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Simpan Data
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Detail Mapping Validasi -->
<div class="modal fade" id="modal-detail-mapping-validasi" tabindex="-1" role="dialog" aria-labelledby="modalDetailMappingValidasiLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info">
                <h5 class="modal-title text-white" id="modalDetailMappingValidasiLabel">
                    <i class="fas fa-eye"></i> Detail Mapping Validasi Dasmen
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- Kolom 1: Data Sekolah -->
                    <div class="col-md-2">
                        <div class="card h-100">
                            <div class="card-header bg-info text-white text-center">
                                <h6 class="mb-0"><i class="fas fa-school"></i> Data Sekolah</h6>
                            </div>
                            <div class="card-body p-2">
                                <table class="table table-sm table-borderless mb-0" style="font-size: 12px;">
                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                        <td width="40%" class="font-weight-bold">NPSN</td>
                                        <td width="60%" id="detail-npsn">-</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                        <td class="font-weight-bold">Nama Sekolah</td>
                                        <td id="detail-nama-sekolah">-</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                        <td class="font-weight-bold">Jenjang</td>
                                        <td id="detail-jenjang">-</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                        <td class="font-weight-bold">Kab/Kota</td>
                                        <td id="detail-kab-kota">-</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                        <td class="font-weight-bold">Nama Kepsek</td>
                                        <td id="detail-nama-kepsek">-</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                        <td class="font-weight-bold">HP Kepsek</td>
                                        <td id="detail-hp-kepsek">-</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">WA Kepsek</td>
                                        <td id="detail-wa-kepsek">-</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Kolom 2: Data Asesor -->
                    <div class="col-md-3">
                        <div class="card h-100">
                            <div class="card-header bg-info text-white text-center">
                                <h6 class="mb-0"><i class="fas fa-users"></i> Data Asesor</h6>
                            </div>
                            <div class="card-body p-2">
                                <table class="table table-sm table-borderless mb-0" style="font-size: 12px;">
                                    <tr style="border-bottom: 2px solid #007bff;">
                                        <td colspan="2" class="font-weight-bold text-primary">Asesor 1</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                        <td width="30%" class="font-weight-bold">NIA</td>
                                        <td width="70%" id="detail-nia1">-</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                        <td class="font-weight-bold">Nama</td>
                                        <td id="detail-nama-asesor1">-</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                        <td class="font-weight-bold">No HP</td>
                                        <td id="detail-hp-asesor1">-</td>
                                    </tr>
                                    <tr style="border-bottom: 2px solid #dee2e6;">
                                        <td class="font-weight-bold">Kab/Kota</td>
                                        <td id="detail-kota-asesor1">-</td>
                                    </tr>
                                    <tr style="border-bottom: 2px solid #007bff;">
                                        <td colspan="2" class="font-weight-bold text-primary">Asesor 2</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                        <td class="font-weight-bold">NIA</td>
                                        <td id="detail-nia2">-</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                        <td class="font-weight-bold">Nama</td>
                                        <td id="detail-nama-asesor2">-</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                        <td class="font-weight-bold">No HP</td>
                                        <td id="detail-hp-asesor2">-</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">Kab/Kota</td>
                                        <td id="detail-kota-asesor2">-</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Kolom 3: Dokumen Unggahan -->
                    <div class="col-md-2">
                        <div class="card h-100">
                            <div class="card-header bg-info text-white text-center">
                                <h6 class="mb-0"><i class="fas fa-file-upload"></i> Dokumen Unggahan</h6>
                            </div>
                            <div class="card-body p-2">
                                <table class="table table-sm table-borderless mb-0" style="font-size: 12px;">
                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                        <td width="60%" class="font-weight-bold">File Pakta Integritas 1</td>
                                        <td width="40%" id="detail-pakta1">-</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                        <td class="font-weight-bold">File Pakta Integritas 2</td>
                                        <td id="detail-pakta2">-</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                        <td class="font-weight-bold">File Berita Acara Validasi 1</td>
                                        <td id="detail-ba1">-</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">File Berita Acara Validasi 2</td>
                                        <td id="detail-ba2">-</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Kolom 4: Pelaksanaan Kegiatan -->
                    <div class="col-md-3">
                        <div class="card h-100">
                            <div class="card-header bg-info text-white text-center">
                                <h6 class="mb-0"><i class="fas fa-calendar-check"></i> Pelaksanaan Kegiatan</h6>
                            </div>
                            <div class="card-body p-2">
                                <table class="table table-sm table-borderless mb-0" style="font-size: 12px;">
                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                        <td width="50%" class="font-weight-bold">Tanggal Validasi Dimulai</td>
                                        <td width="50%" id="detail-tgl-mulai">-</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                        <td class="font-weight-bold">Tanggal Validasi Berakhir</td>
                                        <td id="detail-tgl-akhir">-</td>
                                    </tr>
                                    <tr style="border-bottom: 1px solid #dee2e6;">
                                        <td class="font-weight-bold">No Surat Tugas Validasi</td>
                                        <td id="detail-no-surat">-</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">Tanggal Surat Tugas Validasi</td>
                                        <td id="detail-tgl-surat">-</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Kolom 5: Aksi -->
                    <div class="col-md-2">
                        <div class="card h-100">
                            <div class="card-header bg-info text-white text-center">
                                <h6 class="mb-0"><i class="fas fa-cogs"></i> Aksi</h6>
                            </div>
                            <div class="card-body p-2">
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-warning btn-sm mb-2" id="btn-edit-tanggal-validasi">
                                        <i class="fas fa-calendar-edit"></i> Edit Tanggal Validasi
                                    </button>
                                    <button type="button" class="btn btn-info btn-sm mb-2" id="btn-edit-asesor-perubahan">
                                        <i class="fas fa-user-edit"></i> Edit Asesor Perubahan
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm mb-2" id="btn-hapus-mapping">
                                        <i class="fas fa-trash"></i> Hapus
                                    </button>
                                    <button type="button" class="btn btn-success btn-sm" id="btn-download-surat-validasi">
                                        <i class="fas fa-download"></i> Download Surat Tugas Validasi
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Tanggal Validasi -->
<div class="modal fade" id="modal-edit-tanggal-validasi" tabindex="-1" role="dialog" aria-labelledby="modalEditTanggalValidasiLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header bg-warning">
                <h5 class="modal-title text-white" id="modalEditTanggalValidasiLabel">
                    <i class="fas fa-calendar-edit"></i> Edit Tanggal Validasi
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-edit-tanggal-validasi">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="edit_tgl_mulai_validasi">Tanggal Mulai Validasi <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="edit_tgl_mulai_validasi" name="tgl_mulai_validasi" required>
                        <small class="form-text text-muted">Pilih tanggal mulai validasi</small>
                    </div>

                    <div class="form-group">
                        <label for="edit_tgl_akhir_validasi">Tanggal Akhir Validasi <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="edit_tgl_akhir_validasi" name="tgl_akhir_validasi" required>
                        <small class="form-text text-muted">Pilih tanggal akhir validasi</small>
                    </div>

                    <!-- Hidden field untuk ID mapping validasi -->
                    <input type="hidden" id="edit_id_mapping_validasi" name="id_mapping_validasi">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-warning btn-sm">
                        <i class="fas fa-save"></i> Update Perubahan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Tahun Akreditasi -->
<div class="modal fade" id="modal-tahun-akreditasi" tabindex="-1" role="dialog" aria-labelledby="modalTahunAkreditasiLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header bg-warning">
                <h5 class="modal-title" id="modalTahunAkreditasiLabel">
                    <i class="fas fa-calendar-alt"></i> Edit Tahun Akreditasi
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-tahun-akreditasi">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="nama_tahun">Tahun Akreditasi <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="nama_tahun" name="nama_tahun"
                               min="1000" max="9999" placeholder="Contoh: 2024" required>
                        <small class="form-text text-muted">Masukkan tahun akreditasi (4 digit angka)</small>
                    </div>
                    <input type="hidden" id="id_mapping_validasi_tahun" name="id_mapping_validasi_tahun">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save"></i> Simpan Perubahan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Edit Asesor Perubahan -->
<div class="modal fade" id="modal-edit-asesor-perubahan" tabindex="-1" role="dialog" aria-labelledby="modalEditAsesorPerubahanLabel" aria-hidden="true">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info">
                <h5 class="modal-title text-white" id="modalEditAsesorPerubahanLabel">
                    <i class="fas fa-user-edit"></i> Edit Asesor Perubahan
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-edit-asesor-perubahan">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Petunjuk:</strong> Masukkan NIA asesor baru. Sistem akan otomatis mencari data asesor berdasarkan NIA yang diinput.
                    </div>

                    <div class="form-group">
                        <label for="edit_nia_asesor1">NIA Asesor 1 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_nia_asesor1" name="nia_asesor1"
                               placeholder="Masukkan NIA Asesor 1" required maxlength="20">
                        <small class="form-text text-muted">NIA akan dicari otomatis untuk mendapatkan data asesor</small>
                        <input type="hidden" id="edit_kd_asesor1" name="kd_asesor1">
                        <!-- Display info asesor 1 -->
                        <div id="info-asesor1" class="mt-2" style="display: none;">
                            <div class="card card-outline card-success">
                                <div class="card-body p-2">
                                    <small class="text-success">
                                        <i class="fas fa-check-circle"></i> <strong>Asesor 1 ditemukan:</strong><br>
                                        <strong>Nama:</strong> <span id="info-nama-asesor1">-</span><br>
                                        <strong>Kota:</strong> <span id="info-kota-asesor1">-</span>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit_nia_asesor2">NIA Asesor 2 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_nia_asesor2" name="nia_asesor2"
                               placeholder="Masukkan NIA Asesor 2" required maxlength="20">
                        <small class="form-text text-muted">NIA akan dicari otomatis untuk mendapatkan data asesor</small>
                        <input type="hidden" id="edit_kd_asesor2" name="kd_asesor2">
                        <!-- Display info asesor 2 -->
                        <div id="info-asesor2" class="mt-2" style="display: none;">
                            <div class="card card-outline card-success">
                                <div class="card-body p-2">
                                    <small class="text-success">
                                        <i class="fas fa-check-circle"></i> <strong>Asesor 2 ditemukan:</strong><br>
                                        <strong>Nama:</strong> <span id="info-nama-asesor2">-</span><br>
                                        <strong>Kota:</strong> <span id="info-kota-asesor2">-</span>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden field untuk ID mapping validasi -->
                    <input type="hidden" id="edit_id_mapping_validasi_asesor" name="id_mapping_validasi">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-info btn-sm">
                        <i class="fas fa-save"></i> Update Perubahan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Konfirmasi Hapus Mapping Validasi -->
<div class="modal fade" id="modal-konfirmasi-hapus-mapping" tabindex="-1" role="dialog" aria-labelledby="modalKonfirmasiHapusMappingLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger">
                <h5 class="modal-title text-white" id="modalKonfirmasiHapusMappingLabel">
                    <i class="fas fa-exclamation-triangle"></i> Konfirmasi Hapus Data Mapping Validasi
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <div class="mb-3">
                    <i class="fas fa-trash-alt text-danger" style="font-size: 48px;"></i>
                </div>
                <h6 class="mb-3">Apakah Anda yakin ingin menghapus data mapping validasi ini?</h6>
                <div class="alert alert-warning">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Sekolah:</strong><br>
                            <span id="hapus-nama-sekolah">-</span>
                        </div>
                        <div class="col-md-6">
                            <strong>NPSN:</strong><br>
                            <span id="hapus-npsn">-</span>
                        </div>
                    </div>
                    <hr class="my-2">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Asesor 1:</strong><br>
                            <span id="hapus-asesor1">-</span>
                        </div>
                        <div class="col-md-6">
                            <strong>Asesor 2:</strong><br>
                            <span id="hapus-asesor2">-</span>
                        </div>
                    </div>
                    <hr class="my-2">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Tahun Akreditasi:</strong><br>
                            <span id="hapus-tahun">-</span>
                        </div>
                        <div class="col-md-6">
                            <strong>Tahap:</strong><br>
                            <span id="hapus-tahap">-</span>
                        </div>
                    </div>
                </div>
                <p class="text-muted mb-0">
                    <i class="fas fa-exclamation-circle text-danger"></i>
                    <strong>PERINGATAN:</strong> Data akan dihapus permanen dan tidak dapat dikembalikan!
                </p>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Batal
                </button>
                <button type="button" class="btn btn-danger" id="btn-konfirmasi-hapus-mapping">
                    <i class="fas fa-trash"></i> Ya, Hapus Data
                </button>
            </div>
            <!-- Hidden field untuk menyimpan ID mapping yang akan dihapus -->
            <input type="hidden" id="id-mapping-akan-dihapus">
        </div>
    </div>
</div>

<!-- Include footer -->
<?php include '../footer.php'; ?>

<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap4.min.css">

<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap4.min.js"></script>

<!-- Custom JS -->
<script src="js/validasi.js"></script>

<style>
.btn-group .btn + .btn {
    margin-left: 5px;
}

.card-header .row {
    margin: 0;
}

.card-header .col-md-6 {
    padding: 0;
}

/* File preview badge styles */
.file-preview-link {
    transition: all 0.3s ease;
}

.file-preview-link:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    opacity: 0.9;
}

.badge {
    font-size: 11px;
    padding: 4px 8px;
}

.badge i {
    margin-right: 3px;
}

/* Nested modal z-index management */
.modal {
    z-index: 1050;
}

.modal.show {
    z-index: 1050;
}

.modal-backdrop {
    z-index: 1040;
}

/* Ensure edit modal appears above detail modal */
#modal-edit-tanggal-validasi {
    z-index: 1060 !important;
}

#modal-edit-tanggal-validasi .modal-backdrop {
    z-index: 1055 !important;
}

/* Ensure edit asesor modal appears above detail modal */
#modal-edit-asesor-perubahan {
    z-index: 1080 !important;
}

#modal-edit-asesor-perubahan .modal-backdrop {
    z-index: 1075 !important;
}

/* Ensure konfirmasi hapus modal appears above detail modal */
#modal-konfirmasi-hapus-mapping {
    z-index: 1070 !important;
}

#modal-konfirmasi-hapus-mapping .modal-backdrop {
    z-index: 1065 !important;
}

/* Modal Konfirmasi Hapus Styling */
#modal-konfirmasi-hapus-mapping .modal-body {
    padding: 2rem 1.5rem;
}

#modal-konfirmasi-hapus-mapping .fas.fa-trash-alt {
    animation: shake 0.5s ease-in-out;
}

#modal-konfirmasi-hapus-mapping .alert-warning {
    border-left: 4px solid #ffc107;
    background-color: #fff3cd;
    border-color: #ffeaa7;
    font-size: 14px;
    margin: 1rem 0;
}

#modal-konfirmasi-hapus-mapping .modal-footer {
    border-top: none;
    padding: 1rem 2rem 2rem;
}

/* Modal Edit Asesor Styling */
#modal-edit-asesor-perubahan .alert-info {
    border-left: 4px solid #17a2b8;
    background-color: #d1ecf1;
    border-color: #bee5eb;
    font-size: 13px;
}

#modal-edit-asesor-perubahan .card-outline.card-success {
    border-top: 3px solid #28a745;
}

#modal-edit-asesor-perubahan .card-body {
    background-color: #f8fff9;
}

#modal-edit-asesor-perubahan .form-control:focus {
    border-color: #17a2b8;
    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
}

/* Animation for updated fields */
.text-success {
    transition: color 0.3s ease;
}

@media (max-width: 768px) {
    .card-header .col-md-6.text-right {
        text-align: left !important;
        margin-top: 10px;
    }

    .btn-group {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
    }

    .btn-group .btn {
        margin-left: 0 !important;
        margin-bottom: 5px;
    }
}
</style>


