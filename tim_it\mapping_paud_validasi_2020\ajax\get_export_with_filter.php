<?php
// Export dengan filter tahun akreditasi (optional)
ob_clean();
error_reporting(0);
ini_set('display_errors', 0);

require '../../../koneksi.php';
require '../../../check_session.php';

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Validasi level akses
try {
    requireLevel('Staff IT');
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'A<PERSON><PERSON> ditolak']);
    exit;
}

try {
    // Validasi koneksi database
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Validasi session provinsi_id
    if (!isset($_SESSION['provinsi_id'])) {
        throw new Exception("Session provinsi_id not found");
    }
    
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Cek apakah ada filter tahun akreditasi
    $tahun_filter_query = "SELECT nama_tahun FROM mapping_paud_validasi_tahun WHERE provinsi_id = ? LIMIT 1";
    $tahun_stmt = $conn->prepare($tahun_filter_query);
    $tahun_stmt->bind_param("i", $provinsi_id_session);
    $tahun_stmt->execute();
    $tahun_result = $tahun_stmt->get_result();
    
    if ($tahun_result->num_rows > 0) {
        // Ada filter tahun, gunakan query dengan filter
        $tahun_data = $tahun_result->fetch_assoc();
        $tahun_akreditasi = $tahun_data['nama_tahun'];
        
        $query = "SELECT 
                    mp.id_mapping,
                    mp.sekolah_id,
                    mp.kd_asesor1,
                    mp.kd_asesor2,
                    mp.tahap,
                    mp.tahun_akreditasi,
                    mp.file_penjelasan_hasil_akreditasi,
                    mp.nama_asesor_kpa,
                    mp.catatan_penilaian_asesor_kpa,
                    mp.nama_asesor_Validasi_a,
                    mp.catatan_penilaian_asesor_Validasi_a,
                    mp.nama_asesor_Validasi_b,
                    mp.catatan_penilaian_asesor_Validasi_b,
                    mp.nama_validator,
                    mp.nilai_validasi,
                    mp.catatan_penilaian_validator,
                    mp.pha,
                    mp.provinsi_id,
                    
                    -- Data Sekolah
                    s.nama_sekolah,
                    s.npsn,
                    s.jenjang_id,
                    s.rumpun,
                    s.kota_id,
                    
                    -- Data Jenjang
                    j.nm_jenjang,
                    
                    -- Data Kab/Kota Sekolah
                    k.nm_kota as nm_kota_sekolah,
                    
                    -- Data Asesor 1 (Validator)
                    a1.nia1,
                    a1.nm_asesor1,
                    a1.no_hp as no_hp_asesor1,
                    a1.kota_id1,
                    
                    -- Data Asesor 2 (Verifikator)
                    a2.nia2,
                    a2.nm_asesor2,
                    a2.no_hp as no_hp_asesor2,
                    a2.kota_id2,
                    
                    -- Data Kab/Kota Asesor 1 (Validator)
                    k1.nm_kota as nm_kota_asesor1,
                    
                    -- Data Kab/Kota Asesor 2 (Verifikator)
                    k2.nm_kota as nm_kota_asesor2
                    
                  FROM mapping_paud_validasi mp
                  LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
                  LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                  LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
                  LEFT JOIN asesor_1 a1 ON mp.kd_asesor1 = a1.kd_asesor1
                  LEFT JOIN asesor_2 a2 ON mp.kd_asesor2 = a2.kd_asesor2
                  LEFT JOIN kab_kota k1 ON a1.kota_id1 = k1.kota_id
                  LEFT JOIN kab_kota k2 ON a2.kota_id2 = k2.kota_id
                  WHERE mp.provinsi_id = ?
                    AND mp.tahun_akreditasi = ?
                    AND s.rumpun = 'paud'
                    AND s.soft_delete = '1'
                  ORDER BY s.nama_sekolah ASC";
        
        $stmt = $conn->prepare($query);
        $stmt->bind_param("is", $provinsi_id_session, $tahun_akreditasi);
        
    } else {
        // Tidak ada filter tahun, ambil semua data
        $query = "SELECT 
                    mp.id_mapping,
                    mp.sekolah_id,
                    mp.kd_asesor1,
                    mp.kd_asesor2,
                    mp.tahap,
                    mp.tahun_akreditasi,
                    mp.file_penjelasan_hasil_akreditasi,
                    mp.nama_asesor_kpa,
                    mp.catatan_penilaian_asesor_kpa,
                    mp.nama_asesor_Validasi_a,
                    mp.catatan_penilaian_asesor_Validasi_a,
                    mp.nama_asesor_Validasi_b,
                    mp.catatan_penilaian_asesor_Validasi_b,
                    mp.nama_validator,
                    mp.nilai_validasi,
                    mp.catatan_penilaian_validator,
                    mp.pha,
                    mp.provinsi_id,
                    
                    -- Data Sekolah
                    s.nama_sekolah,
                    s.npsn,
                    s.jenjang_id,
                    s.rumpun,
                    s.kota_id,
                    
                    -- Data Jenjang
                    j.nm_jenjang,
                    
                    -- Data Kab/Kota Sekolah
                    k.nm_kota as nm_kota_sekolah,
                    
                    -- Data Asesor 1 (Validator)
                    a1.nia1,
                    a1.nm_asesor1,
                    a1.no_hp as no_hp_asesor1,
                    a1.kota_id1,
                    
                    -- Data Asesor 2 (Verifikator)
                    a2.nia2,
                    a2.nm_asesor2,
                    a2.no_hp as no_hp_asesor2,
                    a2.kota_id2,
                    
                    -- Data Kab/Kota Asesor 1 (Validator)
                    k1.nm_kota as nm_kota_asesor1,
                    
                    -- Data Kab/Kota Asesor 2 (Verifikator)
                    k2.nm_kota as nm_kota_asesor2
                    
                  FROM mapping_paud_validasi mp
                  LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
                  LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                  LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
                  LEFT JOIN asesor_1 a1 ON mp.kd_asesor1 = a1.kd_asesor1
                  LEFT JOIN asesor_2 a2 ON mp.kd_asesor2 = a2.kd_asesor2
                  LEFT JOIN kab_kota k1 ON a1.kota_id1 = k1.kota_id
                  LEFT JOIN kab_kota k2 ON a2.kota_id2 = k2.kota_id
                  WHERE mp.provinsi_id = ?
                    AND s.rumpun = 'paud'
                    AND s.soft_delete = '1'
                  ORDER BY s.nama_sekolah ASC";
        
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $provinsi_id_session);
    }
    
    if (!$stmt->execute()) {
        throw new Exception("Execute failed: " . $stmt->error);
    }
    
    $result = $stmt->get_result();
    if (!$result) {
        throw new Exception("Get result failed: " . $stmt->error);
    }
    
    // Siapkan data untuk response
    $data = [];
    while ($row = $result->fetch_assoc()) {
        // Tentukan status file
        $file_status = 'Belum Unggah';
        if (!empty($row['file_penjelasan_hasil_akreditasi']) && trim($row['file_penjelasan_hasil_akreditasi']) !== '') {
            $file_status = 'Sudah Unggah';
        }
        
        $data[] = [
            'id_mapping' => $row['id_mapping'],
            'npsn' => $row['npsn'],
            'nama_sekolah' => $row['nama_sekolah'],
            'nm_jenjang' => $row['nm_jenjang'],
            'nm_kota_sekolah' => $row['nm_kota_sekolah'],
            'nia1' => $row['nia1'],
            'nm_asesor1' => $row['nm_asesor1'],
            'nm_kota_asesor1' => $row['nm_kota_asesor1'],
            'nia2' => $row['nia2'],
            'nm_asesor2' => $row['nm_asesor2'],
            'nm_kota_asesor2' => $row['nm_kota_asesor2'],
            'tahun_akreditasi' => $row['tahun_akreditasi'],
            'tahap' => $row['tahap'],
            'file_status' => $file_status,
            'nama_asesor_kpa' => $row['nama_asesor_kpa'],
            'catatan_penilaian_asesor_kpa' => $row['catatan_penilaian_asesor_kpa'],
            'nama_asesor_Validasi_a' => $row['nama_asesor_Validasi_a'],
            'catatan_penilaian_asesor_Validasi_a' => $row['catatan_penilaian_asesor_Validasi_a'],
            'nama_asesor_Validasi_b' => $row['nama_asesor_Validasi_b'],
            'catatan_penilaian_asesor_Validasi_b' => $row['catatan_penilaian_asesor_Validasi_b'],
            'nama_validator' => $row['nama_validator'],
            'nilai_validasi' => $row['nilai_validasi'],
            'catatan_penilaian_validator' => $row['catatan_penilaian_validator'],
            'pha' => $row['pha']
        ];
    }
    
    // Log export activity
    error_log("Export Mapping Validasi PAUD - User: " . ($_SESSION['nm_user'] ?? 'Unknown') . ", Records: " . count($data));
    
    echo json_encode([
        'success' => true,
        'message' => 'Data berhasil diambil untuk export',
        'data' => $data,
        'total_records' => count($data)
    ]);
    
} catch (Exception $e) {
    error_log("Export Mapping Validasi PAUD Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan saat mengambil data untuk export',
        'data' => [],
        'total_records' => 0
    ]);
}

if (isset($conn)) {
    $conn->close();
}
?>
