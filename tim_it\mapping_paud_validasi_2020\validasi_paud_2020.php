<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');
?>

<!-- Include header -->
<?php include '../header.php'; ?>

<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap4.min.css">

<!-- Content Wrapper -->
<div class="content-wrapper">
    <!-- Content Header -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><i class="fas fa-clipboard-check"></i> Mapping Asesor Validasi PAUD</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="#">IASP 2020</a></li>
                        <li class="breadcrumb-item"><a href="#">Mapping PAUD</a></li>
                        <li class="breadcrumb-item active">Validasi</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-list"></i> Data Mapping Asesor Validasi PAUD
                            </h3>
                            <div class="card-tools">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-success" id="btn-input-mapping-validasi">
                                        <i class="fas fa-plus"></i> Input Data Mapping
                                    </button>
                                    <button type="button" class="btn btn-info" id="btn-export-excel">
                                        <i class="fas fa-file-excel"></i> Export Excel
                                    </button>
                                    <button type="button" class="btn btn-warning" id="btn-import-excel">
                                        <i class="fas fa-file-upload"></i> Import Excel
                                    </button>
                                    <button type="button" class="btn btn-secondary" id="btn-tahun-akreditasi">
                                        <i class="fas fa-calendar-alt"></i> Tahun Akreditasi
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="table-mapping-validasi" class="table table-bordered table-striped table-hover" width="100%">
                                    <thead>
                                        <tr>
                                            <th>NO <br> &nbsp; </th>
                                            <th>NPSN <br> &nbsp; </th>
                                            <th>NAMA <br> SEKOLAH</th>
                                            <th>JENJANG <br> &nbsp; </th>
                                            <th>KAB/KOTA <br> &nbsp; </th>
                                            <th>NIA <br> VALIDATOR</th>
                                            <th>NAMA <br> VALIDATOR</th>
                                            <th>NIA <br> VERIFIKATOR</th>
                                            <th>NAMA <br> VERIFIKATOR</th>
                                            <th>TAHUN <br> AKREDITASI</th>
                                            <th>TAHAP <br> VALIDASI</th>
                                            <th>AKSI <br> &nbsp; </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data akan dimuat via AJAX -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Modal Input Data Mapping -->
<div class="modal fade" id="modalInputMapping" tabindex="-1" role="dialog" aria-labelledby="modalInputMappingLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success">
                <h5 class="modal-title text-white" id="modalInputMappingLabel">
                    <i class="fas fa-plus"></i> Input Data Mapping Validasi PAUD
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="formInputMapping">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="npsn_sekolah">NPSN Sekolah <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="npsn_sekolah" name="npsn_sekolah" required>
                                <div class="invalid-feedback" id="npsn_feedback"></div>
                                <input type="hidden" id="sekolah_id" name="sekolah_id">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="nia_validator">NIA Validator <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nia_validator" name="nia_validator" required>
                                <div class="invalid-feedback" id="validator_feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="nia_verifikator">NIA Verifikator <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nia_verifikator" name="nia_verifikator" required>
                                <div class="invalid-feedback" id="verifikator_feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="tahun_akreditasi">Tahun Akreditasi <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="tahun_akreditasi" name="tahun_akreditasi" required maxlength="4" pattern="[0-9]{4}">
                                <div class="invalid-feedback" id="tahun_feedback"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="tahap">Tahap Ke <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="tahap" name="tahap" required min="1">
                                <div class="invalid-feedback" id="tahap_feedback"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-success" id="btnSimpanMapping">
                        <i class="fas fa-save"></i> Simpan Mapping
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Filter Tahun Akreditasi -->
<div class="modal fade" id="modal-tahun-akreditasi" tabindex="-1" role="dialog" aria-labelledby="modalTahunAkreditasiLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header bg-secondary text-white">
                <h5 class="modal-title" id="modalTahunAkreditasiLabel">
                    <i class="fas fa-calendar-alt"></i> Filter Tahun Akreditasi
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-tahun-akreditasi">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="nama_tahun">Tahun Akreditasi Aktif</label>
                        <input type="text" class="form-control" id="nama_tahun" name="nama_tahun" required placeholder="Contoh: 2024" maxlength="4" pattern="[0-9]{4}">
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle text-info"></i>
                            Tahun yang akan digunakan untuk filter data mapping validasi PAUD
                        </small>
                    </div>

                    <!-- Info Current Year -->
                    <div class="alert alert-info" id="info-current-year" style="display: none;">
                        <small>
                            <i class="fas fa-calendar"></i> <strong>Tahun saat ini:</strong>
                            <span id="current-year-display">-</span>
                        </small>
                    </div>

                    <!-- Hidden field untuk provinsi_id -->
                    <input type="hidden" name="provinsi_id" value="<?php echo $_SESSION['provinsi_id']; ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-primary btn-sm">
                        <i class="fas fa-save"></i> Update Filter
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Detail Mapping Validasi Paud -->
<div class="modal fade" id="modal-detail-mapping-validasi" tabindex="-1" role="dialog" aria-labelledby="modal-detail-mapping-validasi-label" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="modal-detail-mapping-validasi-label">
                    <i class="fas fa-eye"></i> Detail Mapping Validasi Paud
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- Kolom 1: Data Sekolah -->
                    <div class="col-md-3 mb-3">
                        <div class="card h-100">
                            <div class="card-header bg-info text-white text-center">
                                <h6 class="mb-0"><i class="fas fa-school"></i><br>DATA SEKOLAH</h6>
                            </div>
                            <div class="card-body p-0">
                                <table class="table table-sm table-bordered mb-0">
                                    <tr>
                                        <td class="font-weight-bold">NPSN</td>
                                        <td id="detail-validasi-npsn">-</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">Nama Sekolah</td>
                                        <td id="detail-validasi-nama-sekolah">-</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">Jenjang</td>
                                        <td id="detail-validasi-jenjang">-</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">Kab/Kota</td>
                                        <td id="detail-validasi-kab-kota">-</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">Nama Kepala Sekolah</td>
                                        <td id="detail-validasi-nama-kepsek">-</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">HP Kepala Sekolah</td>
                                        <td id="detail-validasi-hp-kepsek">-</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">No WA Kepala Sekolah</td>
                                        <td id="detail-validasi-wa-kepsek">-</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Kolom 2: Data Validator dan Verifikator -->
                    <div class="col-md-3 mb-3">
                        <div class="card h-100">
                            <div class="card-header bg-info text-white text-center">
                                <h6 class="mb-0"><i class="fas fa-users"></i><br>DATA VALIDATOR DAN VERIFIKATOR</h6>
                            </div>
                            <div class="card-body p-0">
                                <table class="table table-sm table-bordered mb-0">
                                    <tr class="bg-light">
                                        <td class="font-weight-bold text-center" colspan="2">Validator</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">NIA</td>
                                        <td id="detail-validasi-nia-validator">-</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">Nama</td>
                                        <td id="detail-validasi-nama-validator">-</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">No HP</td>
                                        <td id="detail-validasi-hp-validator">-</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">Kab/Kota</td>
                                        <td id="detail-validasi-kota-validator">-</td>
                                    </tr>
                                    <tr class="bg-light">
                                        <td class="font-weight-bold text-center" colspan="2">Verifikator</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">NIA</td>
                                        <td id="detail-validasi-nia-verifikator">-</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">Nama</td>
                                        <td id="detail-validasi-nama-verifikator">-</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">No HP</td>
                                        <td id="detail-validasi-hp-verifikator">-</td>
                                    </tr>
                                    <tr>
                                        <td class="font-weight-bold">Kab/Kota</td>
                                        <td id="detail-validasi-kota-verifikator">-</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Kolom 3: Dokumen Unggahan -->
                    <div class="col-md-3 mb-3">
                        <div class="card h-100">
                            <div class="card-header bg-info text-white text-center">
                                <h6 class="mb-0"><i class="fas fa-file-upload"></i><br>DOKUMEN UNGGAHAN</h6>
                            </div>
                            <div class="card-body p-0">
                                <table class="table table-sm table-bordered mb-0">
                                    <tr>
                                        <td class="font-weight-bold">File Penjelasan Hasil Akreditasi</td>
                                        <td id="detail-validasi-file-penjelasan">-</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Kolom 4: Aksi -->
                    <div class="col-md-3 mb-3">
                        <div class="card h-100">
                            <div class="card-header bg-info text-white text-center">
                                <h6 class="mb-0"><i class="fas fa-cogs"></i><br>AKSI</h6>
                            </div>
                            <div class="card-body text-center">
                                <button type="button" class="btn btn-warning btn-sm btn-block mb-2" id="btn-edit-validator-verifikator">
                                    <i class="fas fa-user-edit"></i><br>Edit Validator/Verifikator Perubahan
                                </button>
                                <button type="button" class="btn btn-danger btn-sm btn-block" id="btn-hapus-mapping-validasi">
                                    <i class="fas fa-trash"></i><br>Hapus
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom styling untuk modul mapping validasi - Based on Dasmen module */
.content-wrapper {
    background-color: #f4f4f4;
}

.card {
    box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
}

.card-header {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    border-bottom: none;
}

.card-title {
    font-weight: 600;
    margin-bottom: 0;
}

/* Table styling - from Dasmen module */
.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    font-size: 12px;
}

.table td {
    vertical-align: middle;
    font-size: 12px;
}

.btn-action {
    margin: 0 2px;
}

.card-tools .btn-group .btn {
    margin-left: 5px;
}

.card-tools .btn-group .btn:first-child {
    margin-left: 0;
}

/* Responsive table adjustments */
@media (max-width: 768px) {
    .table th, .table td {
        font-size: 10px;
        padding: 4px;
    }

    .card-tools .btn-group {
        flex-direction: column;
    }

    .card-tools .btn-group .btn {
        margin: 2px 0;
        font-size: 12px;
    }
}

/* Custom styling for full text display */
.table td {
    word-wrap: break-word;
    white-space: normal;
}

/* Status badges */
.badge-tahap {
    font-size: 11px;
    padding: 4px 8px;
}

/* Button styling */
.btn-group .btn {
    border-radius: 4px;
}

.btn-group .btn + .btn {
    margin-left: 5px;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,.075);
}

#table-mapping-validasi {
    width: 100% !important;
}

.btn-detail-validasi {
    transition: all 0.2s ease-in-out;
}

.btn-detail-validasi:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Detail Modal Styles - Implemented */
#modal-detail-mapping-validasi .card-header {
    background-color: #17a2b8 !important;
    color: white !important;
    font-weight: 600;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#modal-detail-mapping-validasi .card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

#modal-detail-mapping-validasi .h-100 {
    height: 100% !important;
}

#modal-detail-mapping-validasi .table-bordered td {
    border: 1px solid #dee2e6;
    padding: 6px 8px;
    vertical-align: middle;
    font-size: 0.85em;
    word-wrap: break-word;
}

#modal-detail-mapping-validasi .badge {
    font-size: 0.75em;
    padding: 4px 8px;
    border-radius: 4px;
    white-space: nowrap;
}

#modal-detail-mapping-validasi .badge-success {
    background-color: #28a745 !important;
    color: white !important;
}

#modal-detail-mapping-validasi .badge-danger {
    background-color: #dc3545 !important;
    color: white !important;
}

#modal-detail-mapping-validasi .btn-sm {
    font-size: 0.75em;
    padding: 6px 8px;
    line-height: 1.2;
    margin-bottom: 6px;
}

#modal-detail-mapping-validasi .btn-block {
    width: 100%;
}

/* Responsive adjustments for 4 columns */
@media (max-width: 1200px) {
    #modal-detail-mapping-validasi .modal-dialog {
        max-width: 95%;
    }

    #modal-detail-mapping-validasi .table-bordered td {
        font-size: 0.8em;
        padding: 4px 6px;
    }

    #modal-detail-mapping-validasi .btn-sm {
        font-size: 0.7em;
        padding: 4px 6px;
    }
}

/* Mobile responsive - stack vertically */
@media (max-width: 768px) {
    #modal-detail-mapping-validasi .col-md-3 {
        margin-bottom: 1rem;
    }

    #modal-detail-mapping-validasi .table-bordered td {
        font-size: 0.75em;
        padding: 3px 5px;
    }
}

/* Ensure equal height columns */
#modal-detail-mapping-validasi .row {
    display: flex;
    flex-wrap: wrap;
}

#modal-detail-mapping-validasi .col-md-3 {
    display: flex;
    flex-direction: column;
}

/* Clickable file badge styling */
#modal-detail-mapping-validasi .clickable-file {
    transition: all 0.2s ease;
    text-decoration: none;
}

#modal-detail-mapping-validasi .clickable-file:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    text-decoration: none;
}

#modal-detail-mapping-validasi .clickable-file:active {
    transform: scale(0.98);
}

/* Modal Input Mapping Styles */
#modalInputMapping .modal-header {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
}

#modalInputMapping .form-control.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.88 1.88 3.75-3.75.94.94-4.69 4.69z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

#modalInputMapping .form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

#modalInputMapping .valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #28a745;
}

#modalInputMapping .invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

#modalInputMapping .form-group label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

#modalInputMapping .text-danger {
    color: #dc3545 !important;
}

#modalInputMapping .btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    transition: all 0.2s ease-in-out;
}

#modalInputMapping .btn-success:hover {
    background: linear-gradient(135deg, #218838, #1e7e34);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

#modalInputMapping .btn-secondary {
    transition: all 0.2s ease-in-out;
}

#modalInputMapping .btn-secondary:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Modal Tahun Akreditasi Styles */
#modal-tahun-akreditasi .modal-header {
    background: linear-gradient(135deg, #6c757d, #5a6268) !important;
}

#modal-tahun-akreditasi .form-control:focus {
    border-color: #6c757d;
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
}

#modal-tahun-akreditasi .btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    transition: all 0.2s ease-in-out;
}

#modal-tahun-akreditasi .btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

#modal-tahun-akreditasi .alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
    border-radius: 0.375rem;
}

#modal-tahun-akreditasi .form-text {
    font-size: 0.8rem;
}

#modal-tahun-akreditasi .modal-dialog {
    max-width: 400px;
}
</style>



<!-- Include footer -->
<?php include '../footer.php'; ?>

<!-- DataTables & plugins -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap4.min.js"></script>

<!-- Custom JS -->
<script src="js/mapping_validasi.js"></script>
