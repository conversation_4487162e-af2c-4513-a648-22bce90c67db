Berikut ini adalah struktur tabel "mapping_paud_kpa" :
id_mapping int(11)
sekolah_id int(11)
kd_asesor varchar(25)
tgl_penetapan_kpa date
tahap int(11)
tahun_akreditasi varchar(4)
file_laporan_hasil_kpa varchar(50)
tgl_file_hasil_kpa date
jam_file_hasil_kpa time
provinsi_id	int(11)

Berikut ini struktur tabel "sekolah" :
sekolah_id int(11)
nama_sekolah varchar(100)
npsn varchar(30)
jenjang_id int(1)
rumpun varchar(20)
alamat text
tipe_sekolah_id int(1)
status_sekolah_id int(1)
provinsi_id int(2)
kota_id int(6)
desa_kel<PERSON>han varchar(200)
kecamatan varchar(100)
nama_kepsek varchar(100)
no_hp_kepsek varchar(50)
no_wa_kepsek varchar(50)
nama_operator varchar(50)
no_hp_operator varchar(20)
no_wa_operator varchar(20)
email varchar(100)
nama_ya<PERSON>an varchar(200)
no_akte varchar(50)
tahun_berdiri varchar(4)
status_keaktifan_id varchar(1)
soft_delete rchar(1)

Berikut ini adalah struktur tabel "jenjang" :
id_jenjang int(11)
jenjang_id varchar(2)
nm_jenjang varchar(15)

Berikut ini struktur tabel "kab_kota" :
id_kota	int(11)	
kota_id	varchar(10)
nm_kota	varchar(50)
provinsi_id	int(11)
kd_user	varchar(25)

Berikut ini struktur tabel "asesor" :
id_asesor	int(11)	
kd_asesor	varchar(25)
nia	varchar(20)
nm_asesor	varchar(100)
ktp	varchar(20)
unit_kerja	varchar(300)
kota_id	varchar(10)
provinsi_id	int(11)
no_sertifikat	varchar(30)
no_hp	varchar(50)
no_wa	varchar(50)
tempat_lahir	varchar(30)
tgl_lahir date
jabatan	varchar(50)
jabatan_struktural	varchar(20)
pendidikan	varchar(15)
jenjang_id	int(11)
rumpun	varchar(7)
grade	varchar(1)
jk	varchar(10)
alamat_kantor	varchar(300)
alamat_rumah	varchar(300)
email	varchar(50)
thn_terbit_sertifikat	date
kegiatan	varchar(50)
status_keaktifan_id	varchar(1)
sebab	text
kd_user	varchar(50)
soft_delete	varchar(1)
file_sertifikat	varchar(150)

Berikut ini struktur tabel "mapping_paud_kpa_tahun" :
id_mapping_tahun	int(11)
nama_tahun	int(4)
provinsi_id	int(11)


buatlah modul "Mapping Asesor KPA Paud" pada direktori tim_it/mapping_kpa_paud_2020/mapping_kpa.php, juga tersedia sub direktori ajax dan js, tabel header yang digunakan adalah :
NO,
NPSN (sekolah.npsn),
NAMA SEKOLAH (sekolah.nama_sekolah),
JENJANG (jenjang.nm_jenjang),
KAB/KOTA (kab_kota.nm_kota),
NIA ASESOR (asesor.nia),
NAMA ASESOR (asesor.nm_asesor),
TAHUN AKREDITASI (mapping_paud_kpa.tahun_akreditasi),
TAHAP VISITASI (mapping_paud_kpa.tahap),
AKSI.
Untuk kolom AKSI tampilkan tombol ikon detail (tombolnya saja dulu), sampai disini apakah anda sudah mengerti, ataukah ada yang perlu ditanyakan?

oh iya lupa, tambahkan
<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');
?>

// Include header
<?php include '../header.php'; ?>


<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

terakhir di paling bawah <!-- Include footer -->
<?php include '../footer.php'; ?>

tambahkan juga tombol "Input Mapping kpa" dan tombol "Export Excel" dan "Import Excel" dan "Tahun Akreditasi" (buatkan tombolnya saja dulu)
================================================================================================================

sekarang anda akan meng-aktifkan fungsi tombol "Input Mapping KPA", jika tombol tersebut di-klik akan tampil modal "Input Data Mapping KPA Paud" dengan form sebagi berikut
- label NPSN Sekolah (form input text) akan menyimpan ke field mapping_paud_kpa.sekolah_id
- NIA Asesor (form input text) akan menyimpan ke field mapping_paud_kpa.kd_asesor
- label Tanggal Penetapan KPA (form input date) akan menyimpan ke field mapping_paud_kpa.tgl_penetapan_kpa
- label Tahun Akreditasi (form input text) akan menyimpan ke field mapping_paud_kpa.tahun_akreditasi
- label Tahap Ke (form input text) akan menyimpan ke field mapping_paud_kpa.tahap
- mapping_paud_kpa.provinsi_id= provinsi_id session login
jika proses peng-input-an selesai dan data tersimpan di tabel mapping_paud_kpa di database maka tabel utama di modul "Mapping Asesor KPA PAUD" otomatis berubah tanpa refresh browser (tanpa reload halaman), sampai disini apakah ada yang ingin anda tanyakan?
================================================================================================================

Sekarang kita akan membuat modal "Detail Mapping KPA Paud".
Ketika tombol icon detail yang ada di kolom "Aksi" pada tabel utama modul "Mapping Asesor KPA PAUD" di-klik maka akan muncul sebuah modal dengan judul modal adalah "Detail Mapping KPA Paud" dengan ukuran modal-xl.
Di dalam modal "Detail Mapping KPA Paud" terdapat lima buah kolom, masing-masing kolom memiliki judul kolom dan isi kolom.

Kolom pertama dengan judul kolom "Data Sekolah" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:
- Label NPSN dengan kolom disebelahnya berisi field sekolah.npsn
- Label Nama Sekolah  dengan kolom disebelahnya berisi field sekolah.nama_sekolah
- Label Jenjang dengan kolom disebelahnya berisi field jenjang.nm_jenjang
- Label Kab/Kota dengan kolom disebelahnya berisi field kab_kota.nm_kota
- Label Nama Kepala Sekolah dengan kolom disebelahnya berisi field sekolah.nama_kepsek
- Label HP Kepala Sekolah dengan kolom disebelahnya berisi field sekolah.no_hp_kepsek
- No WA Kepala Sekolah dengan kolom disebelahnya berisi field sekolah.no_wa_kepsek

Kolom kedua dengan judul kolom "Data Asesor" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:

- Label Asesor (colspan=2)
- Label NIA dengan kolom disebelahnya berisi field asesor.nia
- Label Nama dengan kolom disebelahnya berisi field asesor.nm_asesor
- Label No HP dengan kolom disebelahnya berisi field asesor.no_hp
- Label Kab/Kota dengan kolom disebelahnya berisi field kab_kota.nm_kota

Kolom ketiga dengan judul kolom "Dokumen Unggahan" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:

- Label File Temuan Hasil KPA dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_kpa.file_laporan_hasil_kpa ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

Kolom keempat dengan judul kolom "Pelaksanaan Kegiatan" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:
- Label Tanggal Penetapan KPA Dimulai dengan kolom disebelahnya berisi mapping_paud_kpa.field tgl_penetapan_kpa

Kolom kelima dengan judul kolom "Aksi" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:
- Tombol "Edit Tanggal Penetapan KPA"
- Tombol "Hapus" (hapus permanen)

sampai disini jika ada yang belum anda pahami silahkan bertanya
================================================================================================================

baik kawan, kita akan melanjutkan modul "Mapping Asesor KPA PAUD" ini, pada kolom "Dokumen Unggahan" yang ada di modal "Detail Mapping KPA PAUD" tertulis "Sudah Upload" dan atau "Belum Upload", jika yang tampil adalah tulisan "Sudah Upload" maka tulisan tersebut bisa di-klik sedangkan tulisan "Belum Upload" tidak bisa di-klik, untuk tulisan "Sudah Upload" jika di-klik akan membuka tab baru yang menampilkan file PDF. OK kita jelaskan lebih rinci lagi sebagai berikut:

"File Temuan Hasil KPA" jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori "../../../simak/files/upload_file_hasil_kpa/", untuk nama file diambil dari field "mapping_paud_kpa.file_laporan_hasil_kpa"

Sampai disini apakah anda mengerti? silahkan bertanya jika belum paham
================================================================================================================


baik sobatku yang paling pintar sedunia, kita akan melanjutkan modul "Mapping Asesor KPA PAUD", pada kolom "AKSI" yang ada di modal "Detail Mapping KPA PAUD" terdapat tombol "Edit Tanggal Penetapan KPA" jika tombol tersebut di-klik akan menampilkan sebuah modal dengan ukuran kecil yang berisi form untuk edit atau update field mapping_paud_kpa.tgl_penetapan_kpa, perlu saya sampaikan bahwa dengan tampilnya modal yang berisi form edit tersebut tidak serta merta menutup modal "Detail Mapping KPA PAUD", nantinya modal untuk edit/update mapping_paud_kpa.tgl_penetapan_kpa berada di atas modal "Detail Mapping Validasi Dasmen" kemudian ketika di-klik tombol "Update Perubahan" maka secara otomatis "Tanggal Penetapan KPA Dimulai" yang ada di kolom "Pelaksanaan Kegiatan" berubah tanpa refresh browser, sampai disini apakah anda mengerti dengan apa yang saya maksud? jika belum mengerti silahkan bertanya

================================================================================================================

baiklah sobatku, sekarang kita akan memfungsikan/mengaktifkan tombol "Export Excel" di modul "Mapping Asesor KPA PAUD", anda gunakan library export excel sama seperti modul "Data Dasmen" yang ada di direktori "tim_it/dasmen/", data yang perlu di export di adalah:
NO (autoincrement),
NPSN (sekolah.npsn),
NAMA SEKOLAH (sekolah.nama_sekolah),
JENJANG (jenjang.nm_jenjang),
KAB/KOTA (kab_kota.nm_kota domisili sekolah),
NIA ASESOR (asesor.nia),
NAMA ASESOR (asesor.nm_asesor),
KAB/KOTA (kab_kota.nm_kota domisili asesor),
TAHUN AKREDITASI (mapping_paud_kpa.tahun_akreditasi),
TAHAP VALIDASI (mapping_paud_kpa.tahap),
TANGGAL PENETAPAN KPA (mapping_paud_kpa.tgl_penetapan_kpa)
FILE LAPORAN HASIL KPA (jika mapping_paud_kpa.file_laporan_hasil_kpa terbaca kosong/empty maka warna fill pada cell merah dan tulisan "Belum Unggah" warna putih terang, jika terbaca ada isinya/tidak kosong maka warna fill pada cell hijau muda terang dengan tulisan warna hitam bertuliskan "Sudah Unggah")

data yang di download disesuaikan dengan filter where mapping_paud_kpa_tahun.nama_tahun

jika anda belum paham dengan maksud saya silahkan bertanya



