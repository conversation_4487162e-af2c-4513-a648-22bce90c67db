<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Ambil parameter
    $nia = trim($_POST['nia'] ?? '');
    $asesor_table = trim($_POST['asesor_table'] ?? '');
    $nia_field = trim($_POST['nia_field'] ?? '');
    $nama_field = trim($_POST['nama_field'] ?? '');
    
    if (empty($nia)) {
        echo json_encode([
            'success' => false,
            'message' => 'NIA tidak boleh kosong'
        ]);
        exit;
    }
    
    // Validasi table dan field names untuk security
    $allowed_tables = ['asesor_1', 'asesor_2'];
    $allowed_nia_fields = ['nia1', 'nia2'];
    $allowed_nama_fields = ['nm_asesor1', 'nm_asesor2'];
    
    if (!in_array($asesor_table, $allowed_tables) || 
        !in_array($nia_field, $allowed_nia_fields) || 
        !in_array($nama_field, $allowed_nama_fields)) {
        echo json_encode([
            'success' => false,
            'message' => 'Parameter tidak valid'
        ]);
        exit;
    }
    
    // Ambil provinsi_id dari session
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Tentukan kota_id field berdasarkan table
    $kota_field = ($asesor_table === 'asesor_1') ? 'kota_id1' : 'kota_id2';
    
    // Query untuk validasi asesor
    $query = "SELECT 
                a.$nia_field as nia,
                a.$nama_field as nama,
                a.no_hp,
                a.$kota_field as kota_id,
                k.nm_kota as kota
              FROM $asesor_table a
              LEFT JOIN kab_kota k ON a.$kota_field = k.kota_id
              WHERE a.$nia_field = ? 
                AND a.provinsi_id = ?
                AND a.status_keaktifan_id = '1'
                AND a.soft_delete = '1'";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("si", $nia, $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Asesor dengan NIA ' . $nia . ' tidak ditemukan atau tidak aktif'
        ]);
        exit;
    }
    
    $asesor = $result->fetch_assoc();
    
    echo json_encode([
        'success' => true,
        'data' => [
            'nia' => $asesor['nia'],
            'nama' => $asesor['nama'],
            'no_hp' => $asesor['no_hp'],
            'kota' => $asesor['kota'] ?? 'Tidak diketahui'
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Validate Asesor Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan saat validasi asesor'
    ]);
}

$conn->close();
?>
