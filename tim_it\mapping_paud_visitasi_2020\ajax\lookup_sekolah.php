<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input NPSN
    if (!isset($_POST['npsn']) || empty(trim($_POST['npsn']))) {
        throw new Exception('NPSN harus diisi');
    }
    
    $npsn = $conn->real_escape_string(trim($_POST['npsn']));
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Query lookup sekolah PAUD
    $query = "SELECT s.sekolah_id, s.npsn, s.nama_sekolah, s.jenjang_id, s.rumpun, 
                     s.alama<PERSON>, s.kota_id, s.desa_k<PERSON><PERSON><PERSON>, s.kecamatan,
                     s.nama_kepsek, s.no_hp_kepsek, s.no_wa_kepsek,
                     j.nm_jenjang,
                     kk.nm_kota
              FROM sekolah s
              LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
              LEFT JOIN kab_kota kk ON s.kota_id = kk.kota_id
              WHERE s.npsn = ? 
                AND s.provinsi_id = ?
                AND s.rumpun = 'paud'
                AND s.soft_delete = '1'
              LIMIT 1";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("si", $npsn, $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $sekolah = $result->fetch_assoc();
        
        echo json_encode([
            'success' => true,
            'data' => $sekolah,
            'message' => 'Sekolah ditemukan'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'data' => null,
            'message' => 'Sekolah dengan NPSN tersebut tidak ditemukan atau bukan PAUD'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'data' => null,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
