<?php
// Debug: File accessed
error_log("upload_sertifikat.php accessed at " . date('Y-m-d H:i:s'));

session_start();
require_once '../../../koneksi.php';

// Cek session dan level
if (!isset($_SESSION['kd_user']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Ambil provinsi_id dari session
$provinsi_id = $_SESSION['provinsi_id'];

if ($_POST) {
    // Baca Variabel Data Form dengan real_escape_string
    $npsn = $conn->real_escape_string($_POST['npsn']);
    $tahun_akreditasi = $conn->real_escape_string($_POST['tahun_akreditasi']);
    $nama_file = $conn->real_escape_string($_FILES['nama_file']['name']);
    
    // Upload file terlebih dahulu
    move_uploaded_file($_FILES['nama_file']['tmp_name'], '../../../../simak/files/file_sertifikat/' . $_FILES['nama_file']['name']);

    // Cek NPSN di database
    $sql_npsn = "SELECT sekolah.sekolah_id, sekolah.npsn FROM sekolah
                 WHERE sekolah.npsn = '$npsn'
                   AND sekolah.provinsi_id = '$provinsi_id'
                   AND sekolah.rumpun = 'dasmen'
                   AND sekolah.soft_delete = '1'";
    $result1 = $conn->query($sql_npsn);
    
    if ($result1->num_rows >= 1) {
        $baca_npsn = mysqli_fetch_array($result1);
        $sekolah_id_baca = $baca_npsn['sekolah_id'];
        $npsn_baca = $baca_npsn['npsn'];

        // Cek duplikasi nama file
        $input1 = "SELECT * FROM sertifikat WHERE nama_file='$nama_file'";
        $result2 = $conn->query($input1);
        
        if ($result2->num_rows > 0) {
            echo json_encode(['success' => false, 'message' => 'Nama file sertifikat sudah digunakan, silakan upload dengan nama yang berbeda']);
        } else {
            // Insert ke database
            $input2 = "INSERT INTO sertifikat (sekolah_id, tahun_akreditasi, nama_file, provinsi_id)
                       VALUES ('$sekolah_id_baca', '$tahun_akreditasi', '$nama_file', '$provinsi_id')";
            $hasil_input = $conn->query($input2);

            if ($hasil_input) {
                echo json_encode(['success' => true, 'message' => 'Sertifikat berhasil diupload']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Data gagal tersimpan']);
            }
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'NPSN tidak ditemukan atau bukan sekolah dasmen di provinsi Anda']);
    }
}

$conn->close();
?>
