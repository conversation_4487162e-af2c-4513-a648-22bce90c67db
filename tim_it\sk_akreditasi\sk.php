<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');
?>

<!-- Include header -->
<?php include '../header.php'; ?>

<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

<!-- Content Wrapper -->
<div class="content-wrapper">
    <!-- Content Header -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h5><i class="fas fa-file-signature"></i> SK Hasil Akreditasi Dasmen, Kesetaraan, Paud</h5>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item active">SK Akreditasi</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-list"></i> Data SK Hasil Akreditasi
                            </h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#modal-tambah-sk">
                                    <i class="fas fa-plus"></i> Tambah SK Akreditasi
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="table-sk" class="table table-bordered table-striped table-hover" width="100%">
                                    <thead>
                                        <tr>
                                            <th width="5%">NO <br> &nbsp;</th>
                                            <th width="10%">TANGGAL SK <br> &nbsp;</th>
                                            <th width="15%">NOMOR SK <br> &nbsp;</th>
                                            <th width="47%">TENTANG <br> &nbsp;</th>
                                            <th width="10%">TAHUN <br> AKREDITASI</th>
                                            <th width="13%">AKSI <br> &nbsp;</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data akan dimuat via AJAX -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Modal Tambah SK -->
<div class="modal fade" id="modal-tambah-sk" tabindex="-1" role="dialog" aria-labelledby="modal-tambah-sk-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="modal-tambah-sk-label">
                    <i class="fas fa-plus"></i> Tambah SK Akreditasi
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-tambah-sk" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="tgl_sk_akreditasi">Tanggal SK <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="tgl_sk_akreditasi" name="tgl_sk_akreditasi" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="tahun_akreditasi">Tahun Akreditasi <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="tahun_akreditasi" name="tahun_akreditasi" 
                                       min="2000" max="2030" required placeholder="Contoh: 2024">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="no_sk_akreditasi">Nomor SK <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="no_sk_akreditasi" name="no_sk_akreditasi" 
                               placeholder="Masukkan Nomor SK" required maxlength="50">
                    </div>
                    
                    <div class="form-group">
                        <label for="tentang">Tentang <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="tentang" name="tentang" rows="3" 
                                  placeholder="Masukkan keterangan tentang SK" required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="nm_lembaga">Nama Lembaga <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nm_lembaga" name="nm_lembaga" 
                               placeholder="Masukkan Nama Lembaga" required maxlength="3">
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle"></i> Maksimal 3 karakter (contoh: BAN, LAM, dll).
                        </small>
                    </div>
                    
                    <div class="form-group">
                        <label for="nama_file">File SK (PDF) <span class="text-danger">*</span></label>
                        <input type="file" class="form-control-file" id="nama_file" name="nama_file" 
                               accept=".pdf" required>
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle"></i> Hanya file PDF yang diizinkan. Maksimal ukuran 10MB.
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Simpan SK
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Edit SK -->
<div class="modal fade" id="modal-edit-sk" tabindex="-1" role="dialog" aria-labelledby="modal-edit-sk-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="modal-edit-sk-label">
                    <i class="fas fa-edit"></i> Edit SK Akreditasi
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-edit-sk" enctype="multipart/form-data">
                <input type="hidden" id="edit_sk_akreditasi_id" name="sk_akreditasi_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_tgl_sk_akreditasi">Tanggal SK <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="edit_tgl_sk_akreditasi" name="tgl_sk_akreditasi" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit_tahun_akreditasi">Tahun Akreditasi <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="edit_tahun_akreditasi" name="tahun_akreditasi" 
                                       min="2000" max="2030" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_no_sk_akreditasi">Nomor SK <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_no_sk_akreditasi" name="no_sk_akreditasi" 
                               required maxlength="50">
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_tentang">Tentang <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="edit_tentang" name="tentang" rows="3" required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_nm_lembaga">Nama Lembaga <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_nm_lembaga" name="nm_lembaga" 
                               required maxlength="3">
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_nama_file">File SK (PDF)</label>
                        <input type="file" class="form-control-file" id="edit_nama_file" name="nama_file" accept=".pdf">
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle"></i> Kosongkan jika tidak ingin mengubah file. Hanya file PDF yang diizinkan.
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save"></i> Update SK
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Include footer -->
<?php include '../footer.php'; ?>

<!-- DataTables & plugins -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap4.min.js"></script>

<!-- Custom JS -->
<script src="js/sk.js"></script>

<style>
.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
}

.table td {
    vertical-align: middle;
}

.btn-action {
    margin: 0 2px;
}

.modal-header {
    border-bottom: none;
}

.modal-footer {
    border-top: none;
}

#nama_file, #edit_nama_file {
    border: 2px dashed #ddd;
    padding: 10px;
    border-radius: 5px;
    background-color: #f9f9f9;
}

#nama_file:hover, #edit_nama_file:hover {
    border-color: #007bff;
    background-color: #f0f8ff;
}
</style>
