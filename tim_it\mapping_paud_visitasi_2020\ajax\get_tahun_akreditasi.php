<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Get provinsi_id from session
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Query untuk mendapatkan tahun akreditasi aktif
    $query = "SELECT id_mapping_tahun, nama_tahun, provinsi_id 
              FROM mapping_paud_visitasi_tahun 
              WHERE provinsi_id = ? 
              ORDER BY id_mapping_tahun DESC 
              LIMIT 1";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $tahun_data = $result->fetch_assoc();
        
        echo json_encode([
            'success' => true,
            'data' => $tahun_data,
            'message' => 'Tahun akreditasi aktif ditemukan'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'data' => null,
            'message' => 'Belum ada tahun akreditasi yang diset untuk provinsi ini'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'data' => null,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
