<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Get provinsi_id from session
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Get tahun akreditasi aktif
    $tahun_query = "SELECT nama_tahun FROM mapping_paud_kpa_tahun WHERE provinsi_id = ? ORDER BY id_mapping_tahun DESC LIMIT 1";
    $tahun_stmt = $conn->prepare($tahun_query);
    $tahun_stmt->bind_param("i", $provinsi_id);
    $tahun_stmt->execute();
    $tahun_result = $tahun_stmt->get_result();
    
    $tahun_aktif = '';
    if ($tahun_result->num_rows > 0) {
        $tahun_row = $tahun_result->fetch_assoc();
        $tahun_aktif = $tahun_row['nama_tahun'];
    }
    
    // DataTables parameters
    $draw = intval($_POST['draw']);
    $start = intval($_POST['start']);
    $length = intval($_POST['length']);
    $search_value = $_POST['search']['value'];
    
    // Order parameters
    $order_column_index = $_POST['order'][0]['column'];
    $order_direction = $_POST['order'][0]['dir'];
    
    // Column mapping for ordering
    $columns = [
        0 => 'mp.id_mapping',
        1 => 's.npsn',
        2 => 's.nama_sekolah',
        3 => 'j.nm_jenjang',
        4 => 'kk.nm_kota',
        5 => 'a.nia',
        6 => 'a.nm_asesor',
        7 => 'mp.tahun_akreditasi',
        8 => 'mp.tahap',
        9 => 'mp.id_mapping'
    ];
    
    $order_column = isset($columns[$order_column_index]) ? $columns[$order_column_index] : 'mp.id_mapping';
    
    // Base query dengan JOIN ke mapping_paud_kpa_tahun
    $base_query = "FROM mapping_paud_kpa mp
                   LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
                   LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                   LEFT JOIN kab_kota kk ON s.kota_id = kk.kota_id
                   LEFT JOIN asesor a ON mp.kd_asesor = a.kd_asesor
                   LEFT JOIN mapping_paud_kpa_tahun mt ON mp.tahun_akreditasi = mt.nama_tahun AND mt.provinsi_id = $provinsi_id
                   WHERE mp.provinsi_id = $provinsi_id
                     AND s.rumpun = 'paud'
                     AND s.soft_delete = '1'
                     AND a.soft_delete = '1'
                     AND mp.tahun_akreditasi = mt.nama_tahun";
    
    // Search functionality
    if (!empty($search_value)) {
        $base_query .= " AND (
            s.npsn LIKE '%$search_value%' OR
            s.nama_sekolah LIKE '%$search_value%' OR
            j.nm_jenjang LIKE '%$search_value%' OR
            kk.nm_kota LIKE '%$search_value%' OR
            a.nia LIKE '%$search_value%' OR
            a.nm_asesor LIKE '%$search_value%' OR
            mp.tahun_akreditasi LIKE '%$search_value%' OR
            mp.tahap LIKE '%$search_value%'
        )";
    }
    
    // Count total records
    $total_query = "SELECT COUNT(*) as total " . $base_query;
    $total_result = $conn->query($total_query);
    $total_records = $total_result->fetch_assoc()['total'];
    
    // Main query with ordering and pagination
    $main_query = "SELECT mp.id_mapping, mp.sekolah_id, mp.kd_asesor, mp.tgl_penetapan_kpa,
                          mp.tahap, mp.tahun_akreditasi, mp.file_laporan_hasil_kpa,
                          mp.tgl_file_hasil_kpa, mp.jam_file_hasil_kpa,
                          s.npsn, s.nama_sekolah, s.nama_kepsek, s.no_hp_kepsek, s.no_wa_kepsek,
                          j.nm_jenjang,
                          kk.nm_kota,
                          a.nia, a.nm_asesor, a.no_hp, a.unit_kerja,
                          (SELECT kab_kota.nm_kota FROM kab_kota WHERE kab_kota.kota_id = a.kota_id) as kota_asesor
                   " . $base_query . "
                   ORDER BY $order_column $order_direction
                   LIMIT $start, $length";
    
    $result = $conn->query($main_query);
    
    $data = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $data[] = $row;
        }
    }
    
    // Response
    $response = [
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $total_records,
        'data' => $data
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log("Get Mapping KPA Error: " . $e->getMessage());
    
    echo json_encode([
        'draw' => isset($_POST['draw']) ? intval($_POST['draw']) : 1,
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => 'Terjadi kesalahan saat memuat data'
    ]);
}

$conn->close();
?>
