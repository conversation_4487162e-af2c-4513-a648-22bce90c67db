<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi required fields
    if (!isset($_POST['id_mapping']) || empty(trim($_POST['id_mapping']))) {
        throw new Exception('ID mapping harus diisi');
    }
    
    // Sanitasi input
    $id_mapping = intval($_POST['id_mapping']);
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Tanggal penetapan KPA adalah opsional - gunakan '0000-00-00' jika kosong
    $tgl_penetapan_kpa = isset($_POST['tgl_penetapan_kpa']) && !empty(trim($_POST['tgl_penetapan_kpa'])) 
                         ? $conn->real_escape_string(trim($_POST['tgl_penetapan_kpa'])) 
                         : '0000-00-00';
    
    // Validasi ID mapping
    if ($id_mapping <= 0) {
        throw new Exception('ID mapping tidak valid');
    }
    
    // Debug: Log values
    error_log("Update Tanggal KPA - ID: $id_mapping, Tanggal: $tgl_penetapan_kpa, Provinsi: $provinsi_id");
    
    // Cek apakah data mapping exists dan milik provinsi yang benar
    $check_query = "SELECT mp.id_mapping, mp.tgl_penetapan_kpa, s.nama_sekolah, s.npsn
                    FROM mapping_paud_kpa mp
                    LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
                    WHERE mp.id_mapping = ? AND mp.provinsi_id = ?";
    
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("ii", $id_mapping, $provinsi_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows === 0) {
        throw new Exception('Data mapping tidak ditemukan atau Anda tidak memiliki akses untuk mengupdate data ini');
    }
    
    $mapping_data = $check_result->fetch_assoc();
    $old_date = $mapping_data['tgl_penetapan_kpa'];
    
    // Begin transaction
    $conn->autocommit(false);
    
    // Update tanggal penetapan KPA
    $update_query = "UPDATE mapping_paud_kpa SET tgl_penetapan_kpa = ? WHERE id_mapping = ? AND provinsi_id = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("sii", $tgl_penetapan_kpa, $id_mapping, $provinsi_id);
    
    if (!$update_stmt->execute()) {
        throw new Exception('Gagal mengupdate tanggal penetapan KPA: ' . $conn->error);
    }
    
    // Cek apakah ada row yang terupdate
    if ($update_stmt->affected_rows === 0) {
        // Tidak ada perubahan, tapi bukan error
        $conn->commit();
        $conn->autocommit(true);
        
        echo json_encode([
            'success' => true,
            'message' => 'Tanggal penetapan KPA berhasil disimpan (tidak ada perubahan)',
            'data' => [
                'id_mapping' => $id_mapping,
                'tgl_penetapan_kpa' => $tgl_penetapan_kpa,
                'old_date' => $old_date,
                'nama_sekolah' => $mapping_data['nama_sekolah'],
                'npsn' => $mapping_data['npsn']
            ]
        ]);
        return;
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Log successful update
    error_log("Update Tanggal KPA Success - ID: $id_mapping, Old: $old_date, New: $tgl_penetapan_kpa, Sekolah: " . $mapping_data['nama_sekolah'] . ", User: " . $_SESSION['nm_user']);
    
    echo json_encode([
        'success' => true,
        'message' => 'Tanggal penetapan KPA berhasil diupdate',
        'data' => [
            'id_mapping' => $id_mapping,
            'tgl_penetapan_kpa' => $tgl_penetapan_kpa,
            'old_date' => $old_date,
            'nama_sekolah' => $mapping_data['nama_sekolah'],
            'npsn' => $mapping_data['npsn']
        ]
    ]);
    
} catch (Exception $e) {
    // Rollback transaction
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Update Tanggal KPA Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
