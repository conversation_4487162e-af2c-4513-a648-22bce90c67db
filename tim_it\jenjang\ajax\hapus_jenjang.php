<?php
/**
 * AJAX handler untuk menghapus data jenjang (hard delete)
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input
    if (!isset($_POST['id_jenjang']) || empty($_POST['id_jenjang'])) {
        throw new Exception('ID jenjang harus diisi');
    }
    
    $id_jenjang = intval($_POST['id_jenjang']);
    $nm_jenjang = isset($_POST['nm_jenjang']) ? $_POST['nm_jenjang'] : '';
    
    // Cek apakah jenjang masih digunakan di tabel sekolah
    $check_usage_sql = "SELECT COUNT(*) as count FROM sekolah WHERE jenjang_id = (SELECT jenjang_id FROM jenjang WHERE id_jenjang = ?)";
    $check_usage_stmt = $conn->prepare($check_usage_sql);
    $check_usage_stmt->bind_param("i", $id_jenjang);
    $check_usage_stmt->execute();
    $usage_result = $check_usage_stmt->get_result();
    $usage_count = $usage_result->fetch_assoc()['count'];
    
    if ($usage_count > 0) {
        throw new Exception('Jenjang tidak dapat dihapus karena masih digunakan oleh ' . $usage_count . ' sekolah');
    }
    
    // Cek apakah data jenjang ada
    $check_exist_sql = "SELECT COUNT(*) as count FROM jenjang WHERE id_jenjang = ?";
    $check_exist_stmt = $conn->prepare($check_exist_sql);
    $check_exist_stmt->bind_param("i", $id_jenjang);
    $check_exist_stmt->execute();
    $exist_result = $check_exist_stmt->get_result();
    $exist_count = $exist_result->fetch_assoc()['count'];
    
    if ($exist_count == 0) {
        throw new Exception('Data jenjang tidak ditemukan');
    }
    
    // Begin transaction
    $conn->autocommit(false);
    
    // Query delete (hard delete karena ini data master)
    $sql = "DELETE FROM jenjang WHERE id_jenjang = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id_jenjang);
    
    if (!$stmt->execute()) {
        throw new Exception('Gagal menghapus data jenjang: ' . $stmt->error);
    }
    
    // Cek apakah ada baris yang terhapus
    if ($stmt->affected_rows == 0) {
        throw new Exception('Data jenjang tidak ditemukan atau sudah terhapus');
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data jenjang "' . $nm_jenjang . '" berhasil dihapus',
        'data' => [
            'id_jenjang' => $id_jenjang,
            'nm_jenjang' => $nm_jenjang
        ]
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Rollback transaction jika ada error
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Hapus Jenjang Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
