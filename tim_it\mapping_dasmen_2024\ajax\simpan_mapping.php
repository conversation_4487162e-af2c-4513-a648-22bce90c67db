<?php
/**
 * AJAX handler untuk simpan data mapping visitasi SM
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Ambil dan validasi input dengan real_escape_string
    $sekolah_id = intval($_POST['sekolah_id'] ?? 0);
    $kd_asesor1 = $conn->real_escape_string(trim($_POST['kd_asesor1'] ?? ''));
    $kd_asesor2 = $conn->real_escape_string(trim($_POST['kd_asesor2'] ?? ''));
    $tahun_akreditasi = $conn->real_escape_string(trim($_POST['tahun_akreditasi'] ?? ''));
    $tahap = intval($_POST['tahap'] ?? 0);

    // Optional fields dengan escape
    $tgl_pra_visitasi = !empty($_POST['tgl_pra_visitasi']) ? "'" . $conn->real_escape_string($_POST['tgl_pra_visitasi']) . "'" : 'NULL';
    $tgl_surat_tugas_pra_visitasi = !empty($_POST['tgl_surat_tugas_pra_visitasi']) ? "'" . $conn->real_escape_string($_POST['tgl_surat_tugas_pra_visitasi']) . "'" : 'NULL';
    $no_surat_tugas_pra_visitasi = $conn->real_escape_string(trim($_POST['no_surat_tugas_pra_visitasi'] ?? ''));
    $tgl_mulai_visitasi = !empty($_POST['tgl_mulai_visitasi']) ? "'" . $conn->real_escape_string($_POST['tgl_mulai_visitasi']) . "'" : 'NULL';
    $tgl_akhir_visitasi = !empty($_POST['tgl_akhir_visitasi']) ? "'" . $conn->real_escape_string($_POST['tgl_akhir_visitasi']) . "'" : 'NULL';
    $tgl_surat_tugas_visitasi = !empty($_POST['tgl_surat_tugas_visitasi']) ? "'" . $conn->real_escape_string($_POST['tgl_surat_tugas_visitasi']) . "'" : 'NULL';
    $no_surat_tugas_visitasi = $conn->real_escape_string(trim($_POST['no_surat_tugas_visitasi'] ?? ''));
    
    // Validasi required fields
    if ($sekolah_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'Sekolah ID tidak valid']);
        exit;
    }
    
    if (empty($kd_asesor1)) {
        echo json_encode(['success' => false, 'message' => 'Kode Asesor 1 wajib diisi']);
        exit;
    }
    
    if (empty($kd_asesor2)) {
        echo json_encode(['success' => false, 'message' => 'Kode Asesor 2 wajib diisi']);
        exit;
    }
    
    if (empty($tahun_akreditasi)) {
        echo json_encode(['success' => false, 'message' => 'Tahun akreditasi wajib diisi']);
        exit;
    }
    
    if ($tahap <= 0) {
        echo json_encode(['success' => false, 'message' => 'Tahap wajib diisi']);
        exit;
    }
    
    if ($kd_asesor1 === $kd_asesor2) {
        echo json_encode(['success' => false, 'message' => 'Asesor 1 dan Asesor 2 tidak boleh sama']);
        exit;
    }
    
    // Ambil provinsi_id dari session
    $provinsi_id = intval($_SESSION['provinsi_id']);

    // Validasi sekolah exists dan belum ada mapping
    $check_sekolah = "SELECT sekolah_id FROM sekolah
                     WHERE sekolah_id = $sekolah_id AND provinsi_id = $provinsi_id AND rumpun = 'dasmen' AND soft_delete = '1'";
    $result_check = $conn->query($check_sekolah);

    if (!$result_check || $result_check->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Sekolah tidak valid']);
        exit;
    }

    // Cek duplikasi mapping
    $check_mapping = "SELECT id_mapping FROM mapping_2024
                     WHERE sekolah_id = $sekolah_id AND provinsi_id = $provinsi_id";
    $result_mapping = $conn->query($check_mapping);

    if (!$result_mapping) {
        echo json_encode(['success' => false, 'message' => 'Error checking duplicate mapping']);
        exit;
    }

    if ($result_mapping->num_rows > 0) {
        echo json_encode(['success' => false, 'message' => 'Sekolah sudah ada dalam mapping visitasi']);
        exit;
    }
    
    // Insert data mapping
    $insert_query = "INSERT INTO mapping_2024 (
                        sekolah_id,
                        kd_asesor1,
                        kd_asesor2,
                        tgl_pra_visitasi,
                        tgl_surat_tugas_pra_visitasi,
                        no_surat_tugas_pra_visitasi,
                        tgl_mulai_visitasi,
                        tgl_akhir_visitasi,
                        tgl_surat_tugas_visitasi,
                        no_surat_tugas_visitasi,
                        tahap,
                        tahun_akreditasi,
                        provinsi_id
                    ) VALUES (
                        $sekolah_id,
                        '$kd_asesor1',
                        '$kd_asesor2',
                        $tgl_pra_visitasi,
                        $tgl_surat_tugas_pra_visitasi,
                        '$no_surat_tugas_pra_visitasi',
                        $tgl_mulai_visitasi,
                        $tgl_akhir_visitasi,
                        $tgl_surat_tugas_visitasi,
                        '$no_surat_tugas_visitasi',
                        $tahap,
                        '$tahun_akreditasi',
                        $provinsi_id
                    )";

    $result_insert = $conn->query($insert_query);

    if ($result_insert) {
        $id_mapping = $conn->insert_id;

        // Log activity
        error_log("Mapping Visitasi Created - ID: $id_mapping, User: " . ($_SESSION['nm_user'] ?? 'Unknown'));

        echo json_encode([
            'success' => true,
            'message' => 'Data mapping berhasil disimpan',
            'id_mapping' => $id_mapping
        ]);
    } else {
        throw new Exception("Insert failed: " . $conn->error);
    }
    
} catch (Exception $e) {
    error_log("Simpan Mapping Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan saat menyimpan data mapping'
    ]);
}

$conn->close();
?>
