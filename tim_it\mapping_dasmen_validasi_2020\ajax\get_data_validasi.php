<?php
session_start();
require_once '../../../koneksi.php';

// Cek session dan level
if (!isset($_SESSION['kd_user']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

// Ambil provinsi_id dari session
$provinsi_id = $_SESSION['provinsi_id'];

// DataTables parameters
$draw = isset($_POST['draw']) ? intval($_POST['draw']) : 1;
$start = isset($_POST['start']) ? intval($_POST['start']) : 0;
$length = isset($_POST['length']) ? intval($_POST['length']) : 25;
$search_value = isset($_POST['search']['value']) ? $_POST['search']['value'] : '';

// Column mapping for ordering
$columns = [
    0 => 'id_mapping_validasi', // NO (not orderable)
    1 => 's.npsn',
    2 => 's.nama_sekolah',
    3 => 'j.nm_jenjang',
    4 => 'kk.nm_kota',
    5 => 'a1.nia1',
    6 => 'a1.nm_asesor1',
    7 => 'a2.nia2',
    8 => 'a2.nm_asesor2',
    9 => 'mv.tahun_akreditasi',
    10 => 'mv.tahap',
    11 => 'id_mapping_validasi' // AKSI (not orderable)
];

$order_column_index = isset($_POST['order'][0]['column']) ? intval($_POST['order'][0]['column']) : 9;
$order_column = isset($columns[$order_column_index]) ? $columns[$order_column_index] : 'mv.tahun_akreditasi';
$order_dir = isset($_POST['order'][0]['dir']) ? $_POST['order'][0]['dir'] : 'desc';

try {
    // Cek koneksi database
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Ambil tahun akreditasi aktif terlebih dahulu
    $tahun_aktif_query = "SELECT nama_tahun FROM mapping_validasi_tahun WHERE provinsi_id = $provinsi_id LIMIT 1";
    $tahun_aktif_result = $conn->query($tahun_aktif_query);

    if ($tahun_aktif_result->num_rows == 0) {
        // Jika tidak ada tahun akreditasi yang di-set, tampilkan data kosong
        $tahun_aktif = 'TIDAK_ADA';
    } else {
        $tahun_aktif = $tahun_aktif_result->fetch_assoc()['nama_tahun'];
    }

    error_log("Get Data Validasi - Tahun Aktif: " . $tahun_aktif);

    // Base query dengan filter tahun akreditasi yang eksplisit
    $base_query = "FROM mapping_validasi mv
                   LEFT JOIN sekolah s ON mv.sekolah_id = s.sekolah_id
                   LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                   LEFT JOIN kab_kota kk ON s.kota_id = kk.kota_id
                   LEFT JOIN asesor_1 a1 ON mv.kd_asesor1 = a1.kd_asesor1
                   LEFT JOIN asesor_2 a2 ON mv.kd_asesor2 = a2.kd_asesor2
                   WHERE mv.provinsi_id = $provinsi_id
                     AND mv.tahun_akreditasi = '$tahun_aktif'
                     AND s.rumpun = 'dasmen'
                     AND s.soft_delete = '1'";

    // Add search condition
    $search_query = "";
    if (!empty($search_value)) {
        $search_value = $conn->real_escape_string($search_value);
        $search_query = " AND (s.npsn LIKE '%$search_value%'
                             OR s.nama_sekolah LIKE '%$search_value%'
                             OR j.nm_jenjang LIKE '%$search_value%'
                             OR kk.nm_kota LIKE '%$search_value%'
                             OR a1.nia1 LIKE '%$search_value%'
                             OR a1.nm_asesor1 LIKE '%$search_value%'
                             OR a2.nia2 LIKE '%$search_value%'
                             OR a2.nm_asesor2 LIKE '%$search_value%'
                             OR mv.tahun_akreditasi LIKE '%$search_value%'
                             OR mv.tahap LIKE '%$search_value%')";
    }
    


    // Debug: Log base query
    error_log("Get Data Validasi - Base Query: SELECT COUNT(*) as total $base_query");

    // Count total records
    $total_query = "SELECT COUNT(*) as total $base_query";
    $total_result = $conn->query($total_query);
    if (!$total_result) {
        throw new Exception("Total query failed: " . $conn->error);
    }
    $total_records = $total_result->fetch_assoc()['total'];

    // Debug: Log total records
    error_log("Get Data Validasi - Total Records: " . $total_records);

    // Count filtered records
    $filtered_query = "SELECT COUNT(*) as total $base_query $search_query";
    $filtered_result = $conn->query($filtered_query);
    if (!$filtered_result) {
        throw new Exception("Filtered query failed: " . $conn->error);
    }
    $filtered_records = $filtered_result->fetch_assoc()['total'];

    // Get data with pagination
    $data_query = "SELECT mv.id_mapping_validasi,
                          mv.sekolah_id,
                          mv.kd_asesor1,
                          mv.kd_asesor2,
                          mv.tahun_akreditasi,
                          mv.tahap,
                          s.npsn,
                          s.nama_sekolah,
                          j.nm_jenjang,
                          kk.nm_kota,
                          a1.nia1,
                          a1.nm_asesor1,
                          a2.nia2,
                          a2.nm_asesor2
                   $base_query
                   $search_query
                   ORDER BY $order_column $order_dir
                   LIMIT $start, $length";

    $data_result = $conn->query($data_query);
    if (!$data_result) {
        throw new Exception("Data query failed: " . $conn->error);
    }
    
    $data = [];
    $no = $start + 1;
    $tahun_list = []; // Debug: collect tahun yang ditampilkan

    if ($data_result->num_rows > 0) {
        while ($row = $data_result->fetch_assoc()) {
            $tahun_list[] = $row['tahun_akreditasi']; // Debug: collect tahun

            $data[] = [
                'no' => $no++,
                'id_mapping_validasi' => $row['id_mapping_validasi'],
                'sekolah_id' => $row['sekolah_id'],
                'kd_asesor1' => $row['kd_asesor1'],
                'kd_asesor2' => $row['kd_asesor2'],
                'tahun_akreditasi' => $row['tahun_akreditasi'],
                'tahap' => $row['tahap'],
                'npsn' => $row['npsn'] ?: '-',
                'nama_sekolah' => $row['nama_sekolah'] ?: '-',
                'nm_jenjang' => $row['nm_jenjang'] ?: '-',
                'nm_kota' => $row['nm_kota'] ?: '-',
                'nia1' => $row['nia1'] ?: '-',
                'nm_asesor1' => $row['nm_asesor1'] ?: '-',
                'nia2' => $row['nia2'] ?: '-',
                'nm_asesor2' => $row['nm_asesor2'] ?: '-',
                'aksi' => '
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-info btn-sm" onclick="detailMappingValidasi(' . $row['id_mapping_validasi'] . ')" title="Detail">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                '
            ];
        }
    }

    // Debug: Log tahun yang ditampilkan
    $unique_tahun = array_unique($tahun_list);
    error_log("Get Data Validasi - Tahun yang ditampilkan: " . implode(', ', $unique_tahun));

    // Response for DataTables
    $response = [
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $filtered_records,
        'data' => $data
    ];

    // Set proper content type
    header('Content-Type: application/json');
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log("Get Data Validasi Error: " . $e->getMessage());
    header('Content-Type: application/json');
    echo json_encode([
        'draw' => $draw,
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => 'Terjadi kesalahan saat memuat data: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
