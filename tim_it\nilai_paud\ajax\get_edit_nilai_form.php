<?php
// Start session first
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include files
require_once '../../../koneksi.php';

// Simple session check
if (!isset($_SESSION['kd_user']) || empty($_SESSION['kd_user'])) {
    echo json_encode(['success' => false, 'message' => 'Session tidak valid']);
    exit;
}

// Simple level check
if (!isset($_SESSION['level']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['success' => false, 'message' => 'Aks<PERSON> ditolak']);
    exit;
}

// Check provinsi_id
if (!isset($_SESSION['provinsi_id']) || empty($_SESSION['provinsi_id'])) {
    echo json_encode(['success' => false, 'message' => 'Data provinsi tidak ditemukan']);
    exit;
}

// Cek parameter
if (!isset($_POST['id_hasil_akreditasi'])) {
    echo json_encode(['success' => false, 'message' => 'Parameter tidak lengkap']);
    exit;
}

$id_hasil_akreditasi = intval($_POST['id_hasil_akreditasi']);

// Get provinsi_id from session
$provinsi_id = $_SESSION['provinsi_id'];

try {
    // Test koneksi database terlebih dahulu
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Get data hasil akreditasi yang akan diedit - query sederhana dulu
    $query = "SELECT ha.*, s.npsn, s.nama_sekolah
              FROM hasil_akreditasi ha
              LEFT JOIN sekolah s ON ha.sekolah_id = s.sekolah_id
              WHERE ha.id_hasil_akreditasi = ? AND s.provinsi_id = ?";

    $stmt = $conn->prepare($query);
    if (!$stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }

    $stmt->bind_param("ii", $id_hasil_akreditasi, $provinsi_id);
    if (!$stmt->execute()) {
        throw new Exception("Execute failed: " . $stmt->error);
    }

    $result = $stmt->get_result();
    if (!$result) {
        throw new Exception("Get result failed: " . $stmt->error);
    }
    
    if ($result->num_rows == 0) {
        echo json_encode(['success' => false, 'message' => 'Data tidak ditemukan']);
        exit;
    }
    
    $data = $result->fetch_assoc();

    // Tambahkan data yang hilang dengan query terpisah
    $jenjang_query = "SELECT nm_jenjang FROM jenjang WHERE jenjang_id = (SELECT jenjang_id FROM sekolah WHERE sekolah_id = ?)";
    $jenjang_stmt = $conn->prepare($jenjang_query);
    $jenjang_stmt->bind_param("i", $data['sekolah_id']);
    $jenjang_stmt->execute();
    $jenjang_result = $jenjang_stmt->get_result();
    $jenjang_data = $jenjang_result->fetch_assoc();
    $data['nm_jenjang'] = $jenjang_data ? $jenjang_data['nm_jenjang'] : '';

    $kota_query = "SELECT nm_kota FROM kab_kota WHERE kota_id = (SELECT kota_id FROM sekolah WHERE sekolah_id = ?)";
    $kota_stmt = $conn->prepare($kota_query);
    $kota_stmt->bind_param("i", $data['sekolah_id']);
    $kota_stmt->execute();
    $kota_result = $kota_stmt->get_result();
    $kota_data = $kota_result->fetch_assoc();
    $data['nm_kota'] = $kota_data ? $kota_data['nm_kota'] : '';

    $prog_query = "SELECT nm_prog_ahli FROM jurusan_2_prog_ahli WHERE id_prog_ahli = ?";
    $prog_stmt = $conn->prepare($prog_query);
    $prog_stmt->bind_param("i", $data['id_prog_ahli']);
    $prog_stmt->execute();
    $prog_result = $prog_stmt->get_result();
    $prog_data = $prog_result->fetch_assoc();
    $data['nm_prog_ahli'] = $prog_data ? $prog_data['nm_prog_ahli'] : '';

    // Get dropdown options untuk prog_ahli
    $query_prog_options = "SELECT id_prog_ahli, nm_prog_ahli FROM jurusan_2_prog_ahli
                           WHERE id_prog_ahli IN (77,79,80,82,83,84)
                           ORDER BY nm_prog_ahli";
    $result_prog_options = $conn->query($query_prog_options);
    $prog_ahli_options = [];
    while ($row = $result_prog_options->fetch_assoc()) {
        $prog_ahli_options[] = $row;
    }

    // Format tanggal untuk input date
    $tgl_sk_formatted = '';
    if ($data['tgl_sk_penetapan'] && $data['tgl_sk_penetapan'] != '0000-00-00') {
        $tgl_sk_formatted = $data['tgl_sk_penetapan'];
    }

    echo json_encode([
        'success' => true,
        'data' => $data,
        'prog_ahli_options' => $prog_ahli_options,
        'tgl_sk_formatted' => $tgl_sk_formatted
    ]);
    
} catch (Exception $e) {
    error_log("Error in get_edit_nilai_form.php: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    echo json_encode(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
}

$conn->close();
?>
