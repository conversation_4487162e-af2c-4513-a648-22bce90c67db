<?php
/**
 * AJAX handler untuk mendapatkan dropdown options
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }

    $response_data = [];

    // Get dropdown program keahlian sesuai requirement
    $prog_ahli_query = "SELECT * FROM jurusan_2_prog_ahli 
                        WHERE id_prog_ahli = '77' OR id_prog_ahli = '79' OR id_prog_ahli = '80' 
                           OR id_prog_ahli = '82' OR id_prog_ahli = '83' OR id_prog_ahli = '84' 
                        ORDER BY kd_prog_ahli ASC";
    
    $result_prog_ahli = $conn->query($prog_ahli_query);

    $prog_ahli_data = [];
    while ($row = $result_prog_ahli->fetch_assoc()) {
        $prog_ahli_data[] = [
            'id_prog_ahli' => $row['id_prog_ahli'],
            'nm_prog_ahli' => $row['nm_prog_ahli'],
            'kd_prog_ahli' => $row['kd_prog_ahli']
        ];
    }

    $response_data['prog_ahli'] = $prog_ahli_data;

    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Dropdown options berhasil dimuat',
        'data' => $response_data
    ];

    echo json_encode($response);

} catch (Exception $e) {
    // Log error
    error_log("Get Dropdown Options Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
