<?php
session_start();
require_once '../../../koneksi.php';

// Cek session dan level
if (!isset($_SESSION['kd_user']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Ambil provinsi_id dari session
$provinsi_id = $_SESSION['provinsi_id'];

try {
    // Cek koneksi database
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Validasi input
    if (!isset($_POST['sk_akreditasi_id'])) {
        echo json_encode(['success' => false, 'message' => 'Parameter tidak lengkap']);
        exit;
    }

    $sk_akreditasi_id = intval($_POST['sk_akreditasi_id']);

    if ($sk_akreditasi_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'Parameter tidak valid']);
        exit;
    }

    // Ambil data SK berdasarkan ID dan provinsi
    $query = "SELECT sk_akreditasi_id,
                     no_sk_akreditasi,
                     tgl_sk_akreditasi,
                     tentang,
                     tahun_akreditasi,
                     nama_file,
                     nm_lembaga
              FROM e_arsip_sk_akreditasi 
              WHERE sk_akreditasi_id = $sk_akreditasi_id 
                AND provinsi_id = $provinsi_id";
    
    $result = $conn->query($query);

    if (!$result) {
        throw new Exception("Query failed: " . $conn->error);
    }

    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Data SK tidak ditemukan atau tidak memiliki akses']);
        exit;
    }

    $data = $result->fetch_assoc();

    echo json_encode([
        'success' => true,
        'data' => $data
    ]);

} catch (Exception $e) {
    error_log("Get SK Detail Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
}

$conn->close();
?>
