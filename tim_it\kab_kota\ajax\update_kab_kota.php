<?php
/**
 * AJAX handler untuk update data kabupaten/kota
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    $required_fields = ['id_kota', 'kota_id', 'nm_kota'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            throw new Exception("Field {$field} harus diisi");
        }
    }
    
    // Sanitasi input
    $id_kota = intval($_POST['id_kota']);
    $kota_id = trim($_POST['kota_id']);
    $nm_kota = trim($_POST['nm_kota']);
    $kd_user = isset($_POST['kd_user']) ? trim($_POST['kd_user']) : '';
    
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Validasi panjang field
    if (strlen($kota_id) > 10) {
        throw new Exception('Kode kabupaten/kota maksimal 10 karakter');
    }
    
    if (strlen($nm_kota) > 50) {
        throw new Exception('Nama kabupaten/kota maksimal 50 karakter');
    }
    
    if (strlen($kd_user) > 25) {
        throw new Exception('Kode user maksimal 25 karakter');
    }
    
    // Cek apakah data kabupaten/kota ada dan sesuai dengan session user
    $check_query = "SELECT id_kota, kota_id, nm_kota FROM kab_kota WHERE id_kota = ? AND provinsi_id = ?";
    $stmt_check = $conn->prepare($check_query);
    $stmt_check->bind_param("ii", $id_kota, $provinsi_id_session);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();
    
    if ($result_check->num_rows == 0) {
        throw new Exception('Data kabupaten/kota tidak ditemukan atau Anda tidak memiliki akses');
    }
    
    $existing_data = $result_check->fetch_assoc();
    
    // Cek duplikasi kota_id jika berubah
    if ($kota_id !== $existing_data['kota_id']) {
        $check_kota_id = "SELECT id_kota FROM kab_kota WHERE kota_id = ? AND id_kota != ?";
        $stmt_check_kota = $conn->prepare($check_kota_id);
        $stmt_check_kota->bind_param("si", $kota_id, $id_kota);
        $stmt_check_kota->execute();
        $result_check_kota = $stmt_check_kota->get_result();
        
        if ($result_check_kota->num_rows > 0) {
            throw new Exception('Kode kabupaten/kota sudah digunakan, gunakan kode yang berbeda');
        }
    }
    
    // Begin transaction
    $conn->autocommit(false);
    
    // Query update dengan filter session
    $sql = "UPDATE kab_kota SET 
                kota_id = ?,
                nm_kota = ?,
                kd_user = ?
            WHERE id_kota = ? AND provinsi_id = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sssii", 
        $kota_id, $nm_kota, $kd_user, $id_kota, $provinsi_id_session
    );
    
    if (!$stmt->execute()) {
        throw new Exception('Gagal mengupdate data kabupaten/kota: ' . $stmt->error);
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data kabupaten/kota "' . $nm_kota . '" berhasil diupdate',
        'data' => [
            'id_kota' => $id_kota,
            'kota_id' => $kota_id,
            'nm_kota' => $nm_kota,
            'kd_user' => $kd_user
        ]
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Rollback transaction jika ada error
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Update Kab Kota Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
