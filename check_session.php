<?php
// File untuk mengecek session dan hak akses
// Include file ini di setiap halaman yang memerlukan autentikasi

// Start session jika belum dimulai
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Fungsi untuk cek apakah user sudah login
function isLoggedIn() {
    return isset($_SESSION['kd_user']) && !empty($_SESSION['kd_user']);
}

// Fungsi untuk cek level user
function checkUserLevel($required_level) {
    if (!isLoggedIn()) {
        return false;
    }
    
    if (is_array($required_level)) {
        return in_array($_SESSION['level'], $required_level);
    } else {
        return $_SESSION['level'] === $required_level;
    }
}

// Fungsi untuk redirect jika tidak ada akses
function requireLogin($redirect_url = 'login.php') {
    if (!isLoggedIn()) {
        $_SESSION['error_message'] = "Silakan login terlebih dahulu untuk mengakses halaman ini.";
        header('Location: ' . $redirect_url);
        exit();
    }
}

// Fungsi untuk require level tertentu
function requireLevel($required_level, $redirect_url = 'login.php') {
    requireLogin($redirect_url);
    
    if (!checkUserLevel($required_level)) {
        $_SESSION['error_message'] = "Anda tidak memiliki akses untuk halaman ini.";
        
        // Redirect ke dashboard sesuai level user
        switch ($_SESSION['level']) {
            case 'Staff IT':
                header('Location: tim_it/dashboard/dashboard.php');
                break;
            case 'Asesor':
                header('Location: asesor/dashboard/dashboard.php');
                break;
            case 'Sekolah':
                header('Location: sekolah/dashboard/dashboard.php');
                break;
            default:
                header('Location: ' . $redirect_url);
                break;
        }
        exit();
    }
}

// Fungsi untuk mendapatkan informasi user yang sedang login
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    return [
        'kd_user' => $_SESSION['kd_user'],
        'nm_user' => $_SESSION['nm_user'],
        'level' => $_SESSION['level'],
        'sebagai_2' => $_SESSION['sebagai_2'],
        'kota_id' => $_SESSION['kota_id'],
        'provinsi_id' => $_SESSION['provinsi_id']
    ];
}

// Fungsi untuk cek timeout session (opsional)
function checkSessionTimeout($timeout_minutes = 120) {
    if (isLoggedIn()) {
        if (isset($_SESSION['login_time'])) {
            $elapsed_time = time() - $_SESSION['login_time'];
            $timeout_seconds = $timeout_minutes * 60;
            
            if ($elapsed_time > $timeout_seconds) {
                session_unset();
                session_destroy();
                session_start();
                $_SESSION['error_message'] = "Session Anda telah berakhir. Silakan login kembali.";
                header('Location: login.php');
                exit();
            } else {
                // Update login time untuk extend session
                $_SESSION['login_time'] = time();
            }
        }
    }
}

// Fungsi untuk auto-login dari cookie remember me
function checkRememberMe() {
    if (!isLoggedIn() && isset($_COOKIE['sim4k_remember'])) {
        require_once 'koneksi.php';
        
        try {
            $cookie_data = base64_decode($_COOKIE['sim4k_remember']);
            $parts = explode('|', $cookie_data);
            
            if (count($parts) == 2) {
                $kd_user = $parts[0];
                $username = $parts[1];
                
                // Verifikasi user masih valid
                $sql = "SELECT kd_user, nm_user, level, sebagai_2, kota_id, provinsi_id, status_keaktifan_id
                        FROM user
                        WHERE kd_user = ? AND username = ? AND status_keaktifan_id = 1 AND soft_delete = 1";
                
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ss", $kd_user, $username);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result->num_rows == 1) {
                    $user = $result->fetch_assoc();
                    
                    // Set session
                    $_SESSION['kd_user'] = $user['kd_user'];
                    $_SESSION['nm_user'] = $user['nm_user'];
                    $_SESSION['level'] = $user['level'];
                    $_SESSION['sebagai_2'] = $user['sebagai_2'];
                    $_SESSION['kota_id'] = $user['kota_id'];
                    $_SESSION['provinsi_id'] = $user['provinsi_id'];
                    $_SESSION['login_time'] = time();
                }
            }
        } catch (Exception $e) {
            // Hapus cookie jika error
            setcookie('sim4k_remember', '', time() - 3600, "/");
            error_log("Remember me error: " . $e->getMessage());
        }
    }
}

// Auto-check remember me jika dipanggil
if (!isLoggedIn()) {
    checkRememberMe();
}

// Auto-check session timeout
checkSessionTimeout();
?>
