<?php
/**
 * AJAX handler untuk validasi NPSN dan mendapatkan data sekolah
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    if (!isset($_POST['npsn']) || empty(trim($_POST['npsn']))) {
        throw new Exception('NPSN tidak boleh kosong');
    }
    
    $npsn = trim($_POST['npsn']);
    
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Escape NPSN untuk keamanan
    $npsn_escaped = $conn->real_escape_string($npsn);
    
    // Query untuk mencari sekolah berdasarkan NPSN dengan filter rumpun dan provinsi
    $sekolah_query = "SELECT s.sekolah_id, s.npsn, s.nama_sekolah, j.nm_jenjang, k.nm_kota
                      FROM sekolah s
                      LEFT JOIN jenjang j ON s.jenjang_id = j.id_jenjang
                      LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
                      WHERE s.npsn = '$npsn_escaped' 
                        AND s.rumpun = 'kesetaraan' 
                        AND s.provinsi_id = $provinsi_id_session
                        AND s.soft_delete = '1'";
    
    $result_sekolah = $conn->query($sekolah_query);
    
    if ($result_sekolah->num_rows == 0) {
        throw new Exception('NPSN tidak ditemukan atau bukan sekolah kesetaraan di provinsi Anda');
    }
    
    $sekolah_data = $result_sekolah->fetch_assoc();
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'NPSN valid',
        'data' => [
            'sekolah_id' => $sekolah_data['sekolah_id'],
            'npsn' => $sekolah_data['npsn'],
            'nama_sekolah' => $sekolah_data['nama_sekolah'],
            'nm_jenjang' => $sekolah_data['nm_jenjang'],
            'nm_kota' => $sekolah_data['nm_kota']
        ]
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Log error
    error_log("Validate NPSN Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
