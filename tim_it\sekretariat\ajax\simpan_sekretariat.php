<?php
/**
 * AJAX handler untuk menyimpan data sekretariat baru dengan sinkronisasi ke tabel user
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    $required_fields = ['nm_staff', 'kota_id', 'jk', 'jabatan', 'username', 'password'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            throw new Exception("Field {$field} harus diisi");
        }
    }

    // Validasi khusus untuk status_keaktifan_id (boleh "0")
    if (!isset($_POST['status_keaktifan_id']) || $_POST['status_keaktifan_id'] === '') {
        throw new Exception("Field status_keaktifan_id harus diisi");
    }
    
    // Sanitize dan ambil data dari POST
    $kd_sekretariat = isset($_POST['kd_sekretariat']) && !empty(trim($_POST['kd_sekretariat'])) ? trim($_POST['kd_sekretariat']) : uniqid();
    $nm_staff = trim($_POST['nm_staff']);
    $ktp = isset($_POST['ktp']) ? trim($_POST['ktp']) : '';
    $unit_kerja = isset($_POST['unit_kerja']) ? trim($_POST['unit_kerja']) : '';
    $kota_id = trim($_POST['kota_id']);
    $no_hp = isset($_POST['no_hp']) ? trim($_POST['no_hp']) : '';
    $no_wa = isset($_POST['no_wa']) ? trim($_POST['no_wa']) : '';
    $tempat_lahir = isset($_POST['tempat_lahir']) ? trim($_POST['tempat_lahir']) : '';
    $tgl_lahir = isset($_POST['tgl_lahir']) && !empty($_POST['tgl_lahir']) ? $_POST['tgl_lahir'] : '0000-00-00';
    $jabatan = isset($_POST['jabatan']) ? trim($_POST['jabatan']) : '';
    $pendidikan = isset($_POST['pendidikan']) ? trim($_POST['pendidikan']) : '';
    $jk = trim($_POST['jk']);
    $alamat_kantor = isset($_POST['alamat_kantor']) ? trim($_POST['alamat_kantor']) : '';
    $alamat_rumah = isset($_POST['alamat_rumah']) ? trim($_POST['alamat_rumah']) : '';
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $status_keaktifan_id = trim($_POST['status_keaktifan_id']);
    $sebab = isset($_POST['sebab']) ? trim($_POST['sebab']) : '';
    $no_urut = isset($_POST['no_urut']) && !empty($_POST['no_urut']) ? intval($_POST['no_urut']) : 0;
    $username = trim($_POST['username']);
    $password = trim($_POST['password']);
    
    // Data fixed
    $provinsi_id = $_SESSION['provinsi_id']; // Dari session user
    $kd_user = ''; // Kosong untuk sementara
    $soft_delete = '1'; // Aktif
    
    // Validasi jenis kelamin
    $valid_jk = ['Pria', 'Wanita'];
    if (!in_array($jk, $valid_jk)) {
        throw new Exception('Jenis kelamin harus Pria atau Wanita');
    }
    
    // Validasi status keaktifan
    $valid_status = ['0', '1', '2'];
    if (!in_array($status_keaktifan_id, $valid_status)) {
        throw new Exception('Status keaktifan tidak valid');
    }

    // Validasi jabatan
    $valid_jabatan = ['Staff IT', 'Tim PADA', 'Staff Keuangan', 'Staff Sekretariat Umum', 'Asesor'];
    if (!in_array($jabatan, $valid_jabatan)) {
        throw new Exception('Jabatan tidak valid');
    }
    
    // Validasi kota_id ada dan sesuai provinsi session
    $kota_id_check = $conn->real_escape_string($kota_id);
    $check_kota = "SELECT kota_id FROM kab_kota WHERE kota_id = '$kota_id_check' AND provinsi_id = $provinsi_id";
    $result_check_kota = $conn->query($check_kota);

    if ($result_check_kota->num_rows == 0) {
        throw new Exception('Kabupaten/Kota tidak valid atau tidak sesuai dengan provinsi Anda');
    }

    // Validasi duplikasi username
    $username_check = $conn->real_escape_string($username);
    $check_username = "SELECT id_user FROM user WHERE username = '$username_check'";
    $result_check_username = $conn->query($check_username);

    if ($result_check_username->num_rows > 0) {
        throw new Exception('Username sudah terdaftar dalam sistem');
    }
    
    // Escape semua data untuk keamanan
    $kd_sekretariat = $conn->real_escape_string($kd_sekretariat);
    $nm_staff = $conn->real_escape_string($nm_staff);
    $ktp = $conn->real_escape_string($ktp);
    $unit_kerja = $conn->real_escape_string($unit_kerja);
    $kota_id = $conn->real_escape_string($kota_id);
    $no_hp = $conn->real_escape_string($no_hp);
    $no_wa = $conn->real_escape_string($no_wa);
    $tempat_lahir = $conn->real_escape_string($tempat_lahir);
    $tgl_lahir = $conn->real_escape_string($tgl_lahir);
    $jabatan = $conn->real_escape_string($jabatan);
    $pendidikan = $conn->real_escape_string($pendidikan);
    $jk = $conn->real_escape_string($jk);
    $alamat_kantor = $conn->real_escape_string($alamat_kantor);
    $alamat_rumah = $conn->real_escape_string($alamat_rumah);
    $email = $conn->real_escape_string($email);
    $status_keaktifan_id = $conn->real_escape_string($status_keaktifan_id);
    $sebab = $conn->real_escape_string($sebab);
    $kd_user = $conn->real_escape_string($kd_user);
    $soft_delete = $conn->real_escape_string($soft_delete);
    $username = $conn->real_escape_string($username);
    $password = $conn->real_escape_string($password);

    // Begin transaction
    $conn->autocommit(false);

    // 1. Insert ke tabel sekretariat
    $sql_sekretariat = "INSERT INTO sekretariat (
                kd_sekretariat, nm_staff, ktp, unit_kerja, kota_id, provinsi_id,
                no_hp, no_wa, tempat_lahir, tgl_lahir, jabatan, pendidikan,
                jk, alamat_kantor, alamat_rumah, email, status_keaktifan_id,
                sebab, no_urut, kd_user, soft_delete
            ) VALUES (
                '$kd_sekretariat', '$nm_staff', '$ktp', '$unit_kerja', '$kota_id', $provinsi_id,
                '$no_hp', '$no_wa', '$tempat_lahir', '$tgl_lahir', '$jabatan', '$pendidikan',
                '$jk', '$alamat_kantor', '$alamat_rumah', '$email', '$status_keaktifan_id',
                '$sebab', $no_urut, '$kd_user', '$soft_delete'
            )";

    if (!$conn->query($sql_sekretariat)) {
        throw new Exception('Gagal menyimpan data sekretariat: ' . $conn->error);
    }

    $sekretariat_id = $conn->insert_id;
    
    // 2. Insert ke tabel user dengan mapping field
    $sql_user = "INSERT INTO user (
                kd_user, nm_user, status_keaktifan_id, provinsi_id, kota_id,
                username, password, level, sebagai, sebagai_2, soft_delete
            ) VALUES (
                '$kd_sekretariat', '$nm_staff', '$status_keaktifan_id', $provinsi_id, '$kota_id',
                '$username', '$password', '$jabatan', '$jabatan', 'Staff', '$soft_delete'
            )";

    if (!$conn->query($sql_user)) {
        throw new Exception('Gagal menyimpan data user: ' . $conn->error);
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data sekretariat "' . $nm_staff . '" berhasil disimpan dengan akun user',
        'data' => [
            'id_sekretariat' => $sekretariat_id,
            'kd_sekretariat' => $kd_sekretariat,
            'nm_staff' => $nm_staff,
            'username' => $username
        ]
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Rollback transaction jika ada error
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Simpan Sekretariat Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
