Berikut ini adalah struktur tabel "mapping_paud_validasi" :
id_mapping	int(11)
sekolah_id	int(11)
kd_asesor1	varchar(25)
kd_asesor2	varchar(25)
tahap	int(11)
tahun_akreditasi	varchar(4)
file_penjelasan_hasil_akreditasi	varchar(50)
nama_asesor_kpa	varchar(50)
catatan_penilaian_asesor_kpa	text
nama_asesor_Validasi_a	varchar(50)
catatan_penilaian_asesor_Validasi_a	text
nama_asesor_Validasi_b	varchar(50)
catatan_penilaian_asesor_Validasi_b	text
nama_validator	varchar(50)
nilai_validasi	varchar(50)
catatan_penilaian_validator	text
pha	text
provinsi_id	int(11)

Berikut ini struktur tabel "sekolah" :
sekolah_id int(11)
nama_sekolah varchar(100)
npsn varchar(30)
jenjang_id int(1)
rumpun varchar(20)
alamat text
tipe_sekolah_id int(1)
status_sekolah_id int(1)
provinsi_id int(2)
kota_id int(6)
desa_kelurahan varchar(200)
kecamatan varchar(100)
nama_kepsek varchar(100)
no_hp_kepsek varchar(50)
no_wa_kepsek varchar(50)
nama_operator varchar(50)
no_hp_operator varchar(20)
no_wa_operator varchar(20)
email varchar(100)
nama_yayasan varchar(200)
no_akte varchar(50)
tahun_berdiri varchar(4)
status_keaktifan_id varchar(1)
soft_delete rchar(1)

Berikut ini adalah struktur tabel "jenjang" :
id_jenjang int(11)
jenjang_id varchar(2)
nm_jenjang varchar(15)

Berikut ini struktur tabel "kab_kota" :
id_kota	int(11)	
kota_id	varchar(10)
nm_kota	varchar(50)
provinsi_id	int(11)
kd_user	varchar(25)

Berikut ini struktur tabel "asesor_1" :
id_asesor1int(11)
kd_asesor1 varchar(25)
nia1 varchar(20) 
nm_asesor1	varchar(100) 
ktp	varchar(20) 
unit_kerja	varchar(300) 
kota_id1 varchar(10)
provinsi_id	int(11)
no_sertifikat varchar(30)
no_hp varchar(50)
no_wa varchar(50)
tempat_lahir varchar(30)
tgl_lahir date
jabatan	varchar(50)
jabatan_struktural varchar(20)
pendidikan varchar(15)
jenjang_id int(11)
rumpun varchar(7)
grade varchar(1)
jk varchar(10)
alamat_kantor varchar(300)
alamat_rumah varchar(300)
email varchar(50)
thn_terbit_sertifikat	date
kegiatan varchar(50)
status_keaktifan_id	varchar(1)
sebab text
kd_user	varchar(50)
soft_delete	varchar(1)

Berikut ini tabel "asesor_2" :
id_asesor2 int(11)
kd_asesor2 varchar(25)
nia2 varchar(20)
nm_asesor2 varchar(100)
ktp varchar(20)
unit_kerja varchar(300)
kota_id2 varchar(10)
provinsi_id	int(11)
no_sertifikat varchar(30)
no_hp varchar(50)
no_wa varchar(50)
tempat_lahir varchar(30)
tgl_lahir date
jabatan	varchar(50)
jabatan_struktural	varchar(20)
pendidikan varchar(15)
jenjang_id int(11)
rumpun varchar(7)
grade varchar(1)
jk varchar(10)
alamat_kantor varchar(300)
alamat_rumah varchar(300)
email varchar(50)
thn_terbit_sertifikat date
kegiatan varchar(50)
status_keaktifan_id	varchar(1)
sebab text
kd_user	varchar(50)
soft_delete	varchar(1)

Berikut ini struktur tabel "mapping_paud_validasi_tahun" :
id_mapping_tahun	int(11)
nama_tahun	int(4)
provinsi_id	int(11)


buatlah modul "Mapping Asesor Validasi Paud" pada direktori tim_it/mapping_paud_validasi_2020/validasi_paud_2020.php, juga tersedia sub direktori ajax dan js, tabel header yang digunakan adalah :
NO,
NPSN (sekolah.npsn),
NAMA SEKOLAH (sekolah.nama_sekolah),
JENJANG (jenjang.nm_jenjang),
KAB/KOTA (kab_kota.nm_kota),
NIA VALIDATOR (asesor_1.nia1),
NAMA VALIDATOR (asesor_1.nm_asesor1),
NIA VERIFIKATOR (asesor.nia2),
NAMA VERIFIKATOR (asesor.nm_asesor2),
TAHUN AKREDITASI (mapping_paud_validasi.tahun_akreditasi),
TAHAP VALIDASI (mapping_paud_validasi.tahap),
AKSI.
Untuk kolom AKSI tampilkan tombol ikon detail (tombolnya saja dulu), sampai disini apakah anda sudah mengerti, ataukah ada yang perlu ditanyakan?

oh iya lupa, tambahkan
<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');
?>

// Include header
<?php include '../header.php'; ?>


<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

terakhir di paling bawah <!-- Include footer -->
<?php include '../footer.php'; ?>

where mapping_paud_validasi.provinsi_id=provinsi_id session login

tambahkan juga tombol "Input Data Mapping" dan tombol "Export Excel", "Import Excel" dan "Tahun Akreditasi" (buatkan tombolnya saja dulu)
=========================================================================================================

sekarang anda akan meng-aktifkan fungsi tombol "Input Data Mapping", jika tombol tersebut di-klik akan tampil modal "Input Data Mapping Validasi Paud" dengan form sebagi berikut

- label "NPSN Sekolah" (form input text) akan menyimpan ke field mapping_paud_validasi.sekolah_id
- "NIA Validator" (form input text) akan menyimpan ke field mapping_paud_validasi.kd_asesor1
- "Nia Verifikator (form input text) akan menyimpan ke field mapping_paud_validasi.kd_asesor2
- label Tahun Akreditasi (form input text) akan menyimpan ke field mapping_paud_validasi.tahun_akreditasi
- label Tahap Ke (form input text) akan menyimpan ke field mapping_paud_validasi.tahap
proses input data mapping ini terjadi tanpa refresh browser (tanpa reload halaman) pada tabel utama modal "Mapping Asesor Validasi PAUD"
where mapping_paud_validasi.provinsi_id=provinsi_id session login
where mapping_paud_validasi.tahun_akreditasi=mapping_paud_validasi_tahun.nama_tahun

sampai disini apakah ada yang ingin anda tanyakan?
============================================================================================================

Sekarang kita akan membuat modal "Detail Mapping Validasi Paud".

Ketika tombol icon detail yang ada di kolom "Aksi" pada tabel utama modul "Mapping Asesor Validasi PAUD" di-klik maka akan muncul sebuah modal dengan judul modal adalah "Detail Mapping Validasi Paud" dengan ukuran modal-xl.
Di dalam modal "Detail Mapping Validasi Paud" terdapat lima buah kolom, masing-masing kolom memiliki judul kolom dan isi kolom.

Kolom pertama dengan judul kolom "Data Sekolah" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:
- Label NPSN dengan kolom disebelahnya berisi field sekolah.npsn
- Label Nama Sekolah  dengan kolom disebelahnya berisi field sekolah.nama_sekolah
- Label Jenjang dengan kolom disebelahnya berisi field jenjang.nm_jenjang
- Label Kab/Kota dengan kolom disebelahnya berisi field kab_kota.nm_kota
- Label Nama Kepala Sekolah dengan kolom disebelahnya berisi field sekolah.nama_kepsek
- Label HP Kepala Sekolah dengan kolom disebelahnya berisi field sekolah.no_hp_kepsek
- No WA Kepala Sekolah dengan kolom disebelahnya berisi field sekolah.no_wa_kepsek

Kolom kedua dengan judul kolom "Data Asesor" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:
- Label Asesor A (colspan=2)
- Label NIA dengan kolom disebelahnya berisi field asesor_1.nia1
- Label Nama dengan kolom disebelahnya berisi field asesor_1.nm_asesor1
- Label No HP dengan kolom disebelahnya berisi field asesor_1.no_hp
- Label Kab/Kota dengan kolom disebelahnya berisi field kab_kota.nm_kota
- Label Asesor B (colspan=2)
- Label NIA dengan kolom disebelahnya berisi field asesor_2.nia2
- Label Nama dengan kolom disebelahnya berisi field asesor_2.nm_asesor2
- Label No HP dengan kolom disebelahnya berisi field asesor_2.no_hp
- Label Kab/Kota dengan kolom disebelahnya berisi field kab_kota.nm_kota

Kolom ketiga dengan judul kolom "Dokumen Unggahan" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:

- Label "File Pakta Integritas 1" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_validasi.file_pakta_integritas_1 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label "File Pakta Integritas 2" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_validasi.file_pakta_integritas_2 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label "File Berita Acara Validasi" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_validasi.file_berita_acara_Validasi ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label "File Temuan Hasil Validasi" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_validasi.file_temuan_hasil_Validasi ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label "File Absen Pembuka" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_validasi.file_absen_pembuka ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label "File Absen Penutup" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_validasi.file_absen_penutup ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label "File Foto Validasi" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_validasi.file_foto_Validasi ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label "File Laporan Individu 1" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_validasi.file_laporan_individu_1 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label "File Laporan Individu 2" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_validasi.file_laporan_individu_2 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label "File Laporan Kelompok" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_validasi.file_laporan_kelompok ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label "File Penjelasan Hasil Akreditasi" dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_paud_validasi.file_penjelasan_hasil_akreditasi ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah


Kolom keempat dengan judul kolom "Pelaksanaan Kegiatan" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:
- Label Tanggal Validasi Dimulai dengan kolom disebelahnya berisi mapping_paud_validasi.tgl_mulai_Validasi
- Label Tanggal Validasi Berakhir dengan kolom disebelahnya berisi field mapping_paud_validasi.tgl_akhir_Validasi
- Label No Surat Tugas Validasi dengan kolom disebelahnya berisi field mapping_paud_validasi.no_surat
- Label Tanggal Surat Tugas Validasi dengan kolom disebelahnya berisi field mapping_paud_validasi.tgl_surat

Kolom keempat dengan judul kolom "Aksi" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:
- Tombol "Edit Tanggal Kegiatan"
- Tombol "Edit Asesor Perubahan"
- Tombol "Download Surat Tugas Validasi"
=======================================================================================================

baik kawan, kita akan melanjutkan modul "Mapping Asesor Validasi Paud" ini, pada kolom "Dokumen Unggahan" yang ada di modal "Detail Mapping Validasi Paud" tertulis "Sudah Upload" dan "Belum Upload", jika yang tampil adalah tulisan "Sudah Upload" maka tulisan tersebut bisa di-klik sedangkan tulisan "Belum Upload" tidak bisa di-klik, untuk tulisan "Sudah Upload" jika di-klik akan membuka tab baru yang menampilkan file PDF. OK kita jelaskan lebih rinci lagi sebagai berikut:

- "File Pakta Integritas 1" jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_Validasi_paud/, untuk nama file diambil dari field mapping_paud_validasi.file_pakta_integritas_1

- "File Pakta Integritas 2" jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_Validasi_paud/, untuk nama file diambil dari field mapping_paud_validasi.file_pakta_integritas_2

- "File Berita Acara Validasi" jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_Validasi_paud/, untuk nama file diambil dari field mapping_paud_validasi.file_berita_acara_Validasi

- "File Temuan Hasil Validasi" jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_Validasi_paud/, untuk nama file diambil dari field mapping_paud_validasi.file_temuan_hasil_Validasi

- "File Absen Pembuka" jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_Validasi_paud/, untuk nama file diambil dari field mapping_paud_validasi.file_absen_pembuka

- "File Absen Penutup" jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_Validasi_paud/, untuk nama file diambil dari field mapping_paud_validasi.file_absen_penutup

- "File Foto Validasi" jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_Validasi_paud/, untuk nama file diambil dari field mapping_paud_validasi.file_foto_Validasi

- "File Laporan Individu 1" jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_Validasi_paud/, untuk nama file diambil dari field mapping_paud_validasi.file_laporan_individu_1

- "File Laporan Individu 2" jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_Validasi_paud/, untuk nama file diambil dari field mapping_paud_validasi.file_laporan_individu_2

- "File Laporan Kelompok" jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_Validasi_paud/, untuk nama file diambil dari field mapping_paud_validasi.file_laporan_kelompok

- "File Penjelasan Hasil Akreditasi" jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_Validasi_paud/, untuk nama file diambil dari field mapping_paud_validasi.file_penjelasan_hasil_akreditasi

Sampai disini apakah anda mengerti? silahkan bertanya jika belum paham
================================================================================================================


baik kawan, kita akan melanjutkan modul "Mapping Asesor Validasi Paud" ini, pada kolom "AKSI" yang ada di modal "Detail Mapping Validasi Paud" terdapat tombol "Edit Tanggal Kegiatan" jika tombol tersebut di-klik akan menampilkan sebuah modal dengan ukuran kecil yang berisi form untuk edit atau update field-field berikut ini:
- mapping_paud_validasi.tgl_mulai_Validasi dengan label form "Tanggal Validasi Dimulai"
- mapping_paud_validasi.tgl_akhir_Validasi dengan label form "Tanggal Validasi Berakhir"
- mapping_paud_validasi.no_surat dengan label form "No Surat Tugas Visiasi"
- mapping_paud_validasi.tgl_surat dengan label form "Tanggal Surat Tugas Validasi"
perlu saya sampaikan bahwa dengan tampilnya modal yang berisi form edit tersebut tidak serta merta menutup modal "Detail Mapping Validasi Paud", nantinya modal untuk edit/update tersebut berada di atas modal "Detail Mapping Validasi Paud" kemudian ketika di-klik tombol "Update Perubahan" maka secara otomatis "Tanggal Validasi Dimulai" dan "Tanggal Validasi Berakhir", "No Surat Tugas Visiasi" dan "Tanggal Surat Tugas Validasi" yang ada di kolom "PELAKSANAAN KEGIATAN" berubah tanpa refresh browser, sampai disini apakah anda mengerti dengan apa yang saya maksud? jika belum mengerti silahkan bertanya

================================================================================================================

baik kawan, kita akan melanjutkan modul "Mapping Asesor Validasi Paud" ini, pada kolom "AKSI" yang ada di modal "Detail Mapping Validasi Paud" terdapat tombol  "Edit Asesor Perubahan" dimana bisnis prosesnya sangat mirip dengan "Edit Tanggal Kegiatan" yaitu jika tombol "Edit Asesor Perubahan" di-klik akan menampilkan sebuah modal dengan ukuran kecil yang berisi form untuk edit atau update field mapping_paud_validasi.kd_asesor1 dan field mapping_paud_validasi.kd_asesor2, perlu saya sampaikan bahwa dengan tampilnya modal yang berisi form edit tersebut tidak serta merta menutup modal "Detail Mapping Validasi Paud", nantinya modal untuk edit/update field mapping_paud_validasi.kd_asesor1 dan field mapping_paud_validasi.kd_asesor2 berada di atas modal "Detail Mapping Validasi Paud" kemudian ketika di-klik tombol "Update Perubahan" maka secara otomatis "NIA", "Nama", "No. HP", "Kota" kedua asesor yang ada di kolom "DATA ASESOR" berubah tanpa refresh browser, sampai disini apakah anda mengerti dengan apa yang saya maksud? jika belum mengerti silahkan bertanya, Oh.. hampir lupa bahwa data yang diinput ke form "Edit Asesor Perubahan" adalah NIA salah satu dan atau kedua asesor
==============================================================================================================

baik kawan, kita akan melanjutkan modul "Mapping Asesor Validasi Paud" ini, pada kolom "AKSI" yang ada di modal "Detail Mapping Validasi Paud" terdapat tombol  "Download Surat Tugas Validasi" dimana jika tombol tersebut di-klik akan membuka tab baru yang menampilkan file PDF yang ada di direktori "tim_it\mapping_paud_Validasi_2020\mapping_paud_st_Validasi.php", silahkan anda perbaki dan sesuaikan kode sumber yang ada di file "mapping_paud_st_Validasi.php" dengan project ini, jika anda ingin mencontohnya bisa dilihat pada direktori "tim_it\mapping_dasmen_2020\mapping_st_Validasi.php"
=======================================================================================================

baiklah sobatku, sekarang kita akan memfungsikan/mengaktifkan tombol "Export Excel" di modul "Mapping Asesor Validasi Paud", anda gunakan library export excel sama seperti modul "Data Dasmen" yang ada di direktori "tim_it/dasmen/", data yang perlu di export di adalah:

No (autoincrement)
NPSN (sekolah.npsn)
NAMA SEKOLAH (sekolah.nama_sekolah)
JENJANG (jenjang.nm_jenjang)
KABUPATEN / KOTA (kab_kota.nm_kota domisili sekolah)
NIA ASESOR A (asesor_1.nia1)
NAMA ASESOR A (asesor_1.nm_asesor1)
KABUPATEN KOTA (kab_kota.nm_kota domisili asesor)
NIA ASESOR B (asesor_2.nia2)
NAMA ASESOR B (asesor_2.nm_asesor2)
KABUPATEN KOTA (kab_kota.nm_kota domisili asesor)
TAHUN AKREDITASI (mapping_paud_validasi.tahun_akreditasi)
TAHAP (mapping_paud_validasi.tahap)
TANGGAL Validasi (mapping_paud_validasi.tgl_mulai_Validasi)
NOMOR SURAT TUGAS (mapping_paud_validasi.no_surat)
TANGGAL SURAT TUGAS (mapping_paud_validasi.tgl_surat)

FILE PAKTA INTEGRITAS ASESOR A (jika mapping_paud_validasi.file_pakta_integritas_1 terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE PAKTA INTEGRITAS ASESOR B (jika mapping_paud_validasi.file_pakta_integritas_2 terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE BERITA ACARA Validasi (jika mapping_paud_validasi.file_berita_acara_Validasi terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE TEMUAN HASIL Validasi (jika mapping_paud_validasi.file_temuan_hasil_Validasi terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE ABSEN PEMBUKA (jika mapping_paud_validasi.file_absen_pembuka terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE ABSEN PENUTUP (jika mapping_paud_validasi.file_absen_penutup terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE FOTO Validasi (jika mapping_paud_validasi.file_foto_Validasi terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE LAPORAN INDIVIDU ASESOR A (jika mapping_paud_validasi.file_laporan_individu_1 terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE LAPORAN INDIVIDU ASESOR B (jika mapping_paud_validasi.file_laporan_individu_2 terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE LAPORAN KELOMPOK (jika mapping_paud_validasi.file_laporan_kelompok terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")

FILE PENJELASAN HASIL AKREDITASI (PHA) (jika mapping_paud_validasi.file_penjelasan_hasil_akreditasi terbaca kosong/empty maka tertulis "Belum Unggah", jika terbaca ada isinya/tidak kosong maka tertulis "Sudah Unggah")


data yang di download disesuaikan dengan filter where mapping_paud_validasi_tahun.nama_tahun

jika anda belum paham dengan maksud saya silahkan bertanya
================================================================================================================================
polanya sangat mirip dengan tombol "Tahun Akreditasi" di modul "Mapping Asesor Visitasi PAUD" di direktori "tim_it/mapping_paud_visitasi_2020/" namun yang akan di-update/edit adalah mapping_paud_validasi_tahun.nama_tahun dengan clause where mapping_paud_validasi_tahun.provinsi_id = provinsi_id session login

perubahan pada field mapping_paud_validasi_tahun.nama_tahun akan menyebabkan juga perubahan di tabel utama pada modul "Mapping Asesor Validasi PAUD" dengan tanpa refresh browseer (tanpa reload halaman). fungsi dari tombol "Tahun Akreditasi" ini sebagai filter untuk menampilkan data berdasarkan mapping_paud_validasi.tahun_akreditasi=mapping_paud_validasi_tahun.nama_tahun dengan query sebagai berikut
LEFT JOIN mapping_paud_validasi_tahun ON mapping_paud_validasi.tahun_akreditasi=mapping_paud_validasi_tahun.nama_tahun
WHERE mapping_paud_validasi.tahun_akreditasi = mapping_paud_validasi_tahun.nama_tahun
AND mapping_paud_validasi_tahun.provinsi_id = '$provinsi_id'
AND mapping_paud_validasi.provinsi_id = '$provinsi_id'