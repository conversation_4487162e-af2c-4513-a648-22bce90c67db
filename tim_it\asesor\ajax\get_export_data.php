<?php
/**
 * AJAX handler untuk mengambil semua data asesor untuk export Excel
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Query untuk mengambil data asesor aktif untuk export
    $query = "SELECT a.id_asesor, a.kd_asesor, a.nia, a.nm_asesor, a.ktp, a.unit_kerja,
                     a.no_sertifikat, a.no_hp, a.no_wa, a.tempat_lahir, a.tgl_lahir,
                     a.jabatan, a.jabatan_struktural, a.<PERSON>, a.rumpun, a.grade,
                     a.jk, a.alamat_kantor, a.alamat_rumah, a.email, a.thn_terbit_sertifikat,
                     a.kegiatan, a.status_keaktifan_id, a.sebab,
                     k.nm_kota, sk.nm_status, j.nm_jenjang
              FROM asesor a
              LEFT JOIN kab_kota k ON a.kota_id = k.kota_id
              LEFT JOIN status_keaktifan sk ON a.status_keaktifan_id = sk.status_keaktifan_id
              LEFT JOIN jenjang j ON a.jenjang_id = j.jenjang_id
              WHERE a.soft_delete = '1' AND a.status_keaktifan_id = '1' AND a.provinsi_id = $provinsi_id_session
              ORDER BY a.rumpun ASC, a.nm_asesor ASC";
    
    $result = $conn->query($query);
    if (!$result) {
        throw new Exception('Error executing query: ' . $conn->error);
    }
    
    // Siapkan data untuk response
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = [
            'id_asesor' => $row['id_asesor'],
            'kd_asesor' => $row['kd_asesor'],
            'nia' => $row['nia'],
            'nm_asesor' => $row['nm_asesor'],
            'jk' => $row['jk'],
            'nm_kota' => $row['nm_kota'],
            'unit_kerja' => $row['unit_kerja'],
            'rumpun' => $row['rumpun'],
            'nm_jenjang' => $row['nm_jenjang'],
            'nm_status' => $row['nm_status'],
            'ktp' => $row['ktp'],
            'tempat_lahir' => $row['tempat_lahir'],
            'tgl_lahir' => $row['tgl_lahir'],
            'jabatan' => $row['jabatan'],
            'jabatan_struktural' => $row['jabatan_struktural'],
            'pendidikan' => $row['pendidikan'],
            'grade' => $row['grade'],
            'no_sertifikat' => $row['no_sertifikat'],
            'thn_terbit_sertifikat' => $row['thn_terbit_sertifikat'],
            'no_hp' => $row['no_hp'],
            'no_wa' => $row['no_wa'],
            'email' => $row['email'],
            'alamat_kantor' => $row['alamat_kantor'],
            'alamat_rumah' => $row['alamat_rumah'],
            'kegiatan' => $row['kegiatan'],
            'sebab' => $row['sebab']
        ];
    }
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data asesor aktif berhasil diambil untuk export',
        'data' => $data,
        'total_records' => count($data)
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Log error
    error_log("Export Asesor Data Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => 'Terjadi kesalahan saat mengambil data: ' . $e->getMessage(),
        'data' => [],
        'total_records' => 0
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
