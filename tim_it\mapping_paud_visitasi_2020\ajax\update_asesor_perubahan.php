<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input ID mapping
    if (!isset($_POST['id_mapping']) || empty(trim($_POST['id_mapping']))) {
        throw new Exception('ID mapping harus diisi');
    }
    
    $id_mapping = intval($_POST['id_mapping']);
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Validasi ID mapping
    if ($id_mapping <= 0) {
        throw new Exception('ID mapping tidak valid');
    }
    
    // Sanitasi input (kedua field optional)
    $kd_asesor1 = isset($_POST['kd_asesor1']) && !empty(trim($_POST['kd_asesor1'])) 
                 ? $conn->real_escape_string(trim($_POST['kd_asesor1'])) 
                 : null;
    $kd_asesor2 = isset($_POST['kd_asesor2']) && !empty(trim($_POST['kd_asesor2'])) 
                 ? $conn->real_escape_string(trim($_POST['kd_asesor2'])) 
                 : null;
    
    // Validasi dual asesor tidak boleh sama (jika keduanya diisi)
    if ($kd_asesor1 && $kd_asesor2 && $kd_asesor1 === $kd_asesor2) {
        throw new Exception('Asesor A dan Asesor B tidak boleh sama');
    }
    
    // Cek apakah mapping exists dan milik provinsi yang benar
    $check_query = "SELECT id_mapping, sekolah_id, tahun_akreditasi FROM mapping_paud_visitasi 
                    WHERE id_mapping = ? AND provinsi_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("ii", $id_mapping, $provinsi_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows === 0) {
        throw new Exception('Data mapping tidak ditemukan atau Anda tidak memiliki akses');
    }
    
    $mapping_info = $check_result->fetch_assoc();
    
    // Validasi asesor exists jika diisi
    if ($kd_asesor1) {
        $check_asesor1 = "SELECT kd_asesor1 FROM asesor_1 
                          WHERE kd_asesor1 = ? AND provinsi_id = ? AND soft_delete = '1' AND status_keaktifan_id = '1'";
        $stmt_asesor1 = $conn->prepare($check_asesor1);
        $stmt_asesor1->bind_param("si", $kd_asesor1, $provinsi_id);
        $stmt_asesor1->execute();
        if ($stmt_asesor1->get_result()->num_rows === 0) {
            throw new Exception('Asesor A tidak ditemukan atau tidak aktif');
        }
    }
    
    if ($kd_asesor2) {
        $check_asesor2 = "SELECT kd_asesor2 FROM asesor_2 
                          WHERE kd_asesor2 = ? AND provinsi_id = ? AND soft_delete = '1' AND status_keaktifan_id = '1'";
        $stmt_asesor2 = $conn->prepare($check_asesor2);
        $stmt_asesor2->bind_param("si", $kd_asesor2, $provinsi_id);
        $stmt_asesor2->execute();
        if ($stmt_asesor2->get_result()->num_rows === 0) {
            throw new Exception('Asesor B tidak ditemukan atau tidak aktif');
        }
    }
    
    // Begin transaction
    $conn->autocommit(false);
    
    // Update asesor perubahan
    $update_query = "UPDATE mapping_paud_visitasi 
                     SET kd_asesor1 = ?, 
                         kd_asesor2 = ?
                     WHERE id_mapping = ? AND provinsi_id = ?";
    
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("ssii", 
        $kd_asesor1, $kd_asesor2, $id_mapping, $provinsi_id
    );
    
    if (!$update_stmt->execute()) {
        throw new Exception('Gagal mengupdate asesor perubahan: ' . $conn->error);
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Get updated data dengan detail asesor untuk response
    $select_query = "SELECT 
                        mp.kd_asesor1, mp.kd_asesor2,
                        a1.nia1, a1.nm_asesor1, a1.no_hp as no_hp_asesor1,
                        kk_a1.nm_kota as nm_kota_asesor1,
                        a2.nia2, a2.nm_asesor2, a2.no_hp as no_hp_asesor2,
                        kk_a2.nm_kota as nm_kota_asesor2
                     FROM mapping_paud_visitasi mp
                     LEFT JOIN asesor_1 a1 ON mp.kd_asesor1 = a1.kd_asesor1
                     LEFT JOIN kab_kota kk_a1 ON a1.kota_id1 = kk_a1.kota_id
                     LEFT JOIN asesor_2 a2 ON mp.kd_asesor2 = a2.kd_asesor2
                     LEFT JOIN kab_kota kk_a2 ON a2.kota_id2 = kk_a2.kota_id
                     WHERE mp.id_mapping = ? AND mp.provinsi_id = ?";
    $select_stmt = $conn->prepare($select_query);
    $select_stmt->bind_param("ii", $id_mapping, $provinsi_id);
    $select_stmt->execute();
    $select_result = $select_stmt->get_result();
    $updated_data = $select_result->fetch_assoc();
    
    // Log successful update
    error_log("Update Asesor Perubahan Success - ID: $id_mapping, " .
              "Asesor1: " . ($kd_asesor1 ?: 'NULL') . ", " .
              "Asesor2: " . ($kd_asesor2 ?: 'NULL') . ", " .
              "User: " . $_SESSION['nm_user']);
    
    echo json_encode([
        'success' => true,
        'message' => 'Asesor perubahan berhasil diupdate',
        'data' => $updated_data
    ]);
    
} catch (Exception $e) {
    // Rollback transaction
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Update Asesor Perubahan Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
