<?php
/**
 * AJAX handler untuk menampilkan form edit provinsi
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    if (!isset($_POST['id_provinsi']) || empty($_POST['id_provinsi'])) {
        throw new Exception('ID Provinsi tidak valid');
    }
    
    $id_provinsi = intval($_POST['id_provinsi']);

    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];

    // Query untuk mendapatkan data provinsi dengan filter session
    $sql = "SELECT * FROM provinsi WHERE id_provinsi = ? AND provinsi_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $id_provinsi, $provinsi_id_session);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        throw new Exception('Data provinsi tidak ditemukan');
    }
    
    $row = $result->fetch_assoc();
    
    // Query untuk mendapatkan dropdown kab/kota sesuai provinsi session
    $kota_query = "SELECT kota_id, nm_kota FROM kab_kota WHERE provinsi_id = ? ORDER BY nm_kota ASC";
    $stmt_kota = $conn->prepare($kota_query);
    $stmt_kota->bind_param("i", $provinsi_id_session);
    $stmt_kota->execute();
    $kota_result = $stmt_kota->get_result();
    
    ?>
    
    <!-- Hidden field untuk ID -->
    <input type="hidden" name="id_provinsi" value="<?php echo $row['id_provinsi']; ?>">
    <input type="hidden" name="provinsi_id" value="<?php echo $row['provinsi_id']; ?>">

    <div class="row">
        <!-- Kolom Kiri -->
        <div class="col-md-6">
            <div class="form-group">
                <label for="edit_nama_provinsi">Nama Provinsi <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="edit_nama_provinsi" name="nama_provinsi"
                       value="<?php echo htmlspecialchars($row['nama_provinsi']); ?>" required maxlength="50">
            </div>

            <div class="form-group">
                <label for="edit_kota_id">Kabupaten/Kota</label>
                <select class="form-control" id="edit_kota_id" name="kota_id">
                    <option value="">-- Pilih Kabupaten/Kota --</option>
                    <?php
                    while ($kota_row = $kota_result->fetch_assoc()) {
                        $selected = ($kota_row['kota_id'] == $row['kota_id']) ? 'selected' : '';
                        echo '<option value="' . $kota_row['kota_id'] . '" ' . $selected . '>' .
                             htmlspecialchars($kota_row['nm_kota']) . '</option>';
                    }
                    ?>
                </select>
            </div>
        </div>

        <!-- Kolom Kanan -->
        <div class="col-md-6">
            <div class="form-group">
                <label for="edit_nama_ketua_banp">Nama Ketua BAN PDM Provinsi</label>
                <input type="text" class="form-control" id="edit_nama_ketua_banp" name="nama_ketua_banp"
                       value="<?php echo htmlspecialchars($row['nama_ketua_banp']); ?>" maxlength="50">
            </div>

            <div class="form-group">
                <label for="edit_ttd_ketua_banp">TTD Ketua BAN PDM</label>
                <input type="text" class="form-control" id="edit_ttd_ketua_banp" name="ttd_ketua_banp"
                       value="<?php echo htmlspecialchars($row['ttd_ketua_banp']); ?>" maxlength="30">
            </div>
        </div>
    </div>
    
    <!-- Alamat Provinsi (Full Width) -->
    <div class="row">
        <div class="col-12">
            <div class="form-group">
                <label for="edit_alamat_provinsi">Alamat Provinsi</label>
                <textarea class="form-control" id="edit_alamat_provinsi" name="alamat_provinsi" 
                          rows="3" placeholder="Masukkan alamat lengkap provinsi"><?php echo htmlspecialchars($row['alamat_provinsi']); ?></textarea>
            </div>
        </div>
    </div>
    
    <?php
    
} catch (Exception $e) {
    echo '<div class="alert alert-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
