<?php
// Start session first
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include files
require_once '../../../koneksi.php';

// Simple session check
if (!isset($_SESSION['kd_user']) || empty($_SESSION['kd_user'])) {
    echo json_encode(['success' => false, 'message' => 'Session tidak valid']);
    exit;
}

// Simple level check
if (!isset($_SESSION['level']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['success' => false, 'message' => 'Aks<PERSON> ditolak']);
    exit;
}

// Check provinsi_id
if (!isset($_SESSION['provinsi_id']) || empty($_SESSION['provinsi_id'])) {
    echo json_encode(['success' => false, 'message' => 'Data provinsi tidak ditemukan']);
    exit;
}

// Cek parameter
$required_fields = ['id_hasil_akreditasi', 'id_prog_ahli', 'program', 'peringkat', 'status', 'tahun_akreditasi', 'tahun_berakhir'];
foreach ($required_fields as $field) {
    if (!isset($_POST[$field]) || trim($_POST[$field]) === '') {
        echo json_encode(['success' => false, 'message' => 'Field ' . $field . ' harus diisi']);
        exit;
    }
}

$id_hasil_akreditasi = intval($_POST['id_hasil_akreditasi']);
$id_prog_ahli = intval($_POST['id_prog_ahli']);
$nilai_akhir = isset($_POST['nilai_akhir']) && $_POST['nilai_akhir'] !== '' ? intval($_POST['nilai_akhir']) : 0;
$program = trim($_POST['program']);
$peringkat = trim($_POST['peringkat']);
$status = trim($_POST['status']);
$tahun_akreditasi = intval($_POST['tahun_akreditasi']);
$tahun_berakhir = intval($_POST['tahun_berakhir']);
// Handle tanggal SK penetapan
$tgl_sk_penetapan = isset($_POST['tgl_sk_penetapan']) && !empty($_POST['tgl_sk_penetapan']) ? $_POST['tgl_sk_penetapan'] : '';
$no_sk = isset($_POST['no_sk']) ? trim($_POST['no_sk']) : '';

// Get provinsi_id from session
$provinsi_id = $_SESSION['provinsi_id'];

try {
    // Cek apakah data exists dan milik provinsi yang sama
    $check_query = "SELECT ha.sekolah_id, s.provinsi_id
                    FROM hasil_akreditasi ha
                    LEFT JOIN sekolah s ON ha.sekolah_id = s.sekolah_id
                    WHERE ha.id_hasil_akreditasi = $id_hasil_akreditasi";
    $check_result = $conn->query($check_query);

    if (!$check_result || $check_result->num_rows == 0) {
        echo json_encode(['success' => false, 'message' => 'Data tidak ditemukan']);
        exit;
    }

    $check_data = $check_result->fetch_assoc();
    if ($check_data['provinsi_id'] != $provinsi_id) {
        echo json_encode(['success' => false, 'message' => 'Anda tidak memiliki akses untuk mengedit data ini']);
        exit;
    }
    
    // Validasi prog_ahli
    $valid_prog_ahli = [77, 79, 80, 82, 83, 84];
    if (!in_array($id_prog_ahli, $valid_prog_ahli)) {
        echo json_encode(['success' => false, 'message' => 'Program keahlian tidak valid']);
        exit;
    }

    // Validasi program paket
    $valid_program = ['Paket A', 'Paket B', 'Paket C', 'Satdik'];
    if (!in_array($program, $valid_program)) {
        echo json_encode(['success' => false, 'message' => 'Program paket tidak valid']);
        exit;
    }

    // Validasi peringkat
    $valid_peringkat = ['A', 'B', 'C', 'TT'];
    if (!in_array($peringkat, $valid_peringkat)) {
        echo json_encode(['success' => false, 'message' => 'Peringkat tidak valid']);
        exit;
    }
    
    // Validasi status
    $valid_status = ['Terakreditasi', 'Tidak Terakreditasi'];
    if (!in_array($status, $valid_status)) {
        echo json_encode(['success' => false, 'message' => 'Status tidak valid']);
        exit;
    }
    
    // Validasi tahun
    $current_year = date('Y');
    if ($tahun_akreditasi < 2000 || $tahun_akreditasi > ($current_year + 10)) {
        echo json_encode(['success' => false, 'message' => 'Tahun akreditasi tidak valid']);
        exit;
    }
    
    if ($tahun_berakhir < 2000 || $tahun_berakhir > ($current_year + 20)) {
        echo json_encode(['success' => false, 'message' => 'Tahun berakhir tidak valid']);
        exit;
    }
    
    if ($tahun_berakhir <= $tahun_akreditasi) {
        echo json_encode(['success' => false, 'message' => 'Tahun berakhir harus lebih besar dari tahun akreditasi']);
        exit;
    }
    
    // Edit bebas tanpa duplikasi check - user bebas mengubah data sesuai kebutuhan

    // Escape semua data untuk keamanan
    $program = $conn->real_escape_string($program);
    $peringkat = $conn->real_escape_string($peringkat);
    $status = $conn->real_escape_string($status);
    $no_sk = $conn->real_escape_string($no_sk);

    // Format tanggal untuk query
    $tgl_sk_insert = '';
    if (!empty($tgl_sk_penetapan)) {
        $tgl_sk_escaped = $conn->real_escape_string($tgl_sk_penetapan);
        $tgl_sk_insert = "'$tgl_sk_escaped'";
    } else {
        $tgl_sk_insert = 'NULL';
    }

    // Update data dengan query langsung
    $update_query = "UPDATE hasil_akreditasi SET
                     id_prog_ahli = $id_prog_ahli,
                     nilai_akhir = $nilai_akhir,
                     program = '$program',
                     peringkat = '$peringkat',
                     status = '$status',
                     tahun_akreditasi = $tahun_akreditasi,
                     tahun_berakhir = $tahun_berakhir,
                     tgl_sk_penetapan = $tgl_sk_insert,
                     no_sk = '$no_sk'
                     WHERE id_hasil_akreditasi = $id_hasil_akreditasi";

    if ($conn->query($update_query)) {
        echo json_encode([
            'success' => true,
            'message' => 'Data nilai akreditasi berhasil diperbarui'
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Gagal memperbarui data: ' . $conn->error]);
    }
    
} catch (Exception $e) {
    error_log("Update Nilai Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
}

$conn->close();
?>
