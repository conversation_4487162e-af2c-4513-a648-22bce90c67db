// Mapping Asesor Validasi Dasmen JavaScript

$(document).ready(function() {
    console.log('Mapping Validasi JS loaded');

    // Initialize DataTable
    initDataTable();

    // Event handlers untuk tombol-tombol
    initEventHandlers();
});

// Function untuk show alert
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `;

    $('#alert-container').html(alertHtml);

    // Auto hide after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

// Function untuk initialize DataTable
function initDataTable() {
    $('#table-mapping-validasi').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: 'ajax/get_data_validasi.php',
            type: 'POST'
        },
        columns: [
            { data: 'no', orderable: false, searchable: false },
            { data: 'npsn' },
            { data: 'nama_sekolah' },
            { data: 'nm_jenjang' },
            { data: 'nm_kota' },
            { data: 'nia1' },
            { data: 'nm_asesor1' },
            { data: 'nia2' },
            { data: 'nm_asesor2' },
            { data: 'tahun_akreditasi' },
            { data: 'tahap' },
            { data: 'aksi', orderable: false, searchable: false }
        ],
        order: [[1, 'asc']],
        pageLength: 10,
        responsive: true,
        language: {
            processing: "Memuat data...",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            zeroRecords: "Data tidak ditemukan",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(difilter dari _MAX_ total data)",
            search: "Cari:",
            paginate: {
                first: "Pertama",
                last: "Terakhir",
                next: "Selanjutnya",
                previous: "Sebelumnya"
            }
        }
    });
}

function initEventHandlers() {
    // Event handler untuk tombol Input Mapping Validasi
    $(document).on('click', '#btn-input-mapping', function(e) {
        e.preventDefault();
        console.log('Input Mapping Validasi clicked');
        openInputMappingModal();
    });

    // Event handler untuk form submit input mapping
    $(document).on('submit', '#form-input-mapping', function(e) {
        e.preventDefault();
        submitInputMapping();
    });

    // Event handler untuk lookup NPSN
    $(document).on('blur', '#npsn_sekolah', function() {
        const npsn = $(this).val().trim();
        if (npsn) {
            lookupNPSN(npsn);
        }
    });

    // Event handler untuk lookup NIA Asesor 1
    $(document).on('blur', '#nia_asesor1', function() {
        const nia = $(this).val().trim();
        if (nia) {
            lookupNIA(nia, 1);
        }
    });

    // Event handler untuk lookup NIA Asesor 2
    $(document).on('blur', '#nia_asesor2', function() {
        const nia = $(this).val().trim();
        if (nia) {
            lookupNIA(nia, 2);
        }
    });
    
    // Event handler untuk tombol Export Excel
    $(document).on('click', '#btn-export-excel', function(e) {
        e.preventDefault();
        console.log('Export Excel clicked');
        showAlert('info', 'Fitur Export Excel akan segera tersedia');
    });
    
    // Event handler untuk tombol Import Excel
    $(document).on('click', '#btn-import-excel', function(e) {
        e.preventDefault();
        console.log('Import Excel clicked');
        showAlert('info', 'Fitur Import Excel akan segera tersedia');
    });
    
    // Event handler untuk tombol Tahun Akreditasi
    $(document).on('click', '#btn-tahun-akreditasi', function(e) {
        e.preventDefault();
        console.log('Tahun Akreditasi clicked');
        openTahunAkreditasiModal();
    });

    // Event handler untuk form submit tahun akreditasi
    $(document).on('submit', '#form-tahun-akreditasi', function(e) {
        e.preventDefault();
        submitTahunAkreditasi();
    });

    // Event handler untuk tombol Edit Tanggal Validasi di modal detail
    $(document).on('click', '#btn-edit-tanggal-validasi', function(e) {
        e.preventDefault();
        console.log('Edit Tanggal Validasi clicked');
        openEditTanggalValidasiModal();
    });

    // Event handler untuk form submit edit tanggal validasi
    $(document).on('submit', '#form-edit-tanggal-validasi', function(e) {
        e.preventDefault();
        submitEditTanggalValidasi();
    });

    // Event handler untuk tombol Hapus di modal detail
    $(document).on('click', '#btn-hapus-mapping', function(e) {
        e.preventDefault();
        console.log('Hapus Mapping clicked');
        openKonfirmasiHapusMappingModal();
    });

    // Event handler untuk konfirmasi hapus mapping
    $(document).on('click', '#btn-konfirmasi-hapus-mapping', function(e) {
        e.preventDefault();
        submitHapusMapping();
    });

    // Event handler untuk tombol Edit Asesor Perubahan di modal detail
    $(document).on('click', '#btn-edit-asesor-perubahan', function(e) {
        e.preventDefault();
        console.log('Edit Asesor Perubahan clicked');
        openEditAsesorPerubahanModal();
    });

    // Event handler untuk form submit edit asesor perubahan
    $(document).on('submit', '#form-edit-asesor-perubahan', function(e) {
        e.preventDefault();
        submitEditAsesorPerubahan();
    });

    // Event handler untuk lookup NIA Asesor 1 di form edit
    $(document).on('blur', '#edit_nia_asesor1', function() {
        const nia = $(this).val().trim();
        if (nia) {
            lookupNIAForEdit(nia, 1);
        } else {
            $('#info-asesor1').hide();
            $('#edit_kd_asesor1').val('');
        }
    });

    // Event handler untuk lookup NIA Asesor 2 di form edit
    $(document).on('blur', '#edit_nia_asesor2', function() {
        const nia = $(this).val().trim();
        if (nia) {
            lookupNIAForEdit(nia, 2);
        } else {
            $('#info-asesor2').hide();
            $('#edit_kd_asesor2').val('');
        }
    });

    // Event handler untuk tombol Download Surat Tugas Validasi di modal detail
    $(document).on('click', '#btn-download-surat-validasi', function(e) {
        e.preventDefault();
        console.log('Download Surat Tugas Validasi clicked');
        downloadSuratTugasValidasi();
    });
}

// Function untuk detail mapping validasi
function detailMappingValidasi(mappingValidasiId) {
    console.log('Detail mapping validasi ID:', mappingValidasiId);

    // Load detail data
    $.ajax({
        url: 'ajax/get_detail_mapping.php',
        type: 'GET',
        data: { id: mappingValidasiId },
        dataType: 'json',
        beforeSend: function() {
            // Show loading in modal
            $('#modal-detail-mapping-validasi .modal-body').html('<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><br>Memuat data...</div>');
            $('#modal-detail-mapping-validasi').modal('show');
        },
        success: function(response) {
            if (response.success) {
                populateDetailModal(response.data);
            } else {
                showAlert('error', response.message || 'Gagal memuat detail mapping validasi');
                $('#modal-detail-mapping-validasi').modal('hide');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading detail mapping validasi:', error);
            showAlert('error', 'Terjadi kesalahan saat memuat detail mapping validasi');
            $('#modal-detail-mapping-validasi').modal('hide');
        }
    });
}

// Function untuk populate modal detail dengan data
function populateDetailModal(data) {
    console.log('Populating detail modal with data:', data);

    // Restore modal body content
    restoreModalDetailContent();

    // Store data in modal for other functions
    $('#modal-detail-mapping-validasi').data('mapping-data', data);

    // Populate Data Sekolah
    $('#detail-npsn').text(data.npsn);
    $('#detail-nama-sekolah').text(data.nama_sekolah);
    $('#detail-jenjang').text(data.nm_jenjang);
    $('#detail-kab-kota').text(data.nm_kota);
    $('#detail-nama-kepsek').text(data.nama_kepsek);
    $('#detail-hp-kepsek').text(data.no_hp_kepsek);
    $('#detail-wa-kepsek').text(data.no_wa_kepsek);

    // Populate Data Asesor
    $('#detail-nia1').text(data.nia1);
    $('#detail-nama-asesor1').text(data.nm_asesor1);
    $('#detail-hp-asesor1').text(data.hp_asesor1);
    $('#detail-kota-asesor1').text(data.kota_asesor1);
    $('#detail-nia2').text(data.nia2);
    $('#detail-nama-asesor2').text(data.nm_asesor2);
    $('#detail-hp-asesor2').text(data.hp_asesor2);
    $('#detail-kota-asesor2').text(data.kota_asesor2);

    // Populate Dokumen Unggahan dengan clickable badge
    $('#detail-pakta1').html(generateFileStatusBadge(data.file_pakta_integritas_1));
    $('#detail-pakta2').html(generateFileStatusBadge(data.file_pakta_integritas_2));
    $('#detail-ba1').html(generateFileStatusBadge(data.file_berita_acara_validasi_1));
    $('#detail-ba2').html(generateFileStatusBadge(data.file_berita_acara_validasi_2));

    // Populate Pelaksanaan Kegiatan
    $('#detail-tgl-mulai').text(data.tgl_mulai_validasi);
    $('#detail-tgl-akhir').text(data.tgl_akhir_validasi);
    $('#detail-no-surat').text(data.no_surat_validasi);
    $('#detail-tgl-surat').text(data.tgl_surat_validasi);
}

// Function untuk generate file status badge dengan clickable support
function generateFileStatusBadge(fileData) {
    if (fileData.clickable) {
        // Generate clickable badge untuk "Sudah Upload"
        return '<span class="' + fileData.class + '" style="cursor: pointer;" ' +
               'onclick="openFilePreview(\'' + fileData.file_url + '\', \'' + fileData.filename + '\')" ' +
               'title="Klik untuk melihat file: ' + fileData.filename + '">' +
               '<i class="fas fa-eye"></i> ' + fileData.text +
               '</span>';
    } else {
        // Generate non-clickable badge untuk "Belum Upload"
        return '<span class="' + fileData.class + '">' +
               '<i class="fas fa-times"></i> ' + fileData.text +
               '</span>';
    }
}

// Function untuk membuka file preview di tab baru
function openFilePreview(fileUrl, filename) {
    console.log('Opening file preview:', fileUrl);

    // Validasi file URL
    if (!fileUrl || fileUrl === '') {
        showAlert('error', 'URL file tidak valid');
        return;
    }

    // Validasi filename
    if (!filename || filename === '') {
        showAlert('error', 'Nama file tidak valid');
        return;
    }

    try {
        // Buka file di tab baru
        const newWindow = window.open(fileUrl, '_blank');

        // Check jika popup diblokir
        if (!newWindow || newWindow.closed || typeof newWindow.closed == 'undefined') {
            showAlert('warning', 'Popup diblokir oleh browser. Silakan izinkan popup untuk melihat file.');
        } else {
            console.log('File preview opened successfully:', filename);
        }
    } catch (error) {
        console.error('Error opening file preview:', error);
        showAlert('error', 'Terjadi kesalahan saat membuka file preview');
    }
}

// Function untuk membuka modal edit tanggal validasi
function openEditTanggalValidasiModal() {
    console.log('Opening edit tanggal validasi modal');

    // Ambil data mapping dari modal detail yang sedang terbuka
    const mappingData = $('#modal-detail-mapping-validasi').data('mapping-data');

    if (!mappingData) {
        showAlert('error', 'Data mapping tidak ditemukan. Silakan tutup dan buka kembali modal detail.');
        return;
    }

    // Reset form
    clearForm('#form-edit-tanggal-validasi');

    // Populate form dengan data existing
    $('#edit_id_mapping_validasi').val(mappingData.id_mapping_validasi);

    // Convert tanggal dari format display (dd/mm/yyyy) ke format input (yyyy-mm-dd)
    const tglMulai = convertDisplayDateToInputDate(mappingData.tgl_mulai_validasi);
    const tglAkhir = convertDisplayDateToInputDate(mappingData.tgl_akhir_validasi);

    $('#edit_tgl_mulai_validasi').val(tglMulai);
    $('#edit_tgl_akhir_validasi').val(tglAkhir);

    // Show modal dengan z-index yang lebih tinggi untuk nested modal
    $('#modal-edit-tanggal-validasi').css('z-index', 1060).modal('show');

    console.log('Edit modal opened with data:', {
        id: mappingData.id_mapping_validasi,
        tgl_mulai: tglMulai,
        tgl_akhir: tglAkhir
    });
}

// Function untuk submit edit tanggal validasi
function submitEditTanggalValidasi() {
    console.log('Submitting edit tanggal validasi');

    // Validasi form
    if (!validateForm('#form-edit-tanggal-validasi')) {
        return;
    }

    const formData = new FormData($('#form-edit-tanggal-validasi')[0]);

    $.ajax({
        url: 'ajax/update_tanggal_validasi.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        beforeSend: function() {
            toggleFormState('#form-edit-tanggal-validasi', true);
            showLoading('#form-edit-tanggal-validasi button[type="submit"]');
        },
        success: function(response) {
            if (response.success) {
                console.log('Tanggal validasi updated successfully:', response.data);

                // Tutup modal edit
                $('#modal-edit-tanggal-validasi').modal('hide');

                // Update display di modal detail tanpa refresh
                updateTanggalValidasiDisplay(response.data);

                // Update data di modal detail untuk konsistensi
                updateMappingDataInModal(response.data);

                // Silent success - no notification to avoid clutter
                console.log('Tanggal validasi display updated successfully');

            } else {
                showAlert('error', response.message || 'Gagal mengupdate tanggal validasi');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error submitting edit tanggal validasi:', error);
            showAlert('error', 'Terjadi kesalahan saat mengupdate tanggal validasi');
        },
        complete: function() {
            toggleFormState('#form-edit-tanggal-validasi', false);
            hideLoading('#form-edit-tanggal-validasi button[type="submit"]', '<i class="fas fa-save"></i> Update Perubahan');
        }
    });
}

// Function untuk convert tanggal dari format display (dd/mm/yyyy) ke format input (yyyy-mm-dd)
function convertDisplayDateToInputDate(displayDate) {
    if (!displayDate || displayDate === '-') {
        return '';
    }

    // Format display: dd/mm/yyyy
    const parts = displayDate.split('/');
    if (parts.length === 3) {
        // Return format: yyyy-mm-dd
        return parts[2] + '-' + parts[1].padStart(2, '0') + '-' + parts[0].padStart(2, '0');
    }

    return '';
}

// Function untuk update display tanggal validasi di modal detail
function updateTanggalValidasiDisplay(data) {
    console.log('Updating tanggal validasi display with:', data);

    // Update field di kolom Pelaksanaan Kegiatan
    $('#detail-tgl-mulai').text(data.tgl_mulai_validasi);
    $('#detail-tgl-akhir').text(data.tgl_akhir_validasi);

    // Add smooth animation effect
    $('#detail-tgl-mulai, #detail-tgl-akhir').addClass('text-success').delay(2000).queue(function() {
        $(this).removeClass('text-success').dequeue();
    });
}

// Function untuk update mapping data di modal untuk konsistensi
function updateMappingDataInModal(data) {
    const mappingData = $('#modal-detail-mapping-validasi').data('mapping-data');

    if (mappingData) {
        // Update data dengan nilai baru
        mappingData.tgl_mulai_validasi = data.tgl_mulai_validasi;
        mappingData.tgl_akhir_validasi = data.tgl_akhir_validasi;

        // Store updated data back to modal
        $('#modal-detail-mapping-validasi').data('mapping-data', mappingData);

        console.log('Mapping data updated in modal:', mappingData);
    }
}

// Function untuk restore modal detail content
function restoreModalDetailContent() {
    const modalBody = `
        <div class="row">
            <!-- Kolom 1: Data Sekolah -->
            <div class="col-md-2">
                <div class="card h-100">
                    <div class="card-header bg-info text-white text-center">
                        <h6 class="mb-0"><i class="fas fa-school"></i> Data Sekolah</h6>
                    </div>
                    <div class="card-body p-2">
                        <table class="table table-sm table-borderless mb-0" style="font-size: 12px;">
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td width="40%" class="font-weight-bold">NPSN</td>
                                <td width="60%" id="detail-npsn">-</td>
                            </tr>
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td class="font-weight-bold">Nama Sekolah</td>
                                <td id="detail-nama-sekolah">-</td>
                            </tr>
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td class="font-weight-bold">Jenjang</td>
                                <td id="detail-jenjang">-</td>
                            </tr>
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td class="font-weight-bold">Kab/Kota</td>
                                <td id="detail-kab-kota">-</td>
                            </tr>
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td class="font-weight-bold">Nama Kepsek</td>
                                <td id="detail-nama-kepsek">-</td>
                            </tr>
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td class="font-weight-bold">HP Kepsek</td>
                                <td id="detail-hp-kepsek">-</td>
                            </tr>
                            <tr>
                                <td class="font-weight-bold">WA Kepsek</td>
                                <td id="detail-wa-kepsek">-</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Kolom 2: Data Asesor -->
            <div class="col-md-3">
                <div class="card h-100">
                    <div class="card-header bg-info text-white text-center">
                        <h6 class="mb-0"><i class="fas fa-users"></i> Data Asesor</h6>
                    </div>
                    <div class="card-body p-2">
                        <table class="table table-sm table-borderless mb-0" style="font-size: 12px;">
                            <tr style="border-bottom: 2px solid #007bff;">
                                <td colspan="2" class="font-weight-bold text-primary">Asesor 1</td>
                            </tr>
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td width="30%" class="font-weight-bold">NIA</td>
                                <td width="70%" id="detail-nia1">-</td>
                            </tr>
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td class="font-weight-bold">Nama</td>
                                <td id="detail-nama-asesor1">-</td>
                            </tr>
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td class="font-weight-bold">No HP</td>
                                <td id="detail-hp-asesor1">-</td>
                            </tr>
                            <tr style="border-bottom: 2px solid #dee2e6;">
                                <td class="font-weight-bold">Kab/Kota</td>
                                <td id="detail-kota-asesor1">-</td>
                            </tr>
                            <tr style="border-bottom: 2px solid #007bff;">
                                <td colspan="2" class="font-weight-bold text-primary">Asesor 2</td>
                            </tr>
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td class="font-weight-bold">NIA</td>
                                <td id="detail-nia2">-</td>
                            </tr>
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td class="font-weight-bold">Nama</td>
                                <td id="detail-nama-asesor2">-</td>
                            </tr>
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td class="font-weight-bold">No HP</td>
                                <td id="detail-hp-asesor2">-</td>
                            </tr>
                            <tr>
                                <td class="font-weight-bold">Kab/Kota</td>
                                <td id="detail-kota-asesor2">-</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Kolom 3: Dokumen Unggahan -->
            <div class="col-md-2">
                <div class="card h-100">
                    <div class="card-header bg-info text-white text-center">
                        <h6 class="mb-0"><i class="fas fa-file-upload"></i> Dokumen Unggahan</h6>
                    </div>
                    <div class="card-body p-2">
                        <table class="table table-sm table-borderless mb-0" style="font-size: 12px;">
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td width="60%" class="font-weight-bold">File Pakta Integritas 1</td>
                                <td width="40%" id="detail-pakta1">-</td>
                            </tr>
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td class="font-weight-bold">File Pakta Integritas 2</td>
                                <td id="detail-pakta2">-</td>
                            </tr>
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td class="font-weight-bold">File Berita Acara Validasi 1</td>
                                <td id="detail-ba1">-</td>
                            </tr>
                            <tr>
                                <td class="font-weight-bold">File Berita Acara Validasi 2</td>
                                <td id="detail-ba2">-</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Kolom 4: Pelaksanaan Kegiatan -->
            <div class="col-md-3">
                <div class="card h-100">
                    <div class="card-header bg-info text-white text-center">
                        <h6 class="mb-0"><i class="fas fa-calendar-check"></i> Pelaksanaan Kegiatan</h6>
                    </div>
                    <div class="card-body p-2">
                        <table class="table table-sm table-borderless mb-0" style="font-size: 12px;">
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td width="50%" class="font-weight-bold">Tanggal Validasi Dimulai</td>
                                <td width="50%" id="detail-tgl-mulai">-</td>
                            </tr>
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td class="font-weight-bold">Tanggal Validasi Berakhir</td>
                                <td id="detail-tgl-akhir">-</td>
                            </tr>
                            <tr style="border-bottom: 1px solid #dee2e6;">
                                <td class="font-weight-bold">No Surat Tugas Validasi</td>
                                <td id="detail-no-surat">-</td>
                            </tr>
                            <tr>
                                <td class="font-weight-bold">Tanggal Surat Tugas Validasi</td>
                                <td id="detail-tgl-surat">-</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Kolom 5: Aksi -->
            <div class="col-md-2">
                <div class="card h-100">
                    <div class="card-header bg-info text-white text-center">
                        <h6 class="mb-0"><i class="fas fa-cogs"></i> Aksi</h6>
                    </div>
                    <div class="card-body p-2">
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-warning btn-sm mb-2" id="btn-edit-tanggal-validasi">
                                <i class="fas fa-calendar-edit"></i> Edit Tanggal Validasi
                            </button>
                            <button type="button" class="btn btn-info btn-sm mb-2" id="btn-edit-asesor-perubahan">
                                <i class="fas fa-user-edit"></i> Edit Asesor Perubahan
                            </button>
                            <button type="button" class="btn btn-danger btn-sm mb-2" id="btn-hapus-mapping">
                                <i class="fas fa-trash"></i> Hapus
                            </button>
                            <button type="button" class="btn btn-success btn-sm" id="btn-download-surat-validasi">
                                <i class="fas fa-download"></i> Download Surat Tugas Validasi
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    $('#modal-detail-mapping-validasi .modal-body').html(modalBody);
}

// Function untuk membuka modal tahun akreditasi
function openTahunAkreditasiModal() {
    console.log('Opening tahun akreditasi modal');

    // Reset form
    clearForm('#form-tahun-akreditasi');

    // Show modal first
    $('#modal-tahun-akreditasi').modal('show');

    // Load data tahun akreditasi
    $.ajax({
        url: 'ajax/get_tahun_akreditasi.php',
        type: 'GET',
        dataType: 'json',
        beforeSend: function() {
            // Disable form while loading
            toggleFormState('#form-tahun-akreditasi', true);
            $('#nama_tahun').attr('placeholder', 'Memuat data...');
        },
        success: function(response) {
            console.log('Response:', response);

            if (response.success) {
                // Populate form dengan data
                $('#id_mapping_validasi_tahun').val(response.data.id_mapping_validasi_tahun || '');
                $('#nama_tahun').val(response.data.nama_tahun || '');

                console.log('Data loaded:', response.data);
            } else {
                showAlert('error', response.message || 'Gagal memuat data tahun akreditasi');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading tahun akreditasi:', error);
            showAlert('error', 'Terjadi kesalahan saat memuat data tahun akreditasi');
        },
        complete: function() {
            // Enable form
            toggleFormState('#form-tahun-akreditasi', false);
            $('#nama_tahun').attr('placeholder', 'Contoh: 2024');
        }
    });
}

// Function untuk submit tahun akreditasi
function submitTahunAkreditasi() {
    console.log('Submitting tahun akreditasi');

    // Validasi form
    if (!validateForm('#form-tahun-akreditasi')) {
        return;
    }

    const formData = new FormData($('#form-tahun-akreditasi')[0]);

    $.ajax({
        url: 'ajax/update_tahun_akreditasi.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        beforeSend: function() {
            toggleFormState('#form-tahun-akreditasi', true);
            showLoading('#form-tahun-akreditasi button[type="submit"]');
        },
        success: function(response) {
            if (response.success) {
                // Hilangkan notifikasi success yang mengganggu
                // showAlert('success', response.message);
                $('#modal-tahun-akreditasi').modal('hide');

                // Auto refresh tabel utama dengan force refresh untuk memastikan filter tahun akreditasi terupdate
                console.log('Force refreshing main table after tahun akreditasi update');
                setTimeout(function() {
                    forceRefreshDataTable();
                }, 500); // Delay 500ms
            } else {
                showAlert('error', response.message || 'Gagal menyimpan tahun akreditasi');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error submitting tahun akreditasi:', error);
            showAlert('error', 'Terjadi kesalahan saat menyimpan tahun akreditasi');
        },
        complete: function() {
            toggleFormState('#form-tahun-akreditasi', false);
            hideLoading('#form-tahun-akreditasi button[type="submit"]', '<i class="fas fa-save"></i> Simpan Perubahan');
        }
    });
}

// Function untuk membuka modal input mapping
function openInputMappingModal() {
    console.log('Opening input mapping modal');

    // Reset form
    clearForm('#form-input-mapping');

    // Clear semua feedback message yang mungkin tertinggal
    $('#form-input-mapping .valid-feedback').remove();
    $('#form-input-mapping .invalid-feedback').remove();
    $('#form-input-mapping .is-valid').removeClass('is-valid');
    $('#form-input-mapping .is-invalid').removeClass('is-invalid');

    // Clear hidden fields
    $('#sekolah_id').val('');
    $('#kd_asesor1').val('');
    $('#kd_asesor2').val('');

    // Show modal
    $('#modal-input-mapping').modal('show');
}

// Function untuk lookup NPSN
function lookupNPSN(npsn) {
    console.log('Looking up NPSN:', npsn);

    $.ajax({
        url: 'ajax/lookup_npsn.php',
        type: 'GET',
        data: { npsn: npsn },
        dataType: 'json',
        beforeSend: function() {
            $('#npsn_sekolah').addClass('is-loading');
        },
        success: function(response) {
            // Clear previous feedback
            $('#npsn_sekolah').removeClass('is-invalid is-valid');
            $('#npsn_sekolah').siblings('.invalid-feedback, .valid-feedback').remove();

            if (response.success) {
                // Set sekolah_id
                $('#sekolah_id').val(response.data.sekolah_id);

                // Show success feedback
                $('#npsn_sekolah').addClass('is-valid');
                $('#npsn_sekolah').after('<div class="valid-feedback">Sekolah ditemukan: ' + response.data.nama_sekolah + '</div>');

                console.log('NPSN found:', response.data);
            } else {
                // Show error feedback
                $('#sekolah_id').val('');
                $('#npsn_sekolah').addClass('is-invalid');
                $('#npsn_sekolah').after('<div class="invalid-feedback">' + response.message + '</div>');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error looking up NPSN:', error);

            // Clear previous feedback
            $('#npsn_sekolah').removeClass('is-invalid is-valid');
            $('#npsn_sekolah').siblings('.invalid-feedback, .valid-feedback').remove();

            // Show error
            $('#sekolah_id').val('');
            $('#npsn_sekolah').addClass('is-invalid');
            $('#npsn_sekolah').after('<div class="invalid-feedback">Terjadi kesalahan saat mencari NPSN</div>');
        },
        complete: function() {
            $('#npsn_sekolah').removeClass('is-loading');
        }
    });
}

// Function untuk lookup NIA
function lookupNIA(nia, asesorNumber) {
    console.log('Looking up NIA:', nia, 'for asesor', asesorNumber);

    const niaField = '#nia_asesor' + asesorNumber;
    const kdField = '#kd_asesor' + asesorNumber;

    $.ajax({
        url: 'ajax/lookup_nia.php',
        type: 'GET',
        data: { nia: nia },
        dataType: 'json',
        beforeSend: function() {
            $(niaField).addClass('is-loading');
        },
        success: function(response) {
            // Clear previous feedback
            $(niaField).removeClass('is-invalid is-valid');
            $(niaField).siblings('.invalid-feedback, .valid-feedback').remove();

            if (response.success) {
                // Set kd_asesor
                $(kdField).val(response.data.kd_asesor);

                // Show success feedback
                $(niaField).addClass('is-valid');
                $(niaField).after('<div class="valid-feedback">Asesor ditemukan: ' + response.data.nm_asesor + '</div>');

                console.log('NIA found:', response.data);
            } else {
                // Show error feedback
                $(kdField).val('');
                $(niaField).addClass('is-invalid');
                $(niaField).after('<div class="invalid-feedback">' + response.message + '</div>');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error looking up NIA:', error);

            // Clear previous feedback
            $(niaField).removeClass('is-invalid is-valid');
            $(niaField).siblings('.invalid-feedback, .valid-feedback').remove();

            // Show error
            $(kdField).val('');
            $(niaField).addClass('is-invalid');
            $(niaField).after('<div class="invalid-feedback">Terjadi kesalahan saat mencari NIA</div>');
        },
        complete: function() {
            $(niaField).removeClass('is-loading');
        }
    });
}

// Function untuk submit input mapping
function submitInputMapping() {
    console.log('Submitting input mapping');

    // Validasi form
    if (!validateForm('#form-input-mapping')) {
        return;
    }

    // Validasi lookup results
    if (!$('#sekolah_id').val()) {
        showAlert('error', 'NPSN belum valid atau belum dicari');
        return;
    }

    if (!$('#kd_asesor1').val()) {
        showAlert('error', 'NIA Asesor 1 belum valid atau belum dicari');
        return;
    }

    if (!$('#kd_asesor2').val()) {
        showAlert('error', 'NIA Asesor 2 belum valid atau belum dicari');
        return;
    }

    const formData = new FormData($('#form-input-mapping')[0]);

    $.ajax({
        url: 'ajax/insert_mapping.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        beforeSend: function() {
            toggleFormState('#form-input-mapping', true);
            showLoading('#form-input-mapping button[type="submit"]');
        },
        success: function(response) {
            if (response.success) {
                // Silent success - no notification
                $('#modal-input-mapping').modal('hide');

                // Auto refresh tabel utama
                console.log('Refreshing main table after insert mapping');
                reloadDataTable();
            } else {
                showAlert('error', response.message || 'Gagal menyimpan data mapping validasi');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error submitting input mapping:', error);
            showAlert('error', 'Terjadi kesalahan saat menyimpan data mapping validasi');
        },
        complete: function() {
            toggleFormState('#form-input-mapping', false);
            hideLoading('#form-input-mapping button[type="submit"]', '<i class="fas fa-save"></i> Simpan Data');
        }
    });
}

// Function untuk reload DataTable
function reloadDataTable() {
    console.log('Attempting to reload DataTable...');

    if ($.fn.DataTable.isDataTable('#table-mapping-validasi')) {
        console.log('DataTable found, reloading...');
        $('#table-mapping-validasi').DataTable().ajax.reload(function(json) {
            console.log('DataTable reloaded successfully. Records:', json.recordsTotal);
        }, false);
    } else {
        console.log('DataTable not found, reinitializing...');
        // Jika DataTable belum ada, inisialisasi ulang
        initDataTable();
    }
}

// Function untuk force refresh DataTable (destroy dan reinit)
function forceRefreshDataTable() {
    console.log('Force refreshing DataTable...');

    if ($.fn.DataTable.isDataTable('#table-mapping-validasi')) {
        $('#table-mapping-validasi').DataTable().destroy();
    }

    // Reinitialize DataTable
    setTimeout(function() {
        initDataTable();
    }, 100);
}

// Function untuk format tanggal
function formatDate(dateString) {
    if (!dateString || dateString === '0000-00-00') return '';
    
    const date = new Date(dateString);
    const options = { 
        year: 'numeric', 
        month: '2-digit', 
        day: '2-digit' 
    };
    return date.toLocaleDateString('id-ID', options);
}

// Function untuk format tanggal Indonesia
function formatDateIndonesia(dateString) {
    if (!dateString || dateString === '0000-00-00') return '-';
    
    const months = [
        'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
        'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ];
    
    const date = new Date(dateString);
    const day = date.getDate();
    const month = months[date.getMonth()];
    const year = date.getFullYear();
    
    return `${day} ${month} ${year}`;
}

// Function untuk validasi form
function validateForm(formId) {
    let isValid = true;
    const form = $(formId);
    
    // Reset previous validation states
    form.find('.is-invalid').removeClass('is-invalid');
    form.find('.invalid-feedback').remove();
    
    // Validate required fields
    form.find('[required]').each(function() {
        const field = $(this);
        const value = field.val().trim();
        
        if (!value) {
            field.addClass('is-invalid');
            field.after('<div class="invalid-feedback">Field ini wajib diisi</div>');
            isValid = false;
        }
    });
    
    return isValid;
}

// Function untuk clear form
function clearForm(formId) {
    const form = $(formId);
    form[0].reset();
    form.find('.is-invalid').removeClass('is-invalid');
    form.find('.is-valid').removeClass('is-valid');
    form.find('.invalid-feedback').remove();
    form.find('.valid-feedback').remove();

    // Clear hidden fields
    form.find('input[type="hidden"]').val('');
}

// Function untuk disable/enable form
function toggleFormState(formId, disabled) {
    const form = $(formId);
    form.find('input, select, textarea, button[type="submit"]').prop('disabled', disabled);
}

// Function untuk show loading state
function showLoading(elementId) {
    $(elementId).html('<i class="fas fa-spinner fa-spin"></i> Memuat...');
}

// Function untuk hide loading state
function hideLoading(elementId, originalText) {
    $(elementId).html(originalText);
}

// Function untuk membuka modal edit asesor perubahan
function openEditAsesorPerubahanModal() {
    console.log('Opening edit asesor perubahan modal');

    // Ambil data mapping dari modal detail yang sedang terbuka
    const mappingData = $('#modal-detail-mapping-validasi').data('mapping-data');

    if (!mappingData) {
        showAlert('error', 'Data mapping tidak ditemukan. Silakan tutup dan buka kembali modal detail.');
        return;
    }

    // Reset form
    clearForm('#form-edit-asesor-perubahan');

    // Hide info cards
    $('#info-asesor1, #info-asesor2').hide();

    // Populate form dengan data existing
    $('#edit_id_mapping_validasi_asesor').val(mappingData.id_mapping_validasi);
    $('#edit_nia_asesor1').val(mappingData.nia1 || '');
    $('#edit_nia_asesor2').val(mappingData.nia2 || '');
    $('#edit_kd_asesor1').val(mappingData.kd_asesor1 || '');
    $('#edit_kd_asesor2').val(mappingData.kd_asesor2 || '');

    // Show info untuk asesor yang sudah ada
    if (mappingData.nia1 && mappingData.nm_asesor1) {
        $('#info-nama-asesor1').text(mappingData.nm_asesor1);
        $('#info-kota-asesor1').text(mappingData.kota_asesor1 || '-');
        $('#info-asesor1').show();
    }

    if (mappingData.nia2 && mappingData.nm_asesor2) {
        $('#info-nama-asesor2').text(mappingData.nm_asesor2);
        $('#info-kota-asesor2').text(mappingData.kota_asesor2 || '-');
        $('#info-asesor2').show();
    }

    // Show modal dengan z-index yang lebih tinggi untuk nested modal
    $('#modal-edit-asesor-perubahan').css('z-index', 1080).modal('show');

    console.log('Edit asesor modal opened with data:', {
        id: mappingData.id_mapping_validasi,
        nia1: mappingData.nia1,
        nia2: mappingData.nia2
    });
}

// Function untuk lookup NIA khusus untuk form edit asesor
function lookupNIAForEdit(nia, asesorNumber) {
    console.log('Looking up NIA for edit:', nia, 'Asesor:', asesorNumber);

    $.ajax({
        url: 'ajax/lookup_nia.php',
        type: 'GET',
        data: { nia: nia },
        dataType: 'json',
        beforeSend: function() {
            // Show loading
            $(`#info-asesor${asesorNumber}`).hide();
        },
        success: function(response) {
            if (response.success) {
                console.log('NIA lookup success:', response.data);

                // Validasi: NIA asesor 1 dan 2 harus berbeda
                const otherAsesorNumber = asesorNumber === 1 ? 2 : 1;
                const otherNIA = $(`#edit_nia_asesor${otherAsesorNumber}`).val().trim();

                if (otherNIA && otherNIA === nia) {
                    showAlert('error', `NIA Asesor ${asesorNumber} tidak boleh sama dengan NIA Asesor ${otherAsesorNumber}`);
                    $(`#edit_nia_asesor${asesorNumber}`).val('').focus();
                    $(`#edit_kd_asesor${asesorNumber}`).val('');
                    return;
                }

                // Set hidden field kd_asesor
                $(`#edit_kd_asesor${asesorNumber}`).val(response.data.kd_asesor);

                // Show info asesor
                $(`#info-nama-asesor${asesorNumber}`).text(response.data.nm_asesor);
                $(`#info-kota-asesor${asesorNumber}`).text(response.data.nm_kota || '-');
                $(`#info-asesor${asesorNumber}`).show();

            } else {
                showAlert('error', response.message || `NIA Asesor ${asesorNumber} tidak ditemukan`);
                $(`#edit_nia_asesor${asesorNumber}`).val('').focus();
                $(`#edit_kd_asesor${asesorNumber}`).val('');
                $(`#info-asesor${asesorNumber}`).hide();
            }
        },
        error: function(xhr, status, error) {
            console.error('Error looking up NIA:', error);
            showAlert('error', `Terjadi kesalahan saat mencari NIA Asesor ${asesorNumber}`);
            $(`#edit_kd_asesor${asesorNumber}`).val('');
            $(`#info-asesor${asesorNumber}`).hide();
        }
    });
}

// Function untuk membuka modal konfirmasi hapus mapping
function openKonfirmasiHapusMappingModal() {
    console.log('Opening konfirmasi hapus mapping modal');

    // Ambil data mapping dari modal detail yang sedang terbuka
    const mappingData = $('#modal-detail-mapping-validasi').data('mapping-data');

    if (!mappingData) {
        showAlert('error', 'Data mapping tidak ditemukan. Silakan tutup dan buka kembali modal detail.');
        return;
    }

    // Populate data di modal konfirmasi
    $('#hapus-nama-sekolah').text(mappingData.nama_sekolah || '-');
    $('#hapus-npsn').text(mappingData.npsn || '-');
    $('#hapus-asesor1').text(mappingData.nm_asesor1 || '-');
    $('#hapus-asesor2').text(mappingData.nm_asesor2 || '-');
    $('#hapus-tahun').text(mappingData.tahun_akreditasi || '-');
    $('#hapus-tahap').text(mappingData.tahap || '-');

    // Simpan ID mapping yang akan dihapus
    $('#id-mapping-akan-dihapus').val(mappingData.id_mapping_validasi);

    // Show modal dengan z-index yang lebih tinggi untuk nested modal
    $('#modal-konfirmasi-hapus-mapping').css('z-index', 1070).modal('show');

    console.log('Konfirmasi hapus modal opened with ID:', mappingData.id_mapping_validasi);
}

// Function untuk submit edit asesor perubahan
function submitEditAsesorPerubahan() {
    console.log('Submitting edit asesor perubahan');

    // Validasi form
    if (!validateForm('#form-edit-asesor-perubahan')) {
        return;
    }

    // Validasi manual untuk field required
    const nia1 = $('#edit_nia_asesor1').val().trim();
    const nia2 = $('#edit_nia_asesor2').val().trim();
    const kd_asesor1 = $('#edit_kd_asesor1').val();
    const kd_asesor2 = $('#edit_kd_asesor2').val();

    if (!nia1) {
        showAlert('error', 'NIA Asesor 1 harus diisi');
        $('#edit_nia_asesor1').focus();
        return;
    }

    if (!nia2) {
        showAlert('error', 'NIA Asesor 2 harus diisi');
        $('#edit_nia_asesor2').focus();
        return;
    }

    if (nia1 === nia2) {
        showAlert('error', 'NIA Asesor 1 dan Asesor 2 tidak boleh sama');
        $('#edit_nia_asesor2').focus();
        return;
    }

    if (!kd_asesor1) {
        showAlert('error', 'Data Asesor 1 tidak valid. Silakan input ulang NIA Asesor 1');
        $('#edit_nia_asesor1').focus();
        return;
    }

    if (!kd_asesor2) {
        showAlert('error', 'Data Asesor 2 tidak valid. Silakan input ulang NIA Asesor 2');
        $('#edit_nia_asesor2').focus();
        return;
    }

    const formData = new FormData($('#form-edit-asesor-perubahan')[0]);

    $.ajax({
        url: 'ajax/update_asesor.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        beforeSend: function() {
            toggleFormState('#form-edit-asesor-perubahan', true);
            showLoading('#form-edit-asesor-perubahan button[type="submit"]');
        },
        success: function(response) {
            if (response.success) {
                console.log('Asesor perubahan updated successfully:', response.data);

                // Tutup modal edit
                $('#modal-edit-asesor-perubahan').modal('hide');

                // Update display di modal detail tanpa refresh
                updateAsesorDisplayInModal(response.data);

                // Update data di modal detail untuk konsistensi
                updateAsesorDataInModal(response.data);

                // Reload DataTable tanpa refresh halaman
                $('#table-mapping-validasi').DataTable().ajax.reload(null, false);

                // Silent success - no notification to avoid clutter
                console.log('Asesor perubahan display updated successfully');

            } else {
                showAlert('error', response.message || 'Gagal mengupdate data asesor');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error submitting edit asesor perubahan:', error);
            showAlert('error', 'Terjadi kesalahan saat mengupdate data asesor');
        },
        complete: function() {
            toggleFormState('#form-edit-asesor-perubahan', false);
            hideLoading('#form-edit-asesor-perubahan button[type="submit"]', '<i class="fas fa-save"></i> Update Perubahan');
        }
    });
}

// Function untuk update display asesor di modal detail
function updateAsesorDisplayInModal(data) {
    console.log('Updating asesor display with:', data);

    // Update field di kolom Data Asesor
    $('#detail-nia1').text(data.nia1 || '-');
    $('#detail-nama-asesor1').text(data.nm_asesor1 || '-');
    $('#detail-hp-asesor1').text(data.hp_asesor1 || '-');
    $('#detail-kota-asesor1').text(data.kota_asesor1 || '-');

    $('#detail-nia2').text(data.nia2 || '-');
    $('#detail-nama-asesor2').text(data.nm_asesor2 || '-');
    $('#detail-hp-asesor2').text(data.hp_asesor2 || '-');
    $('#detail-kota-asesor2').text(data.kota_asesor2 || '-');

    // Add smooth animation effect
    $('#detail-nia1, #detail-nama-asesor1, #detail-hp-asesor1, #detail-kota-asesor1, #detail-nia2, #detail-nama-asesor2, #detail-hp-asesor2, #detail-kota-asesor2').addClass('text-success').delay(2000).queue(function() {
        $(this).removeClass('text-success').dequeue();
    });
}

// Function untuk update asesor data di modal untuk konsistensi
function updateAsesorDataInModal(data) {
    const mappingData = $('#modal-detail-mapping-validasi').data('mapping-data');

    if (mappingData) {
        // Update data dengan nilai baru
        mappingData.kd_asesor1 = data.kd_asesor1;
        mappingData.kd_asesor2 = data.kd_asesor2;
        mappingData.nia1 = data.nia1;
        mappingData.nia2 = data.nia2;
        mappingData.nm_asesor1 = data.nm_asesor1;
        mappingData.nm_asesor2 = data.nm_asesor2;
        mappingData.hp_asesor1 = data.hp_asesor1;
        mappingData.hp_asesor2 = data.hp_asesor2;
        mappingData.kota_asesor1 = data.kota_asesor1;
        mappingData.kota_asesor2 = data.kota_asesor2;

        // Store updated data back to modal
        $('#modal-detail-mapping-validasi').data('mapping-data', mappingData);

        console.log('Asesor data updated in modal:', mappingData);
    }
}

// Function untuk submit hapus mapping
function submitHapusMapping() {
    console.log('Submitting hapus mapping');

    const idMapping = $('#id-mapping-akan-dihapus').val();

    if (!idMapping) {
        showAlert('error', 'ID mapping tidak valid');
        return;
    }

    $.ajax({
        url: 'ajax/hapus_mapping.php',
        type: 'POST',
        data: { id_mapping_validasi: idMapping },
        dataType: 'json',
        beforeSend: function() {
            // Disable tombol konfirmasi dan show loading
            $('#btn-konfirmasi-hapus-mapping').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menghapus...');
        },
        success: function(response) {
            if (response.success) {
                console.log('Mapping berhasil dihapus:', response.data);

                // Tutup modal konfirmasi hapus
                $('#modal-konfirmasi-hapus-mapping').modal('hide');

                // Tutup modal detail
                $('#modal-detail-mapping-validasi').modal('hide');

                // Reload DataTable tanpa refresh halaman
                $('#table-mapping-validasi').DataTable().ajax.reload(null, false);

                // Silent success - no notification to avoid clutter
                console.log('Data mapping validasi berhasil dihapus:', response.data);

            } else {
                showAlert('error', response.message || 'Gagal menghapus data mapping validasi');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error submitting hapus mapping:', error);
            showAlert('error', 'Terjadi kesalahan saat menghapus data mapping validasi');
        },
        complete: function() {
            // Enable tombol konfirmasi kembali
            $('#btn-konfirmasi-hapus-mapping').prop('disabled', false).html('<i class="fas fa-trash"></i> Ya, Hapus Data');
        }
    });
}

// Function untuk download surat tugas validasi
function downloadSuratTugasValidasi() {
    console.log('Downloading surat tugas validasi');

    // Ambil data mapping dari modal detail yang sedang terbuka
    const mappingData = $('#modal-detail-mapping-validasi').data('mapping-data');

    if (!mappingData) {
        showAlert('error', 'Data mapping tidak ditemukan. Silakan tutup dan buka kembali modal detail.');
        return;
    }

    const idMappingValidasi = mappingData.id_mapping_validasi;

    if (!idMappingValidasi) {
        showAlert('error', 'ID mapping validasi tidak valid');
        return;
    }

    // Validasi apakah data surat tugas sudah lengkap
    const noSurat = mappingData.no_surat_validasi;
    const tglSurat = mappingData.tgl_surat_validasi;

    if (!noSurat || noSurat === '-' || !tglSurat || tglSurat === '-') {
        showAlert('warning', 'Data surat tugas belum lengkap. Pastikan No. Surat dan Tanggal Surat sudah diisi.');
        return;
    }

    // Construct URL untuk file PDF generator
    const downloadUrl = `mapping_validasi_st_validasi.php?kode=${idMappingValidasi}`;

    console.log('Opening surat tugas validasi:', {
        id: idMappingValidasi,
        no_surat: noSurat,
        tgl_surat: tglSurat,
        url: downloadUrl
    });

    // Buka file PDF di tab baru
    window.open(downloadUrl, '_blank');

    // Log successful download attempt
    console.log('Surat tugas validasi opened in new tab');
}

// Function untuk confirm delete (legacy - keep for compatibility)
function confirmDelete(message, callback) {
    if (confirm(message || 'Apakah Anda yakin ingin menghapus data ini?')) {
        callback();
    }
}

// Function untuk export data
function exportData(type, url) {
    console.log(`Exporting data as ${type}`);
    
    // Create temporary link for download
    const link = document.createElement('a');
    link.href = url;
    link.download = `mapping_validasi_${new Date().toISOString().split('T')[0]}.${type}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Function untuk handle file upload
function handleFileUpload(inputElement, allowedTypes, maxSize) {
    const file = inputElement.files[0];
    
    if (!file) return true;
    
    // Check file type
    if (allowedTypes && !allowedTypes.includes(file.type)) {
        showAlert('error', 'Tipe file tidak diizinkan');
        inputElement.value = '';
        return false;
    }
    
    // Check file size (in bytes)
    if (maxSize && file.size > maxSize) {
        showAlert('error', `Ukuran file terlalu besar. Maksimal ${maxSize / 1024 / 1024}MB`);
        inputElement.value = '';
        return false;
    }
    
    return true;
}

// Function untuk preview file
function previewFile(filename, fileType) {
    let basePath = '';
    
    // Tentukan path berdasarkan jenis file
    if (fileType === 'pakta_integritas') {
        basePath = '../../../simak/files/upload_file_hasil_validasi/';
    } else if (fileType === 'berita_acara') {
        basePath = '../../../simak/files/upload_file_hasil_validasi/';
    }
    
    const fileUrl = basePath + filename;
    
    // Buka file di tab baru
    window.open(fileUrl, '_blank');
}

// Function untuk get file status badge
function getFileStatus(filename, fileType) {
    if (filename && filename.trim() !== '') {
        return '<span class="badge badge-success text-white file-preview-link" data-filename="' + filename + '" data-filetype="' + fileType + '" style="cursor: pointer;">Sudah Upload</span>';
    } else {
        return '<span class="badge badge-danger text-white">Belum Upload</span>';
    }
}

// Event handler untuk preview file
$(document).on('click', '.file-preview-link', function(e) {
    e.preventDefault();
    
    const filename = $(this).data('filename');
    const fileType = $(this).data('filetype');
    
    if (filename && filename.trim() !== '') {
        previewFile(filename, fileType);
    }
});

console.log('Mapping Validasi JavaScript initialized successfully');
