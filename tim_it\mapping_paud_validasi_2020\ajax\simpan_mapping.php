<?php
require_once '../../../koneksi.php';
require_once '../../../check_session.php';

// Validasi level akses
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Ambil data dari POST
    $sekolah_id = trim($_POST['sekolah_id'] ?? '');
    $kd_asesor1 = trim($_POST['nia_validator'] ?? '');
    $kd_asesor2 = trim($_POST['nia_verifikator'] ?? '');
    $tahun_akreditasi = trim($_POST['tahun_akreditasi'] ?? '');
    $tahap = trim($_POST['tahap'] ?? '');
    
    // Validasi input required
    if (empty($sekolah_id)) {
        echo json_encode([
            'success' => false,
            'message' => 'NPSN Sekolah harus diisi dan valid'
        ]);
        exit;
    }
    
    if (empty($kd_asesor1)) {
        echo json_encode([
            'success' => false,
            'message' => 'NIA Validator harus diisi'
        ]);
        exit;
    }
    
    if (empty($kd_asesor2)) {
        echo json_encode([
            'success' => false,
            'message' => 'NIA Verifikator harus diisi'
        ]);
        exit;
    }
    
    if (empty($tahun_akreditasi)) {
        echo json_encode([
            'success' => false,
            'message' => 'Tahun Akreditasi harus diisi'
        ]);
        exit;
    }
    
    if (empty($tahap)) {
        echo json_encode([
            'success' => false,
            'message' => 'Tahap Ke harus diisi'
        ]);
        exit;
    }
    
    // Validasi format tahun (4 digit)
    if (!preg_match('/^\d{4}$/', $tahun_akreditasi)) {
        echo json_encode([
            'success' => false,
            'message' => 'Tahun Akreditasi harus berformat 4 digit angka'
        ]);
        exit;
    }
    
    // Validasi tahap (harus angka positif)
    if (!is_numeric($tahap) || $tahap < 1) {
        echo json_encode([
            'success' => false,
            'message' => 'Tahap Ke harus berupa angka positif'
        ]);
        exit;
    }
    
    // Ambil provinsi_id dari session
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Validasi sekolah_id masih valid dan PAUD
    $validate_sekolah = "SELECT sekolah_id, rumpun FROM sekolah 
                        WHERE sekolah_id = ? AND provinsi_id = ? AND soft_delete = '1'";
    $stmt_validate = $conn->prepare($validate_sekolah);
    $stmt_validate->bind_param('ii', $sekolah_id, $provinsi_id);
    $stmt_validate->execute();
    $result_validate = $stmt_validate->get_result();
    
    if ($result_validate->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Sekolah tidak valid atau tidak ditemukan'
        ]);
        exit;
    }
    
    $sekolah_data = $result_validate->fetch_assoc();
    if (strtolower($sekolah_data['rumpun']) !== 'paud') {
        echo json_encode([
            'success' => false,
            'message' => 'Sekolah bukan PAUD'
        ]);
        exit;
    }
    
    // Validasi kd_asesor1 (Validator) - langsung menggunakan kd_asesor1 dari frontend
    $validate_asesor1 = "SELECT kd_asesor1, nm_asesor1, nia1 FROM asesor_1
                         WHERE kd_asesor1 = ? AND provinsi_id = ? AND soft_delete = '1' AND status_keaktifan_id = '1'";
    $stmt_asesor1 = $conn->prepare($validate_asesor1);
    $stmt_asesor1->bind_param('si', $kd_asesor1, $provinsi_id);
    $stmt_asesor1->execute();
    $result_asesor1 = $stmt_asesor1->get_result();

    if ($result_asesor1->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Validator tidak ditemukan atau tidak aktif di provinsi Anda'
        ]);
        exit;
    }
    $asesor1_data = $result_asesor1->fetch_assoc();

    // Validasi kd_asesor2 (Verifikator) - langsung menggunakan kd_asesor2 dari frontend
    $validate_asesor2 = "SELECT kd_asesor2, nm_asesor2, nia2 FROM asesor_2
                         WHERE kd_asesor2 = ? AND provinsi_id = ? AND soft_delete = '1' AND status_keaktifan_id = '1'";
    $stmt_asesor2 = $conn->prepare($validate_asesor2);
    $stmt_asesor2->bind_param('si', $kd_asesor2, $provinsi_id);
    $stmt_asesor2->execute();
    $result_asesor2 = $stmt_asesor2->get_result();

    if ($result_asesor2->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Verifikator tidak ditemukan atau tidak aktif di provinsi Anda'
        ]);
        exit;
    }
    $asesor2_data = $result_asesor2->fetch_assoc();
    
    // Validasi asesor tidak boleh sama
    if ($kd_asesor1 === $kd_asesor2) {
        echo json_encode([
            'success' => false,
            'message' => 'Validator dan Verifikator tidak boleh sama'
        ]);
        exit;
    }

    // Insert data mapping menggunakan kd_asesor yang sudah divalidasi
    $insert_query = "INSERT INTO mapping_paud_validasi
                    (sekolah_id, kd_asesor1, kd_asesor2, tahap, tahun_akreditasi, provinsi_id)
                    VALUES (?, ?, ?, ?, ?, ?)";

    $stmt_insert = $conn->prepare($insert_query);
    $stmt_insert->bind_param('issisi', $sekolah_id, $kd_asesor1, $kd_asesor2, $tahap, $tahun_akreditasi, $provinsi_id);
    
    if ($stmt_insert->execute()) {
        $new_mapping_id = $conn->insert_id;

        // Log successful insert
        error_log("Insert Mapping Validasi Success - ID: $new_mapping_id, Sekolah: " . $sekolah_data['nama_sekolah'] .
                  ", Validator: " . $asesor1_data['nm_asesor1'] . ", Verifikator: " . $asesor2_data['nm_asesor2'] .
                  ", User: " . $_SESSION['nm_user']);

        echo json_encode([
            'success' => true,
            'message' => 'Data mapping berhasil disimpan',
            'data' => [
                'id_mapping' => $new_mapping_id,
                'sekolah' => $sekolah_data,
                'asesor1' => $asesor1_data,
                'asesor2' => $asesor2_data,
                'tahun_akreditasi' => $tahun_akreditasi,
                'tahap' => $tahap
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Gagal menyimpan data mapping: ' . $conn->error
        ]);
    }
    
} catch (Exception $e) {
    error_log("Simpan Mapping Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan saat menyimpan data mapping'
    ]);
}

$conn->close();
?>
