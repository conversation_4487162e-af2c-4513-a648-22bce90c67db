<?php
// Start session first
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include files
require_once '../../../koneksi.php';

// Simple session check
if (!isset($_SESSION['kd_user']) || empty($_SESSION['kd_user'])) {
    echo json_encode(['success' => false, 'message' => 'Session tidak valid']);
    exit;
}

// Simple level check
if (!isset($_SESSION['level']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['success' => false, 'message' => 'Aks<PERSON> ditolak']);
    exit;
}

// Check provinsi_id
if (!isset($_SESSION['provinsi_id']) || empty($_SESSION['provinsi_id'])) {
    echo json_encode(['success' => false, 'message' => 'Data provinsi tidak ditemukan']);
    exit;
}

// Cek parameter
if (!isset($_POST['id_hasil_akreditasi'])) {
    echo json_encode(['success' => false, 'message' => 'Parameter tidak lengkap']);
    exit;
}

$id_hasil_akreditasi = intval($_POST['id_hasil_akreditasi']);
$provinsi_id = $_SESSION['provinsi_id'];

try {
    // Test koneksi database terlebih dahulu
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Cek apakah data exists dan milik provinsi yang sama
    $check_query = "SELECT ha.sekolah_id, ha.tahun_akreditasi, s.nama_sekolah, s.npsn, s.provinsi_id 
                    FROM hasil_akreditasi ha
                    LEFT JOIN sekolah s ON ha.sekolah_id = s.sekolah_id
                    WHERE ha.id_hasil_akreditasi = ?";
    
    $check_stmt = $conn->prepare($check_query);
    if (!$check_stmt) {
        throw new Exception("Prepare failed: " . $conn->error);
    }
    
    $check_stmt->bind_param("i", $id_hasil_akreditasi);
    if (!$check_stmt->execute()) {
        throw new Exception("Execute failed: " . $check_stmt->error);
    }
    
    $check_result = $check_stmt->get_result();
    if (!$check_result) {
        throw new Exception("Get result failed: " . $check_stmt->error);
    }
    
    if ($check_result->num_rows == 0) {
        echo json_encode(['success' => false, 'message' => 'Data tidak ditemukan']);
        exit;
    }
    
    $check_data = $check_result->fetch_assoc();
    
    // Validasi provinsi - hanya bisa hapus data provinsi sendiri
    if ($check_data['provinsi_id'] != $provinsi_id) {
        echo json_encode(['success' => false, 'message' => 'Anda tidak memiliki akses untuk menghapus data ini']);
        exit;
    }
    
    // Simpan info untuk response
    $sekolah_info = $check_data['nama_sekolah'] . ' (NPSN: ' . $check_data['npsn'] . ')';
    $tahun_info = $check_data['tahun_akreditasi'];
    
    // Delete permanen dari database
    $delete_query = "DELETE FROM hasil_akreditasi WHERE id_hasil_akreditasi = ?";
    
    $delete_stmt = $conn->prepare($delete_query);
    if (!$delete_stmt) {
        throw new Exception("Prepare delete failed: " . $conn->error);
    }
    
    $delete_stmt->bind_param("i", $id_hasil_akreditasi);
    if (!$delete_stmt->execute()) {
        throw new Exception("Execute delete failed: " . $delete_stmt->error);
    }
    
    // Cek apakah data benar-benar terhapus
    if ($delete_stmt->affected_rows == 0) {
        throw new Exception("Tidak ada data yang terhapus");
    }
    
    echo json_encode([
        'success' => true, 
        'message' => 'Data nilai akreditasi tahun ' . $tahun_info . ' untuk ' . $sekolah_info . ' berhasil dihapus permanen',
        'sekolah_id' => $check_data['sekolah_id']
    ]);
    
} catch (Exception $e) {
    error_log("Error in delete_nilai.php: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    echo json_encode(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
}

$conn->close();
?>
