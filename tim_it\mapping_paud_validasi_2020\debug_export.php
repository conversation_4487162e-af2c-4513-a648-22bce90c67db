<!DOCTYPE html>
<html>
<head>
    <title>Debug Export Mapping Validasi</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Debug Export Mapping Validasi PAUD</h1>
    
    <?php
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    
    echo "<h2>1. Session Check</h2>";
    session_start();
    if (isset($_SESSION['level'])) {
        echo "<div class='success'>✓ Session OK - Level: " . $_SESSION['level'] . "</div>";
        echo "<div class='info'>Provinsi ID: " . ($_SESSION['provinsi_id'] ?? 'Not set') . "</div>";
    } else {
        echo "<div class='error'>✗ No session found</div>";
        echo "<div class='info'>Please login first</div>";
        exit;
    }
    
    echo "<h2>2. Database Connection</h2>";
    require_once 'koneksi.php';
    if ($conn) {
        echo "<div class='success'>✓ Database connected</div>";
    } else {
        echo "<div class='error'>✗ Database connection failed</div>";
        exit;
    }
    
    echo "<h2>3. Test Simple Query</h2>";
    try {
        $test_query = "SELECT COUNT(*) as total FROM mapping_paud_validasi WHERE provinsi_id = ?";
        $stmt = $conn->prepare($test_query);
        $stmt->bind_param("i", $_SESSION['provinsi_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        $count = $result->fetch_assoc();
        echo "<div class='success'>✓ Total mapping records: " . $count['total'] . "</div>";
    } catch (Exception $e) {
        echo "<div class='error'>✗ Query error: " . $e->getMessage() . "</div>";
    }
    
    echo "<h2>4. Test JOIN Query</h2>";
    try {
        $join_query = "SELECT COUNT(*) as total 
                       FROM mapping_paud_validasi mp
                       LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
                       WHERE mp.provinsi_id = ?
                         AND s.rumpun = 'paud'
                         AND s.soft_delete = '1'";
        $stmt2 = $conn->prepare($join_query);
        $stmt2->bind_param("i", $_SESSION['provinsi_id']);
        $stmt2->execute();
        $result2 = $stmt2->get_result();
        $count2 = $result2->fetch_assoc();
        echo "<div class='success'>✓ Records with JOIN: " . $count2['total'] . "</div>";
    } catch (Exception $e) {
        echo "<div class='error'>✗ JOIN Query error: " . $e->getMessage() . "</div>";
    }
    
    echo "<h2>5. Test Sample Data</h2>";
    try {
        $sample_query = "SELECT 
                            mp.id_mapping,
                            s.nama_sekolah,
                            s.npsn,
                            mp.tahun_akreditasi
                         FROM mapping_paud_validasi mp
                         LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
                         WHERE mp.provinsi_id = ?
                           AND s.rumpun = 'paud'
                           AND s.soft_delete = '1'
                         LIMIT 3";
        $stmt3 = $conn->prepare($sample_query);
        $stmt3->bind_param("i", $_SESSION['provinsi_id']);
        $stmt3->execute();
        $result3 = $stmt3->get_result();
        
        if ($result3->num_rows > 0) {
            echo "<div class='success'>✓ Sample records found:</div>";
            echo "<pre>";
            while ($row = $result3->fetch_assoc()) {
                echo "ID: " . $row['id_mapping'] . 
                     ", School: " . $row['nama_sekolah'] . 
                     ", NPSN: " . $row['npsn'] . 
                     ", Year: " . $row['tahun_akreditasi'] . "\n";
            }
            echo "</pre>";
        } else {
            echo "<div class='error'>✗ No sample records found</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ Sample query error: " . $e->getMessage() . "</div>";
    }
    
    echo "<h2>6. Test AJAX Endpoint</h2>";
    echo "<button onclick='testAjax()'>Test AJAX Export</button>";
    echo "<div id='ajax-result'></div>";
    ?>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
    function testAjax() {
        $('#ajax-result').html('<div class="info">Testing AJAX...</div>');
        
        $.ajax({
            url: 'ajax/get_export_simple.php',
            type: 'POST',
            success: function(response) {
                console.log('Raw response:', response);
                try {
                    var data = (typeof response === 'string') ? JSON.parse(response) : response;
                    if (data.success) {
                        $('#ajax-result').html('<div class="success">✓ AJAX Success: ' + data.total_records + ' records</div>');
                    } else {
                        $('#ajax-result').html('<div class="error">✗ AJAX Error: ' + data.message + '</div>');
                    }
                } catch (e) {
                    $('#ajax-result').html('<div class="error">✗ JSON Parse Error: ' + e.message + '</div>');
                    console.error('Raw response:', response);
                }
            },
            error: function(xhr, status, error) {
                $('#ajax-result').html('<div class="error">✗ AJAX Request Error: ' + error + '</div>');
                console.error('Response text:', xhr.responseText);
            }
        });
    }
    </script>
</body>
</html>
