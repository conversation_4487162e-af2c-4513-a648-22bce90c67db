<?php
/**
 * AJAX handler untuk menghapus data sekolah (soft delete)
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    if (!isset($_POST['sekolah_id']) || empty($_POST['sekolah_id'])) {
        throw new Exception('ID Sekolah tidak valid');
    }
    
    $sekolah_id = intval($_POST['sekolah_id']);
    $nama_sekolah = isset($_POST['nama_sekolah']) ? trim($_POST['nama_sekolah']) : '';
    
    // Ambil provinsi_id dari session untuk validasi
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Validasi apakah sekolah ada dan milik provinsi user
    $check_query = "SELECT sekolah_id, nama_sekolah, soft_delete 
                    FROM sekolah 
                    WHERE sekolah_id = ? AND provinsi_id = ? AND rumpun = 'kesetaraan'";
    
    $stmt_check = $conn->prepare($check_query);
    $stmt_check->bind_param("ii", $sekolah_id, $provinsi_id_session);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();
    
    if ($result_check->num_rows == 0) {
        throw new Exception('Data sekolah tidak ditemukan atau Anda tidak memiliki akses');
    }
    
    $sekolah_data = $result_check->fetch_assoc();
    
    // Cek apakah sudah dihapus sebelumnya
    if ($sekolah_data['soft_delete'] == 0) {
        throw new Exception('Data sekolah sudah dihapus sebelumnya');
    }
    
    // Begin transaction
    $conn->autocommit(false);
    
    // Update soft_delete menjadi 0 (hapus)
    $delete_query = "UPDATE sekolah 
                     SET soft_delete = 0 
                     WHERE sekolah_id = ? AND provinsi_id = ?";
    
    $stmt_delete = $conn->prepare($delete_query);
    $stmt_delete->bind_param("ii", $sekolah_id, $provinsi_id_session);
    
    if (!$stmt_delete->execute()) {
        throw new Exception('Gagal menghapus data sekolah: ' . $stmt_delete->error);
    }
    
    // Cek apakah ada baris yang terpengaruh
    if ($stmt_delete->affected_rows == 0) {
        throw new Exception('Tidak ada data yang dihapus');
    }

    // Sinkronisasi soft delete ke tabel user
    $sql_user = "UPDATE user SET
                     soft_delete = '0'
                 WHERE kd_user = ? AND soft_delete = '1'";

    $stmt_user = $conn->prepare($sql_user);
    $stmt_user->bind_param("s", $sekolah_id);

    if (!$stmt_user->execute()) {
        throw new Exception('Gagal sinkronisasi soft delete ke tabel user: ' . $stmt_user->error);
    }

    // Commit transaction jika semua berhasil
    $conn->commit();
    $conn->autocommit(true);
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data sekolah "' . $sekolah_data['nama_sekolah'] . '" berhasil dihapus',
        'data' => [
            'sekolah_id' => $sekolah_id,
            'nama_sekolah' => $sekolah_data['nama_sekolah'],
            'action' => 'soft_delete'
        ]
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Rollback transaction jika ada error
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Hapus Sekolah Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
}
?>
