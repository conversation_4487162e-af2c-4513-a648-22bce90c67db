<?php
// Start session first
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include files
require_once '../../../koneksi.php';

// Simple session check
if (!isset($_SESSION['kd_user']) || empty($_SESSION['kd_user'])) {
    echo json_encode(['success' => false, 'message' => 'Session tidak valid']);
    exit;
}

// Simple level check
if (!isset($_SESSION['level']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['success' => false, 'message' => 'A<PERSON><PERSON> ditolak']);
    exit;
}

// Check provinsi_id
if (!isset($_SESSION['provinsi_id']) || empty($_SESSION['provinsi_id'])) {
    echo json_encode(['success' => false, 'message' => 'Data provinsi tidak ditemukan']);
    exit;
}

try {
    // Test koneksi database terlebih dahulu
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];

    // Base query untuk nilai terakhir (sama seperti tabel utama)
    // Filter: soft_delete=1, rumpun=paud, provinsi_id=session, status_keaktifan_id=1 (aktif)
    $base_query = "FROM hasil_akreditasi ha
                   LEFT JOIN sekolah s ON ha.sekolah_id = s.sekolah_id
                   LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                   LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
                   LEFT JOIN jurusan_2_prog_ahli pa ON ha.id_prog_ahli = pa.id_prog_ahli
                   WHERE s.soft_delete = '1'
                     AND s.rumpun = 'paud'
                     AND s.provinsi_id = {$provinsi_id_session}
                     AND s.status_keaktifan_id = '1'
                     AND ha.tahun_akreditasi = (
                         SELECT MAX(ha2.tahun_akreditasi)
                         FROM hasil_akreditasi ha2
                         WHERE ha2.sekolah_id = ha.sekolah_id
                     )";

    // Query untuk mengambil data export nilai terakhir
    $query = "SELECT ha.id_hasil_akreditasi, ha.sekolah_id, ha.nilai_akhir, ha.peringkat,
                     ha.status, ha.tahun_akreditasi, ha.tahun_berakhir,
                     DATE_FORMAT(ha.tgl_sk_penetapan, '%d-%m-%Y') as tgl_sk_formatted,
                     ha.no_sk, s.npsn, s.nama_sekolah, j.nm_jenjang, k.nm_kota,
                     pa.nm_prog_ahli
              " . $base_query . "
              ORDER BY s.nama_sekolah ASC";

    $result = $conn->query($query);
    
    if (!$result) {
        throw new Exception("Query failed: " . $conn->error);
    }
    
    // Siapkan data untuk response
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = [
            'id_hasil_akreditasi' => $row['id_hasil_akreditasi'],
            'sekolah_id' => $row['sekolah_id'],
            'npsn' => $row['npsn'],
            'nama_sekolah' => $row['nama_sekolah'],
            'nm_jenjang' => $row['nm_jenjang'],
            'nm_kota' => $row['nm_kota'],
            'nilai_akhir' => $row['nilai_akhir'],
            'peringkat' => $row['peringkat'],
            'status' => $row['status'],
            'tgl_sk_formatted' => $row['tgl_sk_formatted'] ?: '-',
            'no_sk' => $row['no_sk'] ?: '-',
            'tahun_akreditasi' => $row['tahun_akreditasi'],
            'tahun_berakhir' => $row['tahun_berakhir'],
            'nm_prog_ahli' => $row['nm_prog_ahli'] ?: '-'
        ];
    }
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data nilai terakhir berhasil diambil untuk export',
        'data' => $data,
        'total_records' => count($data)
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    error_log("Export Nilai Terakhir Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => 'Terjadi kesalahan saat mengambil data: ' . $e->getMessage(),
        'data' => [],
        'total_records' => 0
    ];
    
    echo json_encode($response);
}

$conn->close();
?>
