<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi required fields
    $required_fields = ['sekolah_id', 'kd_asesor1', 'kd_asesor2', 'tahun_akreditasi', 'tahap'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            throw new Exception("Field $field harus diisi");
        }
    }
    
    // Sanitasi input
    $sekolah_id = intval($_POST['sekolah_id']);
    $kd_asesor1 = $conn->real_escape_string(trim($_POST['kd_asesor1']));
    $kd_asesor2 = $conn->real_escape_string(trim($_POST['kd_asesor2']));
    $tahun_akreditasi = $conn->real_escape_string(trim($_POST['tahun_akreditasi']));
    $tahap = $conn->real_escape_string(trim($_POST['tahap'])); // Changed to string untuk fleksibilitas
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Optional fields
    $tgl_mulai_visitasi = isset($_POST['tgl_mulai_visitasi']) && !empty($_POST['tgl_mulai_visitasi']) 
                         ? $conn->real_escape_string($_POST['tgl_mulai_visitasi']) 
                         : null;
    $tgl_akhir_visitasi = isset($_POST['tgl_akhir_visitasi']) && !empty($_POST['tgl_akhir_visitasi']) 
                         ? $conn->real_escape_string($_POST['tgl_akhir_visitasi']) 
                         : null;
    $no_surat = isset($_POST['no_surat']) && !empty(trim($_POST['no_surat'])) 
               ? $conn->real_escape_string(trim($_POST['no_surat'])) 
               : null;
    $tgl_surat = isset($_POST['tgl_surat']) && !empty($_POST['tgl_surat']) 
                ? $conn->real_escape_string($_POST['tgl_surat']) 
                : null;
    
    // Validasi dual asesor tidak boleh sama
    if ($kd_asesor1 === $kd_asesor2) {
        throw new Exception('Asesor A dan Asesor B tidak boleh sama');
    }
    
    // Validasi sekolah_id
    if ($sekolah_id <= 0) {
        throw new Exception('Sekolah ID tidak valid');
    }
    
    // Validasi tahap - lebih fleksibel untuk input text
    if (empty($tahap)) {
        throw new Exception('Tahap harus diisi');
    }
    
    // Cek apakah sekolah exists dan milik provinsi yang benar
    $check_sekolah = "SELECT sekolah_id, nama_sekolah, npsn FROM sekolah 
                      WHERE sekolah_id = ? AND provinsi_id = ? AND rumpun = 'paud' AND soft_delete = '1'";
    $stmt_sekolah = $conn->prepare($check_sekolah);
    $stmt_sekolah->bind_param("ii", $sekolah_id, $provinsi_id);
    $stmt_sekolah->execute();
    $result_sekolah = $stmt_sekolah->get_result();
    
    if ($result_sekolah->num_rows === 0) {
        throw new Exception('Sekolah tidak ditemukan atau tidak valid');
    }
    $sekolah_data = $result_sekolah->fetch_assoc();
    
    // Cek apakah asesor1 exists dan aktif
    $check_asesor1 = "SELECT kd_asesor1, nm_asesor1, nia1 FROM asesor_1 
                      WHERE kd_asesor1 = ? AND provinsi_id = ? AND soft_delete = '1' AND status_keaktifan_id = '1'";
    $stmt_asesor1 = $conn->prepare($check_asesor1);
    $stmt_asesor1->bind_param("si", $kd_asesor1, $provinsi_id);
    $stmt_asesor1->execute();
    $result_asesor1 = $stmt_asesor1->get_result();
    
    if ($result_asesor1->num_rows === 0) {
        throw new Exception('Asesor A tidak ditemukan atau tidak aktif');
    }
    $asesor1_data = $result_asesor1->fetch_assoc();
    
    // Cek apakah asesor2 exists dan aktif
    $check_asesor2 = "SELECT kd_asesor2, nm_asesor2, nia2 FROM asesor_2 
                      WHERE kd_asesor2 = ? AND provinsi_id = ? AND soft_delete = '1' AND status_keaktifan_id = '1'";
    $stmt_asesor2 = $conn->prepare($check_asesor2);
    $stmt_asesor2->bind_param("si", $kd_asesor2, $provinsi_id);
    $stmt_asesor2->execute();
    $result_asesor2 = $stmt_asesor2->get_result();
    
    if ($result_asesor2->num_rows === 0) {
        throw new Exception('Asesor B tidak ditemukan atau tidak aktif');
    }
    $asesor2_data = $result_asesor2->fetch_assoc();
    
    // Cek duplikasi mapping (sekolah + tahun + tahap)
    $check_duplicate = "SELECT id_mapping FROM mapping_paud_visitasi 
                        WHERE sekolah_id = ? AND tahun_akreditasi = ? AND tahap = ? AND provinsi_id = ?";
    $stmt_duplicate = $conn->prepare($check_duplicate);
    $stmt_duplicate->bind_param("isii", $sekolah_id, $tahun_akreditasi, $tahap, $provinsi_id);
    $stmt_duplicate->execute();
    $result_duplicate = $stmt_duplicate->get_result();
    
    if ($result_duplicate->num_rows > 0) {
        throw new Exception('Mapping untuk sekolah, tahun, dan tahap ini sudah ada');
    }
    
    // Begin transaction
    $conn->autocommit(false);
    
    // Insert mapping visitasi
    $insert_query = "INSERT INTO mapping_paud_visitasi 
                     (sekolah_id, kd_asesor1, kd_asesor2, tgl_mulai_visitasi, tgl_akhir_visitasi, 
                      tahap, no_surat, tgl_surat, tahun_akreditasi, provinsi_id) 
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt_insert = $conn->prepare($insert_query);
    $stmt_insert->bind_param("issssssssi",
        $sekolah_id, $kd_asesor1, $kd_asesor2, $tgl_mulai_visitasi, $tgl_akhir_visitasi,
        $tahap, $no_surat, $tgl_surat, $tahun_akreditasi, $provinsi_id
    );
    
    if (!$stmt_insert->execute()) {
        throw new Exception('Gagal menyimpan data mapping visitasi: ' . $conn->error);
    }
    
    $new_mapping_id = $conn->insert_id;
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Log successful insert
    error_log("Insert Mapping Visitasi Success - ID: $new_mapping_id, Sekolah: " . $sekolah_data['nama_sekolah'] . 
              ", Asesor A: " . $asesor1_data['nm_asesor1'] . ", Asesor B: " . $asesor2_data['nm_asesor2'] . 
              ", User: " . $_SESSION['nm_user']);
    
    echo json_encode([
        'success' => true,
        'message' => 'Data mapping visitasi berhasil disimpan',
        'data' => [
            'id_mapping' => $new_mapping_id,
            'sekolah' => $sekolah_data,
            'asesor1' => $asesor1_data,
            'asesor2' => $asesor2_data,
            'tahun_akreditasi' => $tahun_akreditasi,
            'tahap' => $tahap
        ]
    ]);
    
} catch (Exception $e) {
    // Rollback transaction
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Insert Mapping Visitasi Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
