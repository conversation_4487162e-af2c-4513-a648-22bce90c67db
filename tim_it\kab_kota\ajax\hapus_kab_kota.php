<?php
/**
 * AJAX handler untuk menghapus data kabupaten/kota
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    if (!isset($_POST['id_kota']) || empty($_POST['id_kota'])) {
        throw new Exception('ID Kota tidak valid');
    }
    
    $id_kota = intval($_POST['id_kota']);
    $nm_kota = isset($_POST['nm_kota']) ? trim($_POST['nm_kota']) : '';
    $kota_id = isset($_POST['kota_id']) ? trim($_POST['kota_id']) : '';
    
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Validasi apakah kabupaten/kota ada dan milik provinsi user
    $check_query = "SELECT id_kota, kota_id, nm_kota 
                    FROM kab_kota 
                    WHERE id_kota = ? AND provinsi_id = ?";
    
    $stmt_check = $conn->prepare($check_query);
    $stmt_check->bind_param("ii", $id_kota, $provinsi_id_session);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();
    
    if ($result_check->num_rows == 0) {
        throw new Exception('Data kabupaten/kota tidak ditemukan atau Anda tidak memiliki akses');
    }
    
    $kab_kota_data = $result_check->fetch_assoc();
    
    // Cek apakah kabupaten/kota sedang digunakan di tabel lain
    $usage_checks = [
        'sekolah' => 'SELECT COUNT(*) as count FROM sekolah WHERE kota_id = ? AND soft_delete = 1',
        'asesor' => 'SELECT COUNT(*) as count FROM asesor WHERE kota_id = ? AND soft_delete = 1',
        'provinsi' => 'SELECT COUNT(*) as count FROM provinsi WHERE kota_id = ?'
    ];
    
    foreach ($usage_checks as $table => $query) {
        $stmt_usage = $conn->prepare($query);
        $stmt_usage->bind_param("s", $kab_kota_data['kota_id']);
        $stmt_usage->execute();
        $usage_result = $stmt_usage->get_result();
        $usage_count = $usage_result->fetch_assoc()['count'];
        
        if ($usage_count > 0) {
            throw new Exception("Data kabupaten/kota tidak dapat dihapus karena sedang digunakan di tabel {$table} ({$usage_count} record)");
        }
    }
    
    // Begin transaction
    $conn->autocommit(false);
    
    // Hapus data kabupaten/kota
    $delete_query = "DELETE FROM kab_kota 
                     WHERE id_kota = ? AND provinsi_id = ?";
    
    $stmt_delete = $conn->prepare($delete_query);
    $stmt_delete->bind_param("ii", $id_kota, $provinsi_id_session);
    
    if (!$stmt_delete->execute()) {
        throw new Exception('Gagal menghapus data kabupaten/kota: ' . $stmt_delete->error);
    }
    
    // Cek apakah ada baris yang terpengaruh
    if ($stmt_delete->affected_rows == 0) {
        throw new Exception('Tidak ada data yang dihapus');
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data kabupaten/kota "' . $kab_kota_data['nm_kota'] . '" berhasil dihapus',
        'data' => [
            'id_kota' => $id_kota,
            'kota_id' => $kab_kota_data['kota_id'],
            'nm_kota' => $kab_kota_data['nm_kota'],
            'action' => 'delete'
        ]
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Rollback transaction jika ada error
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Hapus Kab Kota Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
