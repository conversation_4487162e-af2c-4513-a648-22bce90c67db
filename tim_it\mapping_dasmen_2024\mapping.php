<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mapping Asesor Visitasi SM - SIMAK</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    
    <!-- Custom CSS -->
    <style>
        .content-wrapper {
            margin-left: 250px;
            padding: 20px;
        }
        
        .card-header {
            background-color: #17a2b8;
            color: white;
        }
        
        .btn-action {
            margin: 2px;
        }
        
        .table-responsive {
            margin-top: 20px;
        }
        
        .header-with-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .action-buttons .btn {
            white-space: nowrap;
        }

        .btn.btn-input-mapping {
            background-color: white !important;
            color: #007bff !important;
            border: 1px solid #007bff !important;
            transition: all 0.3s ease;
        }

        .btn.btn-input-mapping:hover {
            background-color: #ffc107 !important;
            color: #000000 !important;
            border-color: #ffc107 !important;
        }

        .btn.btn-input-mapping:focus {
            background-color: #ffc107 !important;
            color: #000000 !important;
            border-color: #ffc107 !important;
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
        }

        .btn.btn-input-mapping:active {
            background-color: #ffc107 !important;
            color: #000000 !important;
            border-color: #ffc107 !important;
        }
        
        @media (max-width: 768px) {
            .content-wrapper {
                margin-left: 0;
                padding: 10px;
            }

            .header-with-buttons {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .action-buttons {
                width: 100%;
                justify-content: flex-start;
            }

            .action-buttons .btn {
                flex: 1;
                min-width: 120px;
            }
        }
    </style>
</head>
<body>

<!-- Include header -->
<?php include '../header.php'; ?>

<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

<!-- Main Content -->
<div class="content-wrapper">
    <div class="container-fluid">
        
        <!-- Page Header -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="header-with-buttons">
                            <h4 class="mb-0">
                                <i class="fas fa-users"></i> Mapping Asesor Visitasi SM
                            </h4>

                            <!-- Action Buttons -->
                            <div class="action-buttons">
                                <button type="button" class="btn btn-input-mapping" id="btn-input-mapping">
                                    <i class="fas fa-plus"></i> Input Data Mapping
                                </button>

                                <button type="button" class="btn btn-success" id="btn-export-excel">
                                    <i class="fas fa-file-excel"></i> Export Excel
                                </button>

                                <button type="button" class="btn btn-info" id="btn-import-excel">
                                    <i class="fas fa-file-upload"></i> Import Excel
                                </button>

                                <button type="button" class="btn btn-warning" id="btn-tahun-akreditasi">
                                    <i class="fas fa-calendar-alt"></i> Tahun Akreditasi
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        
                        <!-- DataTable -->
                        <div class="table-responsive">
                            <table id="table-mapping-visitasi" class="table table-striped table-bordered table-hover" style="width:100%">
                                <thead>
                                    <tr>
                                        <th>NO</th>
                                        <th>NPSN</th>
                                        <th>NAMA SEKOLAH</th>
                                        <th>JENJANG</th>
                                        <th>RUMPUN</th>
                                        <th>KAB/KOTA</th>
                                        <th>NIA ASESOR 1</th>
                                        <th>NAMA ASESOR 1</th>
                                        <th>NIA ASESOR 2</th>
                                        <th>NAMA ASESOR 2</th>
                                        <th>TAHUN AKREDITASI</th>
                                        <th>TAHAP VISITASI</th>
                                        <th>AKSI</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Data akan dimuat via AJAX -->
                                </tbody>
                            </table>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
        
    </div>
</div>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<!-- DataTables JS -->
<script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Custom JavaScript -->
<script src="js/mapping_visitasi.js"></script>

<!-- Include footer -->
<?php include '../footer.php'; ?>

</body>
</html>
