<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mapping Asesor Visitasi SM - SIMAK</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    
    <!-- Custom CSS -->
    <style>
        .content-wrapper {
            margin-left: 250px;
            padding: 20px;
        }
        
        .card-header {
            background-color: #17a2b8;
            color: white;
        }
        
        .btn-action {
            margin: 2px;
        }
        
        .table-responsive {
            margin-top: 20px;
        }
        
        .header-with-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .action-buttons .btn {
            white-space: nowrap;
        }

        .btn.btn-input-mapping {
            background-color: white !important;
            color: #007bff !important;
            border: 1px solid #007bff !important;
            transition: all 0.3s ease;
        }

        .btn.btn-input-mapping:hover {
            background-color: #ffc107 !important;
            color: #000000 !important;
            border-color: #ffc107 !important;
        }

        .btn.btn-input-mapping:focus {
            background-color: #ffc107 !important;
            color: #000000 !important;
            border-color: #ffc107 !important;
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
        }

        .btn.btn-input-mapping:active {
            background-color: #ffc107 !important;
            color: #000000 !important;
            border-color: #ffc107 !important;
        }
        
        @media (max-width: 768px) {
            .content-wrapper {
                margin-left: 0;
                padding: 10px;
            }

            .header-with-buttons {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .action-buttons {
                width: 100%;
                justify-content: flex-start;
            }

            .action-buttons .btn {
                flex: 1;
                min-width: 120px;
            }
        }
    </style>
</head>
<body>

<!-- Include header -->
<?php include '../header.php'; ?>

<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

<!-- Main Content -->
<div class="content-wrapper">
    <div class="container-fluid">
        
        <!-- Page Header -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="header-with-buttons">
                            <h4 class="mb-0">
                                <i class="fas fa-users"></i> Mapping Asesor Visitasi SM
                            </h4>

                            <!-- Action Buttons -->
                            <div class="action-buttons">
                                <button type="button" class="btn btn-input-mapping" id="btn-input-mapping">
                                    <i class="fas fa-plus"></i> Input Data Mapping
                                </button>

                                <button type="button" class="btn btn-success" id="btn-export-excel">
                                    <i class="fas fa-file-excel"></i> Export Excel
                                </button>

                                <button type="button" class="btn btn-info" id="btn-import-excel">
                                    <i class="fas fa-file-upload"></i> Import Excel
                                </button>

                                <button type="button" class="btn btn-warning" id="btn-tahun-akreditasi">
                                    <i class="fas fa-calendar-alt"></i> Tahun Akreditasi
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        
                        <!-- DataTable -->
                        <div class="table-responsive">
                            <table id="table-mapping-visitasi" class="table table-striped table-bordered table-hover" style="width:100%">
                                <thead>
                                    <tr>
                                        <th>NO <br> &nbsp;</th>
                                        <th>NPSN <br> &nbsp;</th>
                                        <th>NAMA <br> SEKOLAH</th>
                                        <th>JENJANG <br> &nbsp;</th>
                                        <th>KAB/KOTA <br> &nbsp</th>
                                        <th>NIA <br> ASESOR 1</th>
                                        <th>NAMA <br> ASESOR 1</th>
                                        <th>NIA <br> ASESOR 2</th>
                                        <th>NAMA <br> ASESOR 2</th>
                                        <th>TAHUN <br> AKREDITASI</th>
                                        <th>TAHAP <br> VISITASI</th>
                                        <th>AKSI</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Data akan dimuat via AJAX -->
                                </tbody>
                            </table>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
        
    </div>
</div>

<!-- Modal Input Data Mapping -->
<div class="modal fade" id="modalInputMapping" tabindex="-1" aria-labelledby="modalInputMappingLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background-color: #17a2b8; color: white;">
                <h5 class="modal-title" id="modalInputMappingLabel">
                    <i class="fas fa-plus"></i> Input Data Mapping Visitasi SM
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="formInputMapping">
                    <div class="row">
                        <!-- Kolom Kiri -->
                        <div class="col-md-6">
                            <!-- NPSN Sekolah -->
                            <div class="mb-3">
                                <label for="npsn_sekolah" class="form-label">NPSN Sekolah <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="npsn_sekolah" name="npsn_sekolah" required>
                                <input type="hidden" id="sekolah_id" name="sekolah_id">
                                <div class="invalid-feedback" id="npsn_feedback"></div>
                                <small class="text-muted" id="sekolah_info"></small>
                            </div>

                            <!-- NIA Asesor 1 -->
                            <div class="mb-3">
                                <label for="nia_asesor1" class="form-label">NIA Asesor 1 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nia_asesor1" name="nia_asesor1" required>
                                <input type="hidden" id="kd_asesor1" name="kd_asesor1">
                                <div class="invalid-feedback" id="asesor1_feedback"></div>
                                <small class="text-muted" id="asesor1_info"></small>
                            </div>

                            <!-- NIA Asesor 2 -->
                            <div class="mb-3">
                                <label for="nia_asesor2" class="form-label">NIA Asesor 2 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nia_asesor2" name="nia_asesor2" required>
                                <input type="hidden" id="kd_asesor2" name="kd_asesor2">
                                <div class="invalid-feedback" id="asesor2_feedback"></div>
                                <small class="text-muted" id="asesor2_info"></small>
                            </div>

                            <!-- Tahun Akreditasi -->
                            <div class="mb-3">
                                <label for="tahun_akreditasi" class="form-label">Tahun Akreditasi <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="tahun_akreditasi" name="tahun_akreditasi" required maxlength="4" pattern="[0-9]{4}">
                                <div class="invalid-feedback">Tahun akreditasi wajib diisi (format: YYYY)</div>
                            </div>

                            <!-- Tahap Ke -->
                            <div class="mb-3">
                                <label for="tahap" class="form-label">Tahap Ke <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="tahap" name="tahap" required min="1" max="10">
                                <div class="invalid-feedback">Tahap wajib diisi (1-10)</div>
                            </div>

                            <!-- Tanggal Pra-Visitasi -->
                            <div class="mb-3">
                                <label for="tgl_pra_visitasi" class="form-label">Tanggal Pra-Visitasi</label>
                                <input type="date" class="form-control" id="tgl_pra_visitasi" name="tgl_pra_visitasi">
                            </div>
                        </div>

                        <!-- Kolom Kanan -->
                        <div class="col-md-6">
                            <!-- Tanggal Surat Tugas Pra-Visitasi -->
                            <div class="mb-3">
                                <label for="tgl_surat_tugas_pra_visitasi" class="form-label">Tanggal Surat Tugas Pra-Visitasi</label>
                                <input type="date" class="form-control" id="tgl_surat_tugas_pra_visitasi" name="tgl_surat_tugas_pra_visitasi">
                            </div>

                            <!-- Nomor Surat Tugas Pra-Visitasi -->
                            <div class="mb-3">
                                <label for="no_surat_tugas_pra_visitasi" class="form-label">Nomor Surat Tugas Pra-Visitasi</label>
                                <input type="text" class="form-control" id="no_surat_tugas_pra_visitasi" name="no_surat_tugas_pra_visitasi">
                            </div>

                            <!-- Tanggal Mulai Visitasi -->
                            <div class="mb-3">
                                <label for="tgl_mulai_visitasi" class="form-label">Tanggal Mulai Visitasi</label>
                                <input type="date" class="form-control" id="tgl_mulai_visitasi" name="tgl_mulai_visitasi">
                            </div>

                            <!-- Tanggal Akhir Visitasi -->
                            <div class="mb-3">
                                <label for="tgl_akhir_visitasi" class="form-label">Tanggal Akhir Visitasi</label>
                                <input type="date" class="form-control" id="tgl_akhir_visitasi" name="tgl_akhir_visitasi">
                            </div>

                            <!-- Tanggal Surat Tugas Visitasi -->
                            <div class="mb-3">
                                <label for="tgl_surat_tugas_visitasi" class="form-label">Tanggal Surat Tugas Visitasi</label>
                                <input type="date" class="form-control" id="tgl_surat_tugas_visitasi" name="tgl_surat_tugas_visitasi">
                            </div>

                            <!-- Nomor Surat Tugas Visitasi -->
                            <div class="mb-3">
                                <label for="no_surat_tugas_visitasi" class="form-label">Nomor Surat Tugas Visitasi</label>
                                <input type="text" class="form-control" id="no_surat_tugas_visitasi" name="no_surat_tugas_visitasi">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> Batal
                </button>
                <button type="button" class="btn btn-primary" id="btnSimpanMapping">
                    <i class="fas fa-save"></i> Simpan Data
                </button>
            </div>
        </div>
    </div>
</div>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<!-- DataTables JS -->
<script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Custom JavaScript -->
<script src="js/mapping_visitasi.js"></script>

<!-- Include footer -->
<?php include '../footer.php'; ?>

</body>
</html>
