<?php
/**
 * AJAX handler untuk DataTables Server-side Processing - Data Asesor
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Ambil parameter dari DataTables
    $draw = isset($_POST['draw']) ? intval($_POST['draw']) : 1;
    $start = isset($_POST['start']) ? intval($_POST['start']) : 0;
    $length = isset($_POST['length']) ? intval($_POST['length']) : 10;
    $search_value = isset($_POST['search']['value']) ? $_POST['search']['value'] : '';
    $order_column = isset($_POST['order'][0]['column']) ? intval($_POST['order'][0]['column']) : 6;
    $order_dir = isset($_POST['order'][0]['dir']) ? $_POST['order'][0]['dir'] : 'asc';
    
    // Definisi kolom untuk sorting
    $columns = [
        0 => 'a.id_asesor', // NO (tidak bisa di-sort)
        1 => 'a.nia',
        2 => 'a.nm_asesor',
        3 => 'a.jk',
        4 => 'k.nm_kota',
        5 => 'a.unit_kerja',
        6 => 'a.rumpun',
        7 => 'sk.nm_status',
        8 => 'a.id_asesor' // AKSI (tidak bisa di-sort)
    ];
    
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];

    // Base query dengan JOIN dan filter
    $base_query = "FROM asesor a
                   LEFT JOIN kab_kota k ON a.kota_id = k.kota_id
                   LEFT JOIN status_keaktifan sk ON a.status_keaktifan_id = sk.status_keaktifan_id
                   WHERE a.provinsi_id = {$provinsi_id_session} AND a.soft_delete = '1'";
    
    // Search functionality
    $search_query = "";
    $bind_params = [];
    $bind_types = "";
    
    if (!empty($search_value)) {
        $search_query = " AND (a.nia LIKE ? OR a.nm_asesor LIKE ? OR a.jk LIKE ? OR k.nm_kota LIKE ? OR a.unit_kerja LIKE ? OR a.rumpun LIKE ? OR sk.nm_status LIKE ?)";
        $search_param = "%{$search_value}%";
        $bind_params = [$search_param, $search_param, $search_param, $search_param, $search_param, $search_param, $search_param];
        $bind_types = "sssssss";
    }
    
    // Query untuk menghitung total records
    $total_query = "SELECT COUNT(*) as total " . $base_query;
    $total_result = $conn->query($total_query);
    $total_records = $total_result->fetch_assoc()['total'];
    
    // Query untuk menghitung filtered records
    $filtered_query = "SELECT COUNT(*) as total " . $base_query . $search_query;
    if (!empty($bind_params)) {
        $filtered_stmt = $conn->prepare($filtered_query);
        if (!empty($bind_types)) {
            $filtered_stmt->bind_param($bind_types, ...$bind_params);
        }
        $filtered_stmt->execute();
        $filtered_records = $filtered_stmt->get_result()->fetch_assoc()['total'];
    } else {
        $filtered_result = $conn->query($filtered_query);
        $filtered_records = $filtered_result->fetch_assoc()['total'];
    }
    
    // Query untuk mengambil data dengan pagination dan sorting
    $data_query = "SELECT a.id_asesor, a.nia, a.nm_asesor, a.jk, a.unit_kerja, a.rumpun, 
                          a.status_keaktifan_id, k.nm_kota, sk.nm_status
                   " . $base_query . $search_query;
    
    // Add sorting
    if (isset($columns[$order_column])) {
        $data_query .= " ORDER BY " . $columns[$order_column] . " " . $order_dir;
    }
    
    // Add pagination
    $data_query .= " LIMIT {$start}, {$length}";
    
    // Execute query
    if (!empty($bind_params)) {
        $data_stmt = $conn->prepare($data_query);
        if (!empty($bind_types)) {
            $data_stmt->bind_param($bind_types, ...$bind_params);
        }
        $data_stmt->execute();
        $data_result = $data_stmt->get_result();
    } else {
        $data_result = $conn->query($data_query);
    }
    
    // Siapkan data untuk response
    $data = [];
    while ($row = $data_result->fetch_assoc()) {
        $data[] = [
            'id_asesor' => $row['id_asesor'],
            'nia' => $row['nia'],
            'nm_asesor' => $row['nm_asesor'],
            'jk' => $row['jk'],
            'unit_kerja' => $row['unit_kerja'],
            'rumpun' => $row['rumpun'],
            'status_keaktifan_id' => $row['status_keaktifan_id'],
            'nm_kota' => $row['nm_kota'],
            'nm_status' => $row['nm_status']
        ];
    }
    
    // Response JSON untuk DataTables
    $response = [
        "draw" => $draw,
        "recordsTotal" => intval($total_records),
        "recordsFiltered" => intval($filtered_records),
        "data" => $data
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Log error
    error_log("DataTables Asesor Error: " . $e->getMessage());
    
    // Response error
    $error_response = [
        "draw" => isset($draw) ? $draw : 1,
        "recordsTotal" => 0,
        "recordsFiltered" => 0,
        "data" => [],
        "error" => $e->getMessage()
    ];
    
    echo json_encode($error_response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
