/**
 * JavaScript untuk halaman Data Jenjang
 * Menggunakan DataTables Server-side Processing
 */

$(document).ready(function() {
    
    // Inisialisasi DataTable
    var table = $('#table-jenjang').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "ajax/get_jenjang.php",
            "type": "POST",
            "error": function(xhr, error, thrown) {
                console.error('DataTables Error:', error);
                showAlert('error', 'Terjadi kesalahan saat memuat data: ' + error);
            }
        },
        "columns": [
            { 
                "data": null,
                "orderable": false,
                "searchable": false,
                "render": function(data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { 
                "data": "jenjang_id",
                "className": "text-center",
                "render": function(data, type, row) {
                    return '<span class="badge badge-primary">' + data + '</span>';
                }
            },
            { 
                "data": "nm_jenjang",
                "render": function(data, type, row) {
                    return '<strong>' + data + '</strong>';
                }
            },
            { 
                "data": null,
                "orderable": false,
                "searchable": false,
                "className": "text-center",
                "render": function(data, type, row) {
                    return `
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-info btn-sm mr-2" onclick="showDetail(${row.id_jenjang})" title="Detail">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button type="button" class="btn btn-warning btn-sm mr-2" onclick="loadEditForm(${row.id_jenjang})" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-danger btn-sm mr-2" onclick="confirmDelete(${row.id_jenjang}, '${row.nm_jenjang}')" title="Hapus">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `;
                }
            }
        ],
        "order": [[1, "asc"]],
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]],
        "language": {
            "processing": "Sedang memproses...",
            "lengthMenu": "Tampilkan _MENU_ data per halaman",
            "zeroRecords": "Data tidak ditemukan",
            "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            "infoEmpty": "Menampilkan 0 sampai 0 dari 0 data",
            "infoFiltered": "(disaring dari _MAX_ total data)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        }
    });

    // Event handler untuk tombol tambah data
    $('#btn-add').click(function() {
        showModalTambah();
    });

    // Event handler untuk form submit tambah
    $('#form-tambah-jenjang').submit(function(e) {
        e.preventDefault();
        simpanDataJenjang();
    });

    // Event handler untuk konfirmasi hapus
    $('#btn-konfirmasi-hapus').click(function() {
        var jenjangId = $(this).data('jenjang-id');
        var namaJenjang = $(this).data('nama-jenjang');

        // Tutup modal konfirmasi
        $('#modal-konfirmasi-hapus').modal('hide');

        // Proses hapus
        deleteJenjang(jenjangId, namaJenjang);
    });

});

/**
 * Fungsi untuk menampilkan modal tambah
 */
function showModalTambah() {
    // Reset form
    $('#form-tambah-jenjang')[0].reset();
    
    // Tampilkan modal
    $('#modal-tambah').modal('show');
    
    // Focus ke field pertama
    setTimeout(function() {
        $('#jenjang_id').focus();
    }, 500);
}

/**
 * Fungsi untuk load form edit via AJAX
 */
function loadEditForm(jenjangId) {
    $('#modal-edit').modal('show');
    
    // Reset content
    $('#modal-edit .modal-content').html(
        '<div class="modal-body text-center">' +
        '<i class="fas fa-spinner fa-spin fa-2x"></i>' +
        '<p class="mt-2">Memuat form edit...</p>' +
        '</div>'
    );

    // Load form via AJAX
    $.ajax({
        url: 'ajax/get_edit_form.php',
        type: 'POST',
        data: { id_jenjang: jenjangId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Replace modal content dengan form yang sudah ter-populate
                $('#modal-edit .modal-content').html(response.html);
                
                // Bind event handler untuk form edit
                bindEditFormEvents();
                
            } else {
                $('#modal-edit .modal-content').html(`
                    <div class="modal-header bg-danger">
                        <h5 class="modal-title text-white">
                            <i class="fas fa-exclamation-triangle"></i> Error
                        </h5>
                        <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                        <p class="mt-2">${response.message}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    </div>
                `);
            }
        },
        error: function(xhr, status, error) {
            $('#modal-edit .modal-content').html(`
                <div class="modal-header bg-danger">
                    <h5 class="modal-title text-white">
                        <i class="fas fa-exclamation-triangle"></i> Error
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                    <p class="mt-2">Terjadi kesalahan saat memuat form edit</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                </div>
            `);
        }
    });
}

/**
 * Fungsi untuk bind event handler form edit
 */
function bindEditFormEvents() {
    // Event handler untuk form submit edit
    $('#form-edit-jenjang').off('submit').on('submit', function(e) {
        e.preventDefault();
        updateDataJenjang();
    });
    
    // Event handler untuk tombol update
    $('#btn-update').off('click').on('click', function(e) {
        e.preventDefault();
        updateDataJenjang();
    });
}

/**
 * Fungsi untuk update data jenjang
 */
function updateDataJenjang() {
    // Disable tombol submit
    $('#btn-update').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Mengupdate...');

    // Serialize form data
    var formData = $('#form-edit-jenjang').serialize();

    // AJAX request
    $.ajax({
        url: 'ajax/update_jenjang.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Tutup modal
                $('#modal-edit').modal('hide');

                // Reload DataTable
                $('#table-jenjang').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat mengupdate data');
        },
        complete: function() {
            // Enable tombol submit
            $('#btn-update').prop('disabled', false).html('<i class="fas fa-save"></i> Update Data');
        }
    });
}

/**
 * Fungsi untuk menyimpan data jenjang
 */
function simpanDataJenjang() {
    // Disable tombol submit
    $('#btn-simpan').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    // Serialize form data
    var formData = $('#form-tambah-jenjang').serialize();

    // AJAX request
    $.ajax({
        url: 'ajax/simpan_jenjang.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Tutup modal
                $('#modal-tambah').modal('hide');

                // Reload DataTable
                $('#table-jenjang').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat menyimpan data');
        },
        complete: function() {
            // Enable tombol submit
            $('#btn-simpan').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Data');
        }
    });
}

/**
 * Fungsi untuk konfirmasi hapus data menggunakan modal
 */
function confirmDelete(jenjangId, namaJenjang) {
    // Set data ke tombol konfirmasi
    $('#btn-konfirmasi-hapus').data('jenjang-id', jenjangId);
    $('#btn-konfirmasi-hapus').data('nama-jenjang', namaJenjang);

    // Set nama jenjang di modal
    $('#nama-jenjang-hapus').text(namaJenjang);

    // Tampilkan modal konfirmasi
    $('#modal-konfirmasi-hapus').modal('show');
}

/**
 * Fungsi untuk menghapus data jenjang (soft delete)
 */
function deleteJenjang(jenjangId, namaJenjang) {
    // Show loading
    showAlert('info', 'Sedang menghapus data...');

    $.ajax({
        url: 'ajax/hapus_jenjang.php',
        type: 'POST',
        data: {
            id_jenjang: jenjangId,
            nm_jenjang: namaJenjang
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Reload DataTable
                $('#table-jenjang').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat menghapus data');
        }
    });
}

/**
 * Fungsi untuk menampilkan detail jenjang
 */
function showDetail(jenjangId) {
    $('#modal-detail').modal('show');

    // Reset content
    $('#modal-detail-content').html(
        '<div class="text-center">' +
        '<i class="fas fa-spinner fa-spin fa-2x"></i>' +
        '<p class="mt-2">Memuat data...</p>' +
        '</div>'
    );

    // Load detail via AJAX
    $.ajax({
        url: 'ajax/get_detail_jenjang.php',
        type: 'POST',
        data: { id_jenjang: jenjangId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                displayDetailContent(response.data);
            } else {
                $('#modal-detail-content').html(
                    '<div class="alert alert-danger">' +
                    '<i class="fas fa-exclamation-triangle"></i> ' +
                    response.message +
                    '</div>'
                );
            }
        },
        error: function(xhr, status, error) {
            $('#modal-detail-content').html(
                '<div class="alert alert-danger">' +
                '<i class="fas fa-exclamation-triangle"></i> ' +
                'Terjadi kesalahan saat memuat detail jenjang' +
                '</div>'
            );
        }
    });
}

/**
 * Fungsi untuk menampilkan konten detail jenjang
 */
function displayDetailContent(data) {
    var html = '<div class="row">';

    html += '<div class="col-md-12">';
    html += createDetailRow('ID Jenjang', data.id_jenjang);
    html += createDetailRow('Kode Jenjang', data.jenjang_id);
    html += createDetailRow('Nama Jenjang', data.nm_jenjang);
    html += '</div>';

    html += '</div>';

    $('#modal-detail-content').html(html);
}

/**
 * Fungsi helper untuk membuat baris detail
 */
function createDetailRow(label, value) {
    return '<div class="modal-detail-row">' +
           '<div class="modal-detail-label">' + label + ':</div>' +
           '<div class="modal-detail-value">' + value + '</div>' +
           '</div>';
}

/**
 * Fungsi untuk menampilkan notifikasi modal
 */
function showNotification(type, message) {
    if (type === 'success') {
        $('#notifikasi-sukses-message').text(message);
        $('#modal-notifikasi-sukses').modal('show');
    } else if (type === 'error') {
        $('#notifikasi-error-message').text(message);
        $('#modal-notifikasi-error').modal('show');
    }
}

/**
 * Fungsi untuk menampilkan alert (fallback untuk loading/info)
 */
function showAlert(type, message) {
    // Untuk success dan error, gunakan modal
    if (type === 'success' || type === 'error') {
        showNotification(type, message);
        return;
    }

    // Untuk info/warning, gunakan alert biasa
    var alertClass = 'alert-info';
    var icon = 'fas fa-info-circle';

    switch(type) {
        case 'warning':
            alertClass = 'alert-warning';
            icon = 'fas fa-exclamation-circle';
            break;
    }

    var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                    '<i class="' + icon + '"></i> ' + message +
                    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                    '<span aria-hidden="true">&times;</span>' +
                    '</button>' +
                    '</div>';

    $('#alert-container').html(alertHtml);

    // Auto hide after 3 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 3000);
}
