<?php
/**
 * AJAX handler untuk menghapus data pengurus (soft delete)
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    if (!isset($_POST['id_pengurus']) || empty($_POST['id_pengurus'])) {
        throw new Exception('ID Pengurus tidak valid');
    }
    
    $id_pengurus = intval($_POST['id_pengurus']);
    $nm_pengurus = isset($_POST['nm_pengurus']) ? trim($_POST['nm_pengurus']) : '';
    $kd_pengurus = isset($_POST['kd_pengurus']) ? trim($_POST['kd_pengurus']) : '';
    
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Validasi apakah pengurus ada dan milik provinsi user
    $check_query = "SELECT id_pengurus, kd_pengurus, nm_pengurus, soft_delete 
                    FROM pengurus 
                    WHERE id_pengurus = ? AND provinsi_id = ?";
    
    $stmt_check = $conn->prepare($check_query);
    $stmt_check->bind_param("ii", $id_pengurus, $provinsi_id_session);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();
    
    if ($result_check->num_rows == 0) {
        throw new Exception('Data pengurus tidak ditemukan atau Anda tidak memiliki akses');
    }
    
    $pengurus_data = $result_check->fetch_assoc();
    
    // Cek apakah sudah dihapus sebelumnya
    if ($pengurus_data['soft_delete'] == '0') {
        throw new Exception('Data pengurus sudah dihapus sebelumnya');
    }
    
    // Begin transaction
    $conn->autocommit(false);
    
    // Update soft_delete menjadi '0' (hapus)
    $delete_query = "UPDATE pengurus 
                     SET soft_delete = '0' 
                     WHERE id_pengurus = ? AND provinsi_id = ?";
    
    $stmt_delete = $conn->prepare($delete_query);
    $stmt_delete->bind_param("ii", $id_pengurus, $provinsi_id_session);
    
    if (!$stmt_delete->execute()) {
        throw new Exception('Gagal menghapus data pengurus: ' . $stmt_delete->error);
    }
    
    // Cek apakah ada baris yang terpengaruh
    if ($stmt_delete->affected_rows == 0) {
        throw new Exception('Tidak ada data yang dihapus');
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data pengurus "' . $pengurus_data['nm_pengurus'] . '" berhasil dihapus',
        'data' => [
            'id_pengurus' => $id_pengurus,
            'kd_pengurus' => $pengurus_data['kd_pengurus'],
            'nm_pengurus' => $pengurus_data['nm_pengurus'],
            'action' => 'soft_delete'
        ]
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Rollback transaction jika ada error
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Hapus Pengurus Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
