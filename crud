buatlah CRUD modul "jenjang", tulislah kode sumber di folder tim_it/jenjang/jenjang.php, tersedia juga direktori ajax dan direktori js untuk menulis backend, anda boleh meniru pola tim_it/dasmen/sekolah.php untuk menulis kode sumber html modul jenjang mulai dari 
====================================
<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');
?>

// Include header
<?php include '../header.php'; ?>


<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

terakhir di paling bawah <!-- Include footer -->
<?php include '../footer.php'; ?>
=================================================================================================================
buatlah modul "Data Asesor" pada direktori tim_it/asesor/asesor.php, juga tersedia sub direktori ajax dan js, tabel header yang digunakan adalah NO, NIA, NAMA ASESOR, JENIS KELAMIN, KAB/KOTA, UNIT KERJA, RUMPUN, STATUS KEAKTIFAN, AKSI. Untuk kolom AKSI tampilkan tombol ikon detail, edit dan hapus sampai disini apakah anda sudah mengerti, ataukah ada yang perlu ditanyakan?
============================================
oh iya lupa, tambahkan
<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');
?>

// Include header
<?php include '../header.php'; ?>


<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

terakhir di paling bawah <!-- Include footer -->
<?php include '../footer.php'; ?>
=================================================================================================================
📝 Lesson Learned:
✅ Yang Harus Saya Lakukan:
Ikuti instruksi persis sesuai yang diminta
Tanya dulu sebelum menambah fitur yang tidak diminta
Focus on the task yang spesifik diberikan
Jangan overthinking atau menambah validasi yang tidak perlu
❌ Yang Tidak Boleh Saya Lakukan:
Improvisasi sendiri tanpa perintah
Menambah fitur yang tidak diminta
Overthinking logika yang sudah jalan
Mengubah behavior yang sudah benar
🎯 Moving Forward:
Saya akan selalu:

Dengarkan instruksi dengan teliti
Eksekusi persis sesuai permintaan
Tanya konfirmasi jika ada yang tidak jelas
Jangan menambah-nambah yang tidak diminta
=================================================================================================================
2. letak file sk akreditasi untuk preview tersimpan di ../../../simak/files/sk_akreditasi/
3. letak file sk akreditasi untuk upload tersipan di ../../../../simak/files/sk_akreditasi/
=================================================================================================================