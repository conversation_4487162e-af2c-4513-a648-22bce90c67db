<?php
/**
 * AJAX handler untuk menyimpan data sekolah baru
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    $required_fields = ['npsn', 'nama_sekolah', 'jenjang_id', 'tipe_sekolah_id', 'status_sekolah_id', 'kota_id'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            throw new Exception("Field {$field} harus diisi");
        }
    }
    
    // Sanitize dan ambil data dari POST
    $npsn = trim($_POST['npsn']);
    $nama_sekolah = trim($_POST['nama_sekolah']);
    $jenjang_id = intval($_POST['jenjang_id']);
    $tipe_sekolah_id = intval($_POST['tipe_sekolah_id']);
    $status_sekolah_id = intval($_POST['status_sekolah_id']);
    $kota_id = trim($_POST['kota_id']);
    
    // Data opsional
    $kecamatan = isset($_POST['kecamatan']) ? trim($_POST['kecamatan']) : null;
    $desa_kelurahan = isset($_POST['desa_kelurahan']) ? trim($_POST['desa_kelurahan']) : null;
    $alamat = isset($_POST['alamat']) ? trim($_POST['alamat']) : null;
    $nama_yayasan = isset($_POST['nama_yayasan']) ? trim($_POST['nama_yayasan']) : null;
    $no_akte = isset($_POST['no_akte']) ? trim($_POST['no_akte']) : null;
    $tahun_berdiri = isset($_POST['tahun_berdiri']) ? trim($_POST['tahun_berdiri']) : null;
    
    // Data kontak
    $nama_kepsek = isset($_POST['nama_kepsek']) ? trim($_POST['nama_kepsek']) : null;
    $no_hp_kepsek = isset($_POST['no_hp_kepsek']) ? trim($_POST['no_hp_kepsek']) : null;
    $no_wa_kepsek = isset($_POST['no_wa_kepsek']) ? trim($_POST['no_wa_kepsek']) : null;
    $nama_operator = isset($_POST['nama_operator']) ? trim($_POST['nama_operator']) : null;
    $no_hp_operator = isset($_POST['no_hp_operator']) ? trim($_POST['no_hp_operator']) : null;
    $email = isset($_POST['email']) ? trim($_POST['email']) : null;
    
    // Data fixed
    $rumpun = 'dasmen';
    $provinsi_id = $_SESSION['provinsi_id']; // Dari session user
    $status_keaktifan_id = 1; // Aktif
    $soft_delete = 1; // Aktif
    
    // Validasi NPSN unik
    $check_npsn = "SELECT sekolah_id FROM sekolah WHERE npsn = ? AND soft_delete = 1";
    $stmt_check = $conn->prepare($check_npsn);
    $stmt_check->bind_param("s", $npsn);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();
    
    if ($result_check->num_rows > 0) {
        throw new Exception('NPSN sudah terdaftar dalam sistem');
    }
    
    // Validasi email format jika diisi
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Format email tidak valid');
    }

    // Cek duplikasi di tabel user - nianpsn (NPSN)
    $check_user_npsn = "SELECT kd_user FROM user WHERE nianpsn = ? AND soft_delete = '1'";
    $stmt_check_user_npsn = $conn->prepare($check_user_npsn);
    $stmt_check_user_npsn->bind_param("s", $npsn);
    $stmt_check_user_npsn->execute();
    $result_check_user_npsn = $stmt_check_user_npsn->get_result();

    if ($result_check_user_npsn->num_rows > 0) {
        throw new Exception('NPSN sudah terdaftar sebagai user, gunakan NPSN yang berbeda');
    }

    // Begin transaction
    $conn->autocommit(false);
    
    // Query insert
    $sql = "INSERT INTO sekolah (
                npsn, nama_sekolah, jenjang_id, rumpun, alamat,
                tipe_sekolah_id, status_sekolah_id, provinsi_id, kota_id,
                desa_kelurahan, kecamatan, nama_kepsek, no_hp_kepsek, no_wa_kepsek,
                nama_operator, no_hp_operator, email, nama_yayasan, no_akte, tahun_berdiri,
                status_keaktifan_id, soft_delete
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssissiiissssssssssssii",
        $npsn, $nama_sekolah, $jenjang_id, $rumpun, $alamat,
        $tipe_sekolah_id, $status_sekolah_id, $provinsi_id, $kota_id,
        $desa_kelurahan, $kecamatan, $nama_kepsek, $no_hp_kepsek, $no_wa_kepsek,
        $nama_operator, $no_hp_operator, $email, $nama_yayasan, $no_akte, $tahun_berdiri,
        $status_keaktifan_id, $soft_delete
    );
    
    if (!$stmt->execute()) {
        throw new Exception('Gagal menyimpan data sekolah: ' . $stmt->error);
    }

    $sekolah_id = $conn->insert_id;

    // Cek duplikasi di tabel user - kd_user (sekolah_id) setelah mendapat ID
    $check_user_kd = "SELECT kd_user FROM user WHERE kd_user = ? AND soft_delete = '1'";
    $stmt_check_user_kd = $conn->prepare($check_user_kd);
    $stmt_check_user_kd->bind_param("s", $sekolah_id);
    $stmt_check_user_kd->execute();
    $result_check_user_kd = $stmt_check_user_kd->get_result();

    if ($result_check_user_kd->num_rows > 0) {
        throw new Exception('ID Sekolah sudah terdaftar sebagai user');
    }

    // Sinkronisasi ke tabel user
    $sql_user = "INSERT INTO user (
                     kd_user, nianpsn, nm_user, jenjang_id, status_keaktifan_id,
                     provinsi_id, kota_id, username, password, level, soft_delete
                 ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    $stmt_user = $conn->prepare($sql_user);
    $level = 'Sekolah';
    $soft_delete_user = '1';
    $stmt_user->bind_param("sssiiisssss",
        $sekolah_id, $npsn, $nama_sekolah, $jenjang_id, $status_keaktifan_id,
        $provinsi_id, $kota_id, $npsn, $npsn, $level, $soft_delete_user
    );

    if (!$stmt_user->execute()) {
        throw new Exception('Gagal sinkronisasi ke tabel user: ' . $stmt_user->error);
    }

    // Commit transaction jika semua berhasil
    $conn->commit();
    $conn->autocommit(true);
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data sekolah berhasil disimpan',
        'data' => [
            'sekolah_id' => $sekolah_id,
            'npsn' => $npsn,
            'nama_sekolah' => $nama_sekolah
        ]
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Rollback transaction jika ada error
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Simpan Sekolah Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
}
?>
