<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Get provinsi_id from session
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Query untuk mendapatkan tahun akreditasi aktif untuk validasi PAUD
    $query = "SELECT id_mapping_tahun, nama_tahun, provinsi_id 
              FROM mapping_paud_validasi_tahun 
              WHERE provinsi_id = ? 
              ORDER BY id_mapping_tahun DESC 
              LIMIT 1";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $tahun_data = $result->fetch_assoc();
        
        echo json_encode([
            'success' => true,
            'data' => $tahun_data,
            'message' => 'Tahun akreditasi validasi PAUD ditemukan'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'data' => null,
            'message' => 'Belum ada tahun akreditasi validasi PAUD yang diset untuk provinsi ini'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Get Tahun Akreditasi Validasi Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'data' => null,
        'message' => 'Terjadi kesalahan saat memuat tahun akreditasi'
    ]);
}

$conn->close();
?>
