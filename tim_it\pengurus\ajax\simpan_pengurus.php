<?php
/**
 * AJAX handler untuk menyimpan data pengurus baru
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    $required_fields = ['nm_pengurus', 'kota_id', 'jk'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            throw new Exception("Field {$field} harus diisi");
        }
    }

    // Validasi khusus untuk status_keaktifan_id (boleh "0")
    if (!isset($_POST['status_keaktifan_id']) || $_POST['status_keaktifan_id'] === '') {
        throw new Exception("Field status_keaktifan_id harus diisi");
    }
    
    // Sanitize dan ambil data dari POST
    $kd_pengurus = isset($_POST['kd_pengurus']) && !empty(trim($_POST['kd_pengurus'])) ? trim($_POST['kd_pengurus']) : uniqid();
    $nm_pengurus = trim($_POST['nm_pengurus']);
    $ktp = isset($_POST['ktp']) ? trim($_POST['ktp']) : '';
    $unit_kerja = isset($_POST['unit_kerja']) ? trim($_POST['unit_kerja']) : '';
    $kota_id = trim($_POST['kota_id']);
    $no_hp = isset($_POST['no_hp']) ? trim($_POST['no_hp']) : '';
    $no_wa = isset($_POST['no_wa']) ? trim($_POST['no_wa']) : '';
    $tempat_lahir = isset($_POST['tempat_lahir']) ? trim($_POST['tempat_lahir']) : '';
    $tgl_lahir = isset($_POST['tgl_lahir']) && !empty($_POST['tgl_lahir']) ? $_POST['tgl_lahir'] : '0000-00-00';
    $jabatan = isset($_POST['jabatan']) ? trim($_POST['jabatan']) : '';
    $jabatan_kantor_asal = isset($_POST['jabatan_kantor_asal']) ? trim($_POST['jabatan_kantor_asal']) : '';
    $pendidikan = isset($_POST['pendidikan']) ? trim($_POST['pendidikan']) : '';
    $jk = trim($_POST['jk']);
    $alamat_kantor = isset($_POST['alamat_kantor']) ? trim($_POST['alamat_kantor']) : '';
    $alamat_rumah = isset($_POST['alamat_rumah']) ? trim($_POST['alamat_rumah']) : '';
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $status_keaktifan_id = trim($_POST['status_keaktifan_id']);
    $sebab = isset($_POST['sebab']) ? trim($_POST['sebab']) : '';
    $no_urut = isset($_POST['no_urut']) && !empty($_POST['no_urut']) ? intval($_POST['no_urut']) : 0;
    
    // Data fixed
    $provinsi_id = $_SESSION['provinsi_id']; // Dari session user
    $kd_user = ''; // Kosong untuk sementara
    $soft_delete = '1'; // Aktif
    
    // Validasi jenis kelamin
    $valid_jk = ['Pria', 'Wanita'];
    if (!in_array($jk, $valid_jk)) {
        throw new Exception('Jenis kelamin harus Pria atau Wanita');
    }
    
    // Validasi status keaktifan
    $valid_status = ['0', '1', '2'];
    if (!in_array($status_keaktifan_id, $valid_status)) {
        throw new Exception('Status keaktifan tidak valid');
    }
    
    // Validasi kota_id ada dan sesuai provinsi session
    $check_kota = "SELECT kota_id FROM kab_kota WHERE kota_id = ? AND provinsi_id = ?";
    $stmt_check_kota = $conn->prepare($check_kota);
    $stmt_check_kota->bind_param("si", $kota_id, $provinsi_id);
    $stmt_check_kota->execute();
    $result_check_kota = $stmt_check_kota->get_result();
    
    if ($result_check_kota->num_rows == 0) {
        throw new Exception('Kabupaten/Kota tidak valid atau tidak sesuai dengan provinsi Anda');
    }
    
    // Begin transaction
    $conn->autocommit(false);
    
    // Query insert
    $sql = "INSERT INTO pengurus (
                kd_pengurus, nm_pengurus, ktp, unit_kerja, kota_id, provinsi_id,
                no_hp, no_wa, tempat_lahir, tgl_lahir, jabatan, jabatan_kantor_asal,
                pendidikan, jk, alamat_kantor, alamat_rumah, email, status_keaktifan_id,
                sebab, no_urut, kd_user, soft_delete
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sssssissssssssssssiiss",
        $kd_pengurus, $nm_pengurus, $ktp, $unit_kerja, $kota_id, $provinsi_id,
        $no_hp, $no_wa, $tempat_lahir, $tgl_lahir, $jabatan, $jabatan_kantor_asal,
        $pendidikan, $jk, $alamat_kantor, $alamat_rumah, $email, $status_keaktifan_id,
        $sebab, $no_urut, $kd_user, $soft_delete
    );
    
    if (!$stmt->execute()) {
        throw new Exception('Gagal menyimpan data pengurus: ' . $stmt->error);
    }
    
    $pengurus_id = $conn->insert_id;
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data pengurus "' . $nm_pengurus . '" berhasil disimpan',
        'data' => [
            'id_pengurus' => $pengurus_id,
            'kd_pengurus' => $kd_pengurus,
            'nm_pengurus' => $nm_pengurus
        ]
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Rollback transaction jika ada error
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Simpan Pengurus Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
