<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi parameter
    if (!isset($_GET['nia']) || empty(trim($_GET['nia']))) {
        throw new Exception('NIA harus diisi');
    }
    
    $nia = $conn->real_escape_string(trim($_GET['nia']));
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Query lookup NIA di tabel asesor_1
    $query1 = "SELECT kd_asesor1 as kd_asesor, nia1 as nia, nm_asesor1 as nm_asesor, 
                      unit_kerja, k.nm_kota
               FROM asesor_1 a1
               LEFT JOIN kab_kota k ON a1.kota_id1 = k.kota_id
               WHERE a1.nia1 = ? AND a1.provinsi_id = ? AND a1.soft_delete = '1'";
    
    $stmt1 = $conn->prepare($query1);
    $stmt1->bind_param("si", $nia, $provinsi_id);
    $stmt1->execute();
    $result1 = $stmt1->get_result();
    
    if ($result1->num_rows > 0) {
        $data = $result1->fetch_assoc();
        
        echo json_encode([
            'success' => true,
            'data' => [
                'kd_asesor' => $data['kd_asesor'],
                'nia' => $data['nia'],
                'nm_asesor' => $data['nm_asesor'],
                'unit_kerja' => $data['unit_kerja'],
                'nm_kota' => $data['nm_kota']
            ]
        ]);
        exit;
    }
    
    // Jika tidak ditemukan di asesor_1, cari di asesor_2
    $query2 = "SELECT kd_asesor2 as kd_asesor, nia2 as nia, nm_asesor2 as nm_asesor, 
                      unit_kerja, k.nm_kota
               FROM asesor_2 a2
               LEFT JOIN kab_kota k ON a2.kota_id2 = k.kota_id
               WHERE a2.nia2 = ? AND a2.provinsi_id = ? AND a2.soft_delete = '1'";
    
    $stmt2 = $conn->prepare($query2);
    $stmt2->bind_param("si", $nia, $provinsi_id);
    $stmt2->execute();
    $result2 = $stmt2->get_result();
    
    if ($result2->num_rows > 0) {
        $data = $result2->fetch_assoc();
        
        echo json_encode([
            'success' => true,
            'data' => [
                'kd_asesor' => $data['kd_asesor'],
                'nia' => $data['nia'],
                'nm_asesor' => $data['nm_asesor'],
                'unit_kerja' => $data['unit_kerja'],
                'nm_kota' => $data['nm_kota']
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'NIA tidak ditemukan atau tidak aktif'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
