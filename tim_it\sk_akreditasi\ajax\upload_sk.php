<?php
session_start();
require_once '../../../koneksi.php';

// Cek session dan level
if (!isset($_SESSION['kd_user']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Ambil provinsi_id dari session
$provinsi_id = $_SESSION['provinsi_id'];

if ($_POST) {
    // Baca Variabel Data Form dengan real_escape_string
    $no_sk_akreditasi = $conn->real_escape_string($_POST['no_sk_akreditasi']);
    $tgl_sk_akreditasi = $conn->real_escape_string($_POST['tgl_sk_akreditasi']);
    $tentang = $conn->real_escape_string($_POST['tentang']);
    $tahun_akreditasi = $conn->real_escape_string($_POST['tahun_akreditasi']);
    $nm_lembaga = $conn->real_escape_string($_POST['nm_lembaga']);
    $nama_file = $conn->real_escape_string($_FILES['nama_file']['name']);
    
    // Upload file terlebih dahulu
    move_uploaded_file($_FILES['nama_file']['tmp_name'], '../../../../simak/files/sk_akreditasi/' . $_FILES['nama_file']['name']);

    // Cek duplikasi nama file
    $input1 = "SELECT * FROM e_arsip_sk_akreditasi WHERE nama_file='$nama_file'";
    $result2 = $conn->query($input1);
    
    if ($result2->num_rows > 0) {
        echo json_encode(['success' => false, 'message' => 'Nama file SK sudah digunakan, silakan upload dengan nama yang berbeda']);
    } else {
        // Insert ke database
        $input2 = "INSERT INTO e_arsip_sk_akreditasi (no_sk_akreditasi, tgl_sk_akreditasi, tentang, tahun_akreditasi, nama_file, nm_lembaga, provinsi_id)
                   VALUES ('$no_sk_akreditasi', '$tgl_sk_akreditasi', '$tentang', '$tahun_akreditasi', '$nama_file', '$nm_lembaga', '$provinsi_id')";
        $hasil_input = $conn->query($input2);

        if ($hasil_input) {
            echo json_encode(['success' => true, 'message' => 'SK Akreditasi berhasil disimpan']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Data gagal tersimpan: ' . $conn->error]);
        }
    }
}

$conn->close();
?>
