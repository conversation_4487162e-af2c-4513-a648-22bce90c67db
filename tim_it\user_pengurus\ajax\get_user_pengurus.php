<?php
/**
 * AJAX handler untuk DataTables Server-side Processing - Data User Pengurus
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Ambil parameter dari DataTables
    $draw = isset($_POST['draw']) ? intval($_POST['draw']) : 1;
    $start = isset($_POST['start']) ? intval($_POST['start']) : 0;
    $length = isset($_POST['length']) ? intval($_POST['length']) : 10;
    $search_value = isset($_POST['search']['value']) ? $_POST['search']['value'] : '';
    $order_column = isset($_POST['order'][0]['column']) ? intval($_POST['order'][0]['column']) : 1;
    $order_dir = isset($_POST['order'][0]['dir']) ? $_POST['order'][0]['dir'] : 'asc';
    
    // Definisi kolom untuk sorting
    $columns = [
        0 => 'u.id_user', // NO (tidak bisa di-sort)
        1 => 'u.nm_user',
        2 => 'u.sebagai',
        3 => 'u.username',
        4 => 'u.id_user' // EDIT PASSWORD (tidak bisa di-sort)
    ];
    
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];

    // Base query dengan filter
    $base_query = "FROM user u
                   WHERE u.soft_delete = 1 
                   AND u.level = 'Pengurus' 
                   AND u.provinsi_id = {$provinsi_id_session}";
    
    // Search functionality
    $search_query = "";
    $bind_params = [];
    $bind_types = "";
    
    if (!empty($search_value)) {
        $search_query = " AND (u.nm_user LIKE ? OR u.sebagai LIKE ? OR u.username LIKE ?)";
        $search_param = "%{$search_value}%";
        $bind_params = [$search_param, $search_param, $search_param];
        $bind_types = "sss";
    }
    
    // Query untuk menghitung total records
    $total_query = "SELECT COUNT(*) as total " . $base_query;
    $total_result = $conn->query($total_query);
    $total_records = $total_result->fetch_assoc()['total'];
    
    // Query untuk menghitung filtered records
    $filtered_query = "SELECT COUNT(*) as total " . $base_query . $search_query;
    if (!empty($bind_params)) {
        $filtered_stmt = $conn->prepare($filtered_query);
        if (!empty($bind_types)) {
            $filtered_stmt->bind_param($bind_types, ...$bind_params);
        }
        $filtered_stmt->execute();
        $filtered_records = $filtered_stmt->get_result()->fetch_assoc()['total'];
    } else {
        $filtered_result = $conn->query($filtered_query);
        $filtered_records = $filtered_result->fetch_assoc()['total'];
    }
    
    // Query untuk mengambil data dengan pagination dan sorting
    $data_query = "SELECT u.id_user, u.nm_user, u.sebagai, u.username
                   " . $base_query . $search_query;
    
    // Add sorting
    if (isset($columns[$order_column])) {
        $data_query .= " ORDER BY " . $columns[$order_column] . " " . $order_dir;
    }
    
    // Add pagination
    $data_query .= " LIMIT {$start}, {$length}";
    
    // Execute query
    if (!empty($bind_params)) {
        $data_stmt = $conn->prepare($data_query);
        if (!empty($bind_types)) {
            $data_stmt->bind_param($bind_types, ...$bind_params);
        }
        $data_stmt->execute();
        $data_result = $data_stmt->get_result();
    } else {
        $data_result = $conn->query($data_query);
    }
    
    // Siapkan data untuk response
    $data = [];
    while ($row = $data_result->fetch_assoc()) {
        $data[] = [
            'id_user' => $row['id_user'],
            'nm_user' => $row['nm_user'],
            'sebagai' => $row['sebagai'],
            'username' => $row['username']
        ];
    }
    
    // Response JSON untuk DataTables
    $response = [
        "draw" => $draw,
        "recordsTotal" => intval($total_records),
        "recordsFiltered" => intval($filtered_records),
        "data" => $data
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Log error
    error_log("DataTables User Pengurus Error: " . $e->getMessage());
    
    // Response error
    $error_response = [
        "draw" => isset($draw) ? $draw : 1,
        "recordsTotal" => 0,
        "recordsFiltered" => 0,
        "data" => [],
        "error" => $e->getMessage()
    ];
    
    echo json_encode($error_response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
