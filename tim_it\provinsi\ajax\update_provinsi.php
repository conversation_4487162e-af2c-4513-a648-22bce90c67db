<?php
/**
 * AJAX handler untuk update data provinsi
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    $required_fields = ['id_provinsi', 'provinsi_id', 'nama_provinsi'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            throw new Exception("Field {$field} harus diisi");
        }
    }
    
    // Sanitasi input
    $id_provinsi = intval($_POST['id_provinsi']);
    $provinsi_id = intval($_POST['provinsi_id']);
    $nama_provinsi = trim($_POST['nama_provinsi']);
    $alamat_provinsi = isset($_POST['alamat_provinsi']) ? trim($_POST['alamat_provinsi']) : '';
    $kota_id = isset($_POST['kota_id']) && !empty($_POST['kota_id']) ? trim($_POST['kota_id']) : null;
    $nama_ketua_banp = isset($_POST['nama_ketua_banp']) ? trim($_POST['nama_ketua_banp']) : '';
    $ttd_ketua_banp = isset($_POST['ttd_ketua_banp']) ? trim($_POST['ttd_ketua_banp']) : '';

    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Validasi panjang field
    if (strlen($nama_provinsi) > 50) {
        throw new Exception('Nama provinsi maksimal 50 karakter');
    }
    
    if (strlen($nama_ketua_banp) > 50) {
        throw new Exception('Nama ketua BAN PDM maksimal 50 karakter');
    }
    
    if (strlen($ttd_ketua_banp) > 30) {
        throw new Exception('TTD ketua BAN PDM maksimal 30 karakter');
    }
    
    // Cek apakah data provinsi ada dan sesuai dengan session user
    $check_query = "SELECT id_provinsi, provinsi_id, nama_provinsi FROM provinsi WHERE id_provinsi = ? AND provinsi_id = ?";
    $stmt_check = $conn->prepare($check_query);
    $stmt_check->bind_param("ii", $id_provinsi, $provinsi_id_session);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();

    if ($result_check->num_rows == 0) {
        throw new Exception('Data provinsi tidak ditemukan atau Anda tidak memiliki akses');
    }

    $existing_data = $result_check->fetch_assoc();
    
    // Validasi: provinsi_id harus sama dengan session (tidak boleh diubah)
    if ($provinsi_id != $provinsi_id_session) {
        throw new Exception('Anda tidak dapat mengubah ID Provinsi yang tidak sesuai dengan akses Anda');
    }
    
    // Begin transaction
    $conn->autocommit(false);
    
    // Query update dengan filter session
    $sql = "UPDATE provinsi SET
                nama_provinsi = ?,
                alamat_provinsi = ?,
                kota_id = ?,
                nama_ketua_banp = ?,
                ttd_ketua_banp = ?
            WHERE id_provinsi = ? AND provinsi_id = ?";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sssssii",
        $nama_provinsi, $alamat_provinsi, $kota_id,
        $nama_ketua_banp, $ttd_ketua_banp, $id_provinsi, $provinsi_id_session
    );
    
    if (!$stmt->execute()) {
        throw new Exception('Gagal mengupdate data provinsi: ' . $stmt->error);
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data provinsi "' . $nama_provinsi . '" berhasil diupdate',
        'data' => [
            'id_provinsi' => $id_provinsi,
            'provinsi_id' => $provinsi_id,
            'nama_provinsi' => $nama_provinsi,
            'alamat_provinsi' => $alamat_provinsi,
            'kota_id' => $kota_id,
            'nama_ketua_banp' => $nama_ketua_banp,
            'ttd_ketua_banp' => $ttd_ketua_banp
        ]
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Rollback transaction jika ada error
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Update Provinsi Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
