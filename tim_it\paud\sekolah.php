<?php
// Include koneksi database
require_once '../../koneksi.php';

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');

// Include header
include '../header.php';
?>

<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">DATA PAUD</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../dashboard/dashboard.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="#">Data Master</a></li>
                        <li class="breadcrumb-item active">Data Paud</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
    <!-- /.content-header -->

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            
            <!-- Alert Messages -->
            <div id="alert-container"></div>
            
            <!-- Main Card -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-school mr-2"></i>
                        Data Lembaga Satuan Pendidikan Anak Usia Dini
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-success btn-sm mr-2" id="btn-export-excel">
                            <i class="fas fa-file-excel"></i> Export Excel
                        </button>
                        <button type="button" class="btn btn-primary btn-sm" id="btn-add">
                            <i class="fas fa-plus"></i> Tambah Data
                        </button>
                    </div>
                </div>
                <!-- /.card-header -->
                
                <div class="card-body">
                    <!-- DataTable -->
                    <div class="table-responsive">
                        <table id="table-sekolah" class="table table-bordered table-striped table-hover" width="100%">
                            <thead>
                                <tr>
                                    <th width="5%">NO</th>
                                    <th width="8%">NPSN</th>
                                    <th width="25%">NAMA SEKOLAH</th>
                                    <th width="10%">JENJANG</th>
                                    <th width="15%">KAB/KOTA</th>
                                    <th width="15%">KECAMATAN</th>
                                    <th width="8%">STATUS</th>
                                    <th width="14%">AKSI</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data akan diisi via DataTables Server-side -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- /.card-body -->
            </div>
            <!-- /.card -->
            
        </div><!-- /.container-fluid -->
    </section>
    <!-- /.content -->
</div>
<!-- /.content-wrapper -->

<!-- Modal Detail Sekolah -->
<div class="modal fade" id="modal-detail" tabindex="-1" role="dialog" aria-labelledby="modal-detail-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info">
                <h5 class="modal-title" id="modal-detail-label">
                    <i class="fas fa-info-circle"></i> Detail Sekolah
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-detail-content">
                <!-- Content akan diisi via AJAX -->
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <p class="mt-2">Memuat data...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah Data Sekolah -->
<div class="modal fade" id="modal-tambah" tabindex="-1" role="dialog" aria-labelledby="modal-tambah-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success">
                <h5 class="modal-title" id="modal-tambah-label">
                    <i class="fas fa-plus-circle"></i> Tambah Data Paud
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-tambah-sekolah" novalidate>
                <div class="modal-body">
                    <div class="row">
                        <!-- Kolom Kiri -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3"><i class="fas fa-school"></i> Informasi Paud</h6>

                            <div class="form-group">
                                <label for="npsn">NPSN <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="npsn" name="npsn" required maxlength="30">
                                <small class="form-text text-muted">Nomor Pokok Sekolah Nasional</small>
                            </div>

                            <div class="form-group">
                                <label for="nama_sekolah">Nama Sekolah <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nama_sekolah" name="nama_sekolah" required maxlength="100">
                            </div>

                            <div class="form-group">
                                <label for="jenjang_id">Jenjang <span class="text-danger">*</span></label>
                                <select class="form-control" id="jenjang_id" name="jenjang_id" required>
                                    <option value="">Pilih Jenjang</option>
                                    <!-- Options akan diisi via AJAX -->
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="tipe_sekolah_id">Tipe Sekolah <span class="text-danger">*</span></label>
                                <select class="form-control" id="tipe_sekolah_id" name="tipe_sekolah_id" required>
                                    <option value="">Pilih Tipe Sekolah</option>
                                    <!-- Options akan diisi via AJAX -->
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="status_sekolah_id">Status Sekolah <span class="text-danger">*</span></label>
                                <select class="form-control" id="status_sekolah_id" name="status_sekolah_id" required>
                                    <option value="">Pilih Status Sekolah</option>
                                    <!-- Options akan diisi via AJAX -->
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="tahun_berdiri">Tahun Berdiri</label>
                                <input type="text" class="form-control" id="tahun_berdiri" name="tahun_berdiri">
                            </div>
                        </div>

                        <!-- Kolom Kanan -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3"><i class="fas fa-map-marker-alt"></i> Informasi Lokasi</h6>

                            <div class="form-group">
                                <label for="kota_id">Kabupaten/Kota <span class="text-danger">*</span></label>
                                <select class="form-control" id="kota_id" name="kota_id" required>
                                    <option value="">Pilih Kab/Kota</option>
                                    <!-- Options akan diisi via AJAX -->
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="kecamatan">Kecamatan</label>
                                <input type="text" class="form-control" id="kecamatan" name="kecamatan" maxlength="100">
                            </div>

                            <div class="form-group">
                                <label for="desa_kelurahan">Desa/Kelurahan</label>
                                <input type="text" class="form-control" id="desa_kelurahan" name="desa_kelurahan" maxlength="200">
                            </div>

                            <div class="form-group">
                                <label for="alamat">Alamat Lengkap</label>
                                <textarea class="form-control" id="alamat" name="alamat" rows="3"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="nama_yayasan">Nama Yayasan</label>
                                <input type="text" class="form-control" id="nama_yayasan" name="nama_yayasan" maxlength="200">
                                <small class="form-text text-muted">Khusus untuk sekolah swasta</small>
                            </div>

                            <div class="form-group">
                                <label for="no_akte">No. Akte</label>
                                <input type="text" class="form-control" id="no_akte" name="no_akte" maxlength="50">
                                <small class="form-text text-muted">Nomor akte pendirian yayasan</small>
                            </div>
                        </div>
                    </div>

                    <!-- Informasi Kontak -->
                    <hr>
                    <h6 class="text-primary mb-3"><i class="fas fa-address-book"></i> Informasi Kontak</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="nama_kepsek">Nama Kepala Sekolah</label>
                                <input type="text" class="form-control" id="nama_kepsek" name="nama_kepsek" maxlength="100">
                            </div>

                            <div class="form-group">
                                <label for="no_hp_kepsek">No. HP Kepala Sekolah</label>
                                <input type="text" class="form-control" id="no_hp_kepsek" name="no_hp_kepsek" maxlength="50">
                            </div>

                            <div class="form-group">
                                <label for="no_wa_kepsek">No. WhatsApp Kepala Sekolah</label>
                                <input type="text" class="form-control" id="no_wa_kepsek" name="no_wa_kepsek" maxlength="50">
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="nama_operator">Nama Operator</label>
                                <input type="text" class="form-control" id="nama_operator" name="nama_operator" maxlength="50">
                            </div>

                            <div class="form-group">
                                <label for="no_hp_operator">No. HP Operator</label>
                                <input type="text" class="form-control" id="no_hp_operator" name="no_hp_operator" maxlength="20">
                            </div>

                            <div class="form-group">
                                <label for="email">Email Sekolah</label>
                                <input type="email" class="form-control" id="email" name="email" maxlength="100">
                            </div>
                        </div>
                    </div>

                    <!-- Hidden fields -->
                    <input type="hidden" name="rumpun" value="paud">
                    <input type="hidden" name="provinsi_id" value="<?php echo $_SESSION['provinsi_id']; ?>">
                    <input type="hidden" name="status_keaktifan_id" value="1">
                    <input type="hidden" name="soft_delete" value="1">
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-success" id="btn-simpan">
                        <i class="fas fa-save"></i> Simpan Data
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


<!-- Modal Edit Data Sekolah (Konten di-generate via AJAX) -->
<div class="modal fade" id="modal-edit" tabindex="-1" role="dialog" aria-labelledby="modal-edit-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <!-- Konten akan diisi via AJAX dari get_edit_form.php -->
        </div>
    </div>
</div>

<!-- Modal Konfirmasi Hapus -->
<div class="modal fade" id="modal-konfirmasi-hapus" tabindex="-1" role="dialog" aria-labelledby="modal-konfirmasi-hapus-label" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger">
                <h5 class="modal-title text-white" id="modal-konfirmasi-hapus-label">
                    <i class="fas fa-exclamation-triangle"></i> Konfirmasi Hapus Data
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <div class="mb-3">
                    <i class="fas fa-trash-alt text-danger" style="font-size: 48px;"></i>
                </div>
                <h6 class="mb-3">Apakah Anda yakin ingin menghapus data sekolah:</h6>
                <div class="alert alert-warning">
                    <strong id="nama-sekolah-hapus"></strong>
                </div>
                <p class="text-muted mb-0">
                    <i class="fas fa-info-circle"></i>
                    Data yang dihapus dapat dipulihkan kembali oleh administrator.
                </p>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Batal
                </button>
                <button type="button" class="btn btn-danger" id="btn-konfirmasi-hapus">
                    <i class="fas fa-trash"></i> Ya, Hapus Data
                </button>
            </div>
        </div>
    </div>
</div>





<!-- Include footer -->
<?php include '../footer.php'; ?>

<!-- Page specific script -->
<script src="js/sekolah.js"></script>

<style>
/* Custom CSS untuk DataTables */
.table th {
    background-color: #007bff;
    color: white;
    text-align: center;
    vertical-align: middle;
}

.table td {
    vertical-align: middle;
}

.btn-action {
    margin: 1px;
    min-width: 32px;
}

.btn-action .fas {
    font-size: 12px;
}

.status-badge {
    font-size: 0.875em;
    padding: 0.25em 0.5em;
}

.modal-detail-row {
    margin-bottom: 10px;
    padding: 8px;
    border-bottom: 1px solid #eee;
}

.modal-detail-label {
    font-weight: bold;
    color: #495057;
    margin-bottom: 5px;
}

.modal-detail-value {
    color: #6c757d;
}

/* Loading overlay */
.dataTables_processing {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    margin-left: -100px;
    margin-top: -26px;
    text-align: center;
    padding: 1em 0;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Modal Konfirmasi Hapus */
#modal-konfirmasi-hapus .modal-body {
    padding: 2rem 1.5rem;
}

#modal-konfirmasi-hapus .fas.fa-trash-alt {
    animation: shake 0.5s ease-in-out;
}

#modal-konfirmasi-hapus .alert-warning {
    border-left: 4px solid #ffc107;
    background-color: #fff3cd;
    border-color: #ffeaa7;
    font-size: 16px;
    margin: 1rem 0;
}

#modal-konfirmasi-hapus .modal-footer {
    border-top: none;
    padding: 1rem 2rem 2rem;
}

/* Animasi shake untuk icon */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Modal Notifikasi Sukses */
#modal-notifikasi-sukses .modal-body {
    padding: 2rem 1.5rem;
}

#modal-notifikasi-sukses .fas.fa-check-circle {
    animation: bounce 0.6s ease-in-out;
}

#modal-notifikasi-sukses .modal-footer {
    border-top: none;
    padding: 1rem 2rem 2rem;
}

/* Modal Notifikasi Error */
#modal-notifikasi-error .modal-body {
    padding: 2rem 1.5rem;
}

#modal-notifikasi-error .fas.fa-exclamation-triangle {
    animation: shake 0.5s ease-in-out;
}

#modal-notifikasi-error .modal-footer {
    border-top: none;
    padding: 1rem 2rem 2rem;
}

/* Animasi bounce untuk icon sukses */
@keyframes bounce {
    0%, 20%, 60%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    80% { transform: translateY(-5px); }
}
</style>
