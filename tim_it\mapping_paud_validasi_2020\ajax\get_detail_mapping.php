<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$id_mapping = isset($_POST['id_mapping']) ? intval($_POST['id_mapping']) : 0;

if ($id_mapping <= 0) {
    echo json_encode(['success' => false, 'message' => 'ID mapping tidak valid']);
    exit;
}

try {
    // Query untuk mengambil detail mapping validasi PAUD dengan join ke tabel terkait
    $query = "SELECT 
                mp.id_mapping,
                mp.sekolah_id,
                mp.kd_asesor1,
                mp.kd_asesor2,
                mp.tahap,
                mp.tahun_akreditasi,
                mp.file_penjelasan_hasil_akreditasi,
                mp.nama_asesor_kpa,
                mp.catatan_penilaian_asesor_kpa,
                mp.nama_asesor_Validasi_a,
                mp.catatan_penilaian_asesor_Validasi_a,
                mp.nama_asesor_Validasi_b,
                mp.catatan_penilaian_asesor_Validasi_b,
                mp.nama_validator,
                mp.nilai_validasi,
                mp.catatan_penilaian_validator,
                mp.pha,
                mp.provinsi_id,
                
                -- Data Sekolah
                s.nama_sekolah,
                s.npsn,
                s.jenjang_id,
                s.rumpun,
                s.alamat,
                s.kota_id,
                s.desa_kelurahan,
                s.kecamatan,
                s.nama_kepsek,
                s.no_hp_kepsek,
                s.no_wa_kepsek,
                s.nama_operator,
                s.no_hp_operator,
                s.no_wa_operator,
                s.email,
                
                -- Data Jenjang
                j.nm_jenjang,
                
                -- Data Kab/Kota Sekolah
                k.nm_kota,
                
                -- Data Asesor 1 (Validator)
                a1.nia1,
                a1.nm_asesor1,
                a1.no_hp as no_hp_asesor1,
                a1.kota_id1,
                
                -- Data Asesor 2 (Verifikator)
                a2.nia2,
                a2.nm_asesor2,
                a2.no_hp as no_hp_asesor2,
                a2.kota_id2,
                
                -- Data Kab/Kota Asesor 1 (Validator)
                k1.nm_kota as nm_kota_asesor1,
                
                -- Data Kab/Kota Asesor 2 (Verifikator)
                k2.nm_kota as nm_kota_asesor2
                
              FROM mapping_paud_validasi mp
              LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
              LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
              LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
              LEFT JOIN asesor_1 a1 ON mp.kd_asesor1 = a1.kd_asesor1
              LEFT JOIN asesor_2 a2 ON mp.kd_asesor2 = a2.kd_asesor2
              LEFT JOIN kab_kota k1 ON a1.kota_id1 = k1.kota_id
              LEFT JOIN kab_kota k2 ON a2.kota_id2 = k2.kota_id
              WHERE mp.id_mapping = ? AND mp.provinsi_id = ?";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ii", $id_mapping, $_SESSION['provinsi_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Data mapping validasi tidak ditemukan']);
        exit;
    }
    
    $data = $result->fetch_assoc();
    
    // Log successful access
    error_log("Detail Mapping Validasi PAUD accessed - ID: $id_mapping, User: " . $_SESSION['nm_user']);
    
    echo json_encode([
        'success' => true,
        'data' => $data
    ]);
    
} catch (Exception $e) {
    error_log("Get Detail Mapping Validasi Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan saat memuat detail mapping validasi'
    ]);
}

$conn->close();
?>
