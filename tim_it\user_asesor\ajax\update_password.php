<?php
/**
 * AJAX handler untuk mengupdate password user asesor
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    $required_fields = ['id_user', 'username', 'new_password'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            throw new Exception("Field {$field} harus diisi");
        }
    }
    
    // Sanitasi input
    $id_user = intval($_POST['id_user']);
    $username = trim($_POST['username']);
    $new_password = trim($_POST['new_password']);
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Cek apakah user ada dan sesuai dengan provinsi session
    $check_user_sql = "SELECT COUNT(*) as count, nm_user FROM user 
                       WHERE id_user = ? 
                       AND username = ? 
                       AND level = 'Asesor' 
                       AND provinsi_id = ? 
                       AND soft_delete = 1";
    $check_user_stmt = $conn->prepare($check_user_sql);
    $check_user_stmt->bind_param("isi", $id_user, $username, $provinsi_id_session);
    $check_user_stmt->execute();
    $check_result = $check_user_stmt->get_result();
    $user_data = $check_result->fetch_assoc();
    
    if ($user_data['count'] == 0) {
        throw new Exception('Asesor tidak ditemukan atau tidak memiliki akses');
    }
    
    $nm_user = $user_data['nm_user'];
    
    // Hash password (menggunakan MD5 sesuai dengan sistem yang ada)
    // Note: Dalam production sebaiknya gunakan password_hash() dengan bcrypt
    $hashed_password = md5($new_password);
    
    // Begin transaction
    $conn->autocommit(false);
    
    // Query update password
    $sql = "UPDATE user SET password = ? 
            WHERE id_user = ? 
            AND username = ? 
            AND level = 'Asesor' 
            AND provinsi_id = ? 
            AND soft_delete = 1";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sisi", $hashed_password, $id_user, $username, $provinsi_id_session);
    
    if (!$stmt->execute()) {
        throw new Exception('Gagal mengupdate password: ' . $stmt->error);
    }
    
    // Cek apakah ada baris yang ter-update
    if ($stmt->affected_rows == 0) {
        throw new Exception('Tidak ada perubahan atau asesor tidak ditemukan');
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Password asesor "' . $nm_user . '" berhasil diupdate',
        'data' => [
            'id_user' => $id_user,
            'username' => $username,
            'nm_user' => $nm_user
        ]
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Rollback transaction jika ada error
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Update Password Asesor Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
