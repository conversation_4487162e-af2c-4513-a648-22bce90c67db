<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi parameter
    if (!isset($_POST['filename']) || empty(trim($_POST['filename']))) {
        throw new Exception('Nama file harus diisi');
    }
    
    $filename = trim($_POST['filename']);
    
    // Sanitasi filename untuk security
    $filename = basename($filename); // Remove directory traversal
    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename); // Remove special chars
    
    if (empty($filename)) {
        throw new Exception('Nama file tidak valid');
    }
    
    // Construct file path
    $file_directory = '../../../simak/files/upload_file_hasil_kpa/';
    $file_path = $file_directory . $filename;
    
    // Debug log
    error_log("Check File KPA - Filename: $filename, Path: $file_path");
    
    // Check if file exists and is readable
    $file_exists = false;
    $file_size = 0;
    $file_type = '';
    
    if (file_exists($file_path) && is_readable($file_path)) {
        $file_exists = true;
        $file_size = filesize($file_path);
        $file_type = mime_content_type($file_path);
        
        // Additional check for PDF files
        $is_pdf = (strtolower(pathinfo($filename, PATHINFO_EXTENSION)) === 'pdf');
        
        echo json_encode([
            'success' => true,
            'file_exists' => $file_exists,
            'filename' => $filename,
            'file_size' => $file_size,
            'file_type' => $file_type,
            'is_pdf' => $is_pdf,
            'file_path' => $file_path
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'file_exists' => false,
            'filename' => $filename,
            'message' => 'File tidak ditemukan atau tidak dapat diakses'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Check File KPA Error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'file_exists' => false,
        'message' => $e->getMessage()
    ]);
}
?>
