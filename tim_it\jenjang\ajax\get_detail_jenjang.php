<?php
/**
 * AJAX handler untuk mengambil detail jenjang
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi input
    if (!isset($_POST['id_jenjang']) || empty($_POST['id_jenjang'])) {
        throw new Exception('ID Jenjang tidak valid');
    }
    
    $id_jenjang = intval($_POST['id_jenjang']);
    
    // Query untuk mengambil detail jenjang
    $query = "SELECT * FROM jenjang WHERE id_jenjang = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $id_jenjang);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        throw new Exception('Data jenjang tidak ditemukan');
    }
    
    $data = $result->fetch_assoc();
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Detail jenjang berhasil dimuat',
        'data' => [
            'id_jenjang' => $data['id_jenjang'],
            'jenjang_id' => $data['jenjang_id'],
            'nm_jenjang' => $data['nm_jenjang']
        ]
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Log error
    error_log("Get Detail Jenjang Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
