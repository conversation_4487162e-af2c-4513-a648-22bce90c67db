<?php
session_start();
require_once '../../../koneksi.php';

// Cek session dan level
if (!isset($_SESSION['kd_user']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

// Ambil provinsi_id dari session
$provinsi_id = $_SESSION['provinsi_id'];

try {
    // Cek koneksi database
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // DataTables parameters
    $draw = intval($_POST['draw']);
    $start = intval($_POST['start']);
    $length = intval($_POST['length']);
    $search_value = $_POST['search']['value'];
    $order_column = $_POST['order'][0]['column'];
    $order_dir = $_POST['order'][0]['dir'];

    // Column mapping for ordering
    $columns = [
        0 => 'sertifikat_id', // NO (not orderable)
        1 => 's.npsn',
        2 => 'sk.nama_sekolah', 
        3 => 'j.nm_jenjang',
        4 => 'k.nm_kota',
        5 => 's.tahun_akreditasi',
        6 => 'sertifikat_id' // AKSI (not orderable)
    ];

    // Base query
    $base_query = "FROM sertifikat s
                   LEFT JOIN sekolah sk ON s.sekolah_id = sk.sekolah_id
                   LEFT JOIN jenjang j ON sk.jenjang_id = j.jenjang_id  
                   LEFT JOIN kab_kota k ON sk.kota_id = k.kota_id
                   WHERE s.provinsi_id = $provinsi_id
                     AND sk.rumpun = 'dasmen'
                     AND sk.soft_delete = '1'";

    // Add search condition
    $search_query = "";
    if (!empty($search_value)) {
        $search_value = $conn->real_escape_string($search_value);
        $search_query = " AND (sk.npsn LIKE '%$search_value%' 
                             OR sk.nama_sekolah LIKE '%$search_value%'
                             OR j.nm_jenjang LIKE '%$search_value%'
                             OR k.nm_kota LIKE '%$search_value%'
                             OR s.tahun_akreditasi LIKE '%$search_value%')";
    }

    // Count total records
    $total_query = "SELECT COUNT(*) as total " . $base_query;
    $total_result = $conn->query($total_query);
    $total_records = $total_result->fetch_assoc()['total'];

    // Count filtered records
    $filtered_query = "SELECT COUNT(*) as total " . $base_query . $search_query;
    $filtered_result = $conn->query($filtered_query);
    $filtered_records = $filtered_result->fetch_assoc()['total'];

    // Get data with pagination
    $order_by = "";
    if (isset($columns[$order_column])) {
        $order_by = "ORDER BY " . $columns[$order_column] . " " . $order_dir;
    } else {
        $order_by = "ORDER BY s.tahun_akreditasi DESC";
    }

    $data_query = "SELECT s.sertifikat_id, s.nama_file, s.tahun_akreditasi,
                          sk.npsn, sk.nama_sekolah, j.nm_jenjang, k.nm_kota
                   " . $base_query . $search_query . " 
                   " . $order_by . " 
                   LIMIT $start, $length";

    $data_result = $conn->query($data_query);

    $data = [];
    if ($data_result && $data_result->num_rows > 0) {
        while ($row = $data_result->fetch_assoc()) {
            $data[] = [
                'sertifikat_id' => $row['sertifikat_id'],
                'npsn' => $row['npsn'] ?: '-',
                'nama_sekolah' => $row['nama_sekolah'] ?: '-',
                'nm_jenjang' => $row['nm_jenjang'] ?: '-',
                'nm_kota' => $row['nm_kota'] ?: '-',
                'tahun_akreditasi' => $row['tahun_akreditasi'] ?: '-',
                'nama_file' => $row['nama_file'] ?: ''
            ];
        }
    }

    // Return DataTables response
    echo json_encode([
        'draw' => $draw,
        'recordsTotal' => intval($total_records),
        'recordsFiltered' => intval($filtered_records),
        'data' => $data
    ]);

} catch (Exception $e) {
    error_log("Get Sertifikat Error: " . $e->getMessage());
    echo json_encode([
        'draw' => isset($draw) ? $draw : 0,
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
