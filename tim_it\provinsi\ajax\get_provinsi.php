<?php
/**
 * AJAX handler untuk DataTables server-side processing Data Provinsi
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }

    // Ambil parameter DataTables
    $draw = isset($_POST['draw']) ? intval($_POST['draw']) : 1;
    $start = isset($_POST['start']) ? intval($_POST['start']) : 0;
    $length = isset($_POST['length']) ? intval($_POST['length']) : 10;
    $search_value = isset($_POST['search']['value']) ? trim($_POST['search']['value']) : '';
    $order_column = isset($_POST['order'][0]['column']) ? intval($_POST['order'][0]['column']) : 0;
    $order_dir = isset($_POST['order'][0]['dir']) ? $_POST['order'][0]['dir'] : 'asc';

    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];

    // Mapping kolom untuk ordering
    $columns = [
        0 => 'p.nama_provinsi',
        1 => 'p.alamat_provinsi',
        2 => 'k.nm_kota',
        3 => 'p.nama_ketua_banp'
    ];

    $order_by = isset($columns[$order_column]) ? $columns[$order_column] : 'p.nama_provinsi';

    // Base query dengan JOIN dan filter provinsi session
    $base_query = "FROM provinsi p
                   LEFT JOIN kab_kota k ON p.kota_id = k.kota_id
                   WHERE p.provinsi_id = {$provinsi_id_session}";

    // WHERE clause untuk search (tambahan dari filter provinsi)
    $where_clause = "";
    $search_params = [];
    $param_types = "";

    if (!empty($search_value)) {
        $where_clause = " AND (p.nama_provinsi LIKE ? OR
                               p.alamat_provinsi LIKE ? OR
                               k.nm_kota LIKE ? OR
                               p.nama_ketua_banp LIKE ?)";
        $search_like = "%{$search_value}%";
        $search_params = [$search_like, $search_like, $search_like, $search_like];
        $param_types = "ssss";
    }

    // Query untuk total records
    $total_query = "SELECT COUNT(*) as total " . $base_query;
    $stmt_total = $conn->prepare($total_query);
    $stmt_total->execute();
    $total_records = $stmt_total->get_result()->fetch_assoc()['total'];

    // Query untuk filtered records
    $filtered_query = "SELECT COUNT(*) as total " . $base_query . $where_clause;
    if (!empty($search_params)) {
        $stmt_filtered = $conn->prepare($filtered_query);
        $stmt_filtered->bind_param($param_types, ...$search_params);
    } else {
        $stmt_filtered = $conn->prepare($filtered_query);
    }
    $stmt_filtered->execute();
    $filtered_records = $stmt_filtered->get_result()->fetch_assoc()['total'];

    // Query untuk data dengan pagination
    $data_query = "SELECT p.id_provinsi, p.provinsi_id, p.nama_provinsi, p.alamat_provinsi,
                          p.kota_id, p.nama_ketua_banp, p.ttd_ketua_banp, p.kd_user,
                          k.nm_kota
                   " . $base_query . $where_clause . "
                   ORDER BY {$order_by} {$order_dir}
                   LIMIT ?, ?";

    // Prepare statement untuk data
    if (!empty($search_params)) {
        $all_params = array_merge($search_params, [$start, $length]);
        $all_param_types = $param_types . "ii";
        $stmt_data = $conn->prepare($data_query);
        $stmt_data->bind_param($all_param_types, ...$all_params);
    } else {
        $stmt_data = $conn->prepare($data_query);
        $stmt_data->bind_param("ii", $start, $length);
    }

    $stmt_data->execute();
    $result = $stmt_data->get_result();

    // Siapkan data untuk response
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = [
            'id_provinsi' => $row['id_provinsi'],
            'provinsi_id' => $row['provinsi_id'],
            'nama_provinsi' => $row['nama_provinsi'],
            'alamat_provinsi' => $row['alamat_provinsi'],
            'kota_id' => $row['kota_id'],
            'nm_kota' => $row['nm_kota'],
            'nama_ketua_banp' => $row['nama_ketua_banp'],
            'ttd_ketua_banp' => $row['ttd_ketua_banp'],
            'kd_user' => $row['kd_user']
        ];
    }

    // Response DataTables
    $response = [
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $filtered_records,
        'data' => $data
    ];

    echo json_encode($response);

} catch (Exception $e) {
    // Log error
    error_log("Get Provinsi Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'draw' => isset($draw) ? $draw : 1,
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => $e->getMessage()
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
