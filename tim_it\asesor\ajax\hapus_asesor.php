<?php
/**
 * AJAX handler untuk menghapus data asesor (soft delete)
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    $required_fields = ['id_asesor', 'nm_asesor', 'nia'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            throw new Exception("Field {$field} harus diisi");
        }
    }
    
    // Sanitasi input
    $id_asesor = intval($_POST['id_asesor']);
    $nm_asesor = trim($_POST['nm_asesor']);
    $nia = trim($_POST['nia']);
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Cek apakah asesor ada dan sesuai dengan provinsi session
    $check_asesor_sql = "SELECT COUNT(*) as count, nm_asesor, nia FROM asesor
                         WHERE id_asesor = $id_asesor AND provinsi_id = $provinsi_id_session AND soft_delete = '1'";
    $check_result = $conn->query($check_asesor_sql);
    if (!$check_result) {
        throw new Exception('Error checking asesor: ' . $conn->error);
    }
    $asesor_data = $check_result->fetch_assoc();
    
    if ($asesor_data['count'] == 0) {
        throw new Exception('Data asesor tidak ditemukan atau tidak memiliki akses');
    }

    // Ambil data asesor untuk response dan kd_asesor untuk user
    $nm_asesor_db = $asesor_data['nm_asesor'];
    $nia_db = $asesor_data['nia'];

    // Ambil kd_asesor untuk soft delete user
    $get_kd_asesor_sql = "SELECT kd_asesor FROM asesor WHERE id_asesor = $id_asesor";
    $kd_asesor_result = $conn->query($get_kd_asesor_sql);
    if (!$kd_asesor_result) {
        throw new Exception('Error getting kd_asesor: ' . $conn->error);
    }
    $kd_asesor_data = $kd_asesor_result->fetch_assoc();
    $kd_asesor = $kd_asesor_data['kd_asesor'];
    
    // Begin transaction
    $conn->autocommit(false);
    
    // Soft delete: Update field soft_delete menjadi '0'
    $sebab_hapus = "Data dihapus pada " . date('Y-m-d H:i:s') . " oleh " . $_SESSION['username'];
    $sebab_hapus_escaped = $conn->real_escape_string($sebab_hapus);

    $sql = "UPDATE asesor SET
                soft_delete = '0',
                sebab = CONCAT(IFNULL(sebab, ''), IF(sebab IS NULL OR sebab = '', '', ' | '), '$sebab_hapus_escaped')
            WHERE id_asesor = $id_asesor AND provinsi_id = $provinsi_id_session AND soft_delete = '1'";

    // Debug: Log query
    error_log("DELETE Query: " . $sql);

    // Execute query untuk tabel asesor
    $result = $conn->query($sql);

    if (!$result) {
        throw new Exception('Gagal menghapus data asesor: ' . $conn->error);
    }

    // Debug: Log affected rows
    error_log("DELETE Success: Affected rows = " . $conn->affected_rows);

    // Sinkronisasi soft delete ke tabel asesor_1
    $sql_asesor1 = "UPDATE asesor_1 SET
                        soft_delete = '0',
                        sebab = CONCAT(IFNULL(sebab, ''), IF(sebab IS NULL OR sebab = '', '', ' | '), '$sebab_hapus_escaped')
                    WHERE id_asesor1 = $id_asesor AND provinsi_id = $provinsi_id_session AND soft_delete = '1'";

    $result_asesor1 = $conn->query($sql_asesor1);
    if (!$result_asesor1) {
        throw new Exception('Gagal sinkronisasi soft delete ke tabel asesor_1: ' . $conn->error);
    }

    // Sinkronisasi soft delete ke tabel asesor_2
    $sql_asesor2 = "UPDATE asesor_2 SET
                        soft_delete = '0',
                        sebab = CONCAT(IFNULL(sebab, ''), IF(sebab IS NULL OR sebab = '', '', ' | '), '$sebab_hapus_escaped')
                    WHERE id_asesor2 = $id_asesor AND provinsi_id = $provinsi_id_session AND soft_delete = '1'";

    $result_asesor2 = $conn->query($sql_asesor2);
    if (!$result_asesor2) {
        throw new Exception('Gagal sinkronisasi soft delete ke tabel asesor_2: ' . $conn->error);
    }

    // Sinkronisasi soft delete ke tabel user
    $sql_user = "UPDATE user SET
                     soft_delete = '0'
                 WHERE kd_user = '$kd_asesor' AND soft_delete = '1'";

    $result_user = $conn->query($sql_user);
    if (!$result_user) {
        throw new Exception('Gagal sinkronisasi soft delete ke tabel user: ' . $conn->error);
    }

    // Commit transaction jika semua berhasil
    $conn->commit();
    $conn->autocommit(true);
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data asesor "' . $nm_asesor_db . '" (NIA: ' . $nia_db . ') berhasil dihapus',
        'data' => [
            'id_asesor' => $id_asesor,
            'nm_asesor' => $nm_asesor_db,
            'nia' => $nia_db
        ]
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Rollback transaction jika ada error
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Hapus Asesor Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
