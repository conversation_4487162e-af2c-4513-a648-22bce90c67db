$(document).ready(function() {
    // Initialize DataTable
    initDataTable();
    
    // Event handlers
    initEventHandlers();
});

function initDataTable() {
    $('#table-sk').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: 'ajax/get_sk.php',
            type: 'POST'
        },
        columns: [
            { 
                data: null,
                orderable: false,
                searchable: false,
                render: function(data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { 
                data: 'tgl_sk_akreditasi',
                render: function(data, type, row) {
                    if (data) {
                        var date = new Date(data);
                        return date.toLocaleDateString('id-ID');
                    }
                    return '-';
                }
            },
            { data: 'no_sk_akreditasi' },
            {
                data: 'tentang',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { 
                data: 'tahun_akreditasi',
                className: 'text-center'
            },
            { 
                data: null,
                orderable: false,
                searchable: false,
                className: 'text-center',
                render: function(data, type, row) {
                    return `
                        <button type="button" class="btn btn-info btn-sm btn-action" 
                                onclick="previewSK('${row.nama_file}')" 
                                title="Preview SK">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-warning btn-sm btn-action" 
                                onclick="editSK(${row.sk_akreditasi_id})" 
                                title="Edit SK">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-danger btn-sm btn-action" 
                                onclick="deleteSK(${row.sk_akreditasi_id}, '${row.nama_file}')" 
                                title="Hapus SK">
                            <i class="fas fa-trash"></i>
                        </button>
                    `;
                }
            }
        ],
        order: [[1, 'desc']], // Order by tanggal SK descending
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: "Memuat data...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(disaring dari _MAX_ total data)",
            paginate: {
                first: "Pertama",
                last: "Terakhir",
                next: "Selanjutnya",
                previous: "Sebelumnya"
            },
            emptyTable: "Tidak ada data SK akreditasi"
        },
        responsive: true,
        autoWidth: false
    });
}

function initEventHandlers() {
    // Form submit handlers
    $('#form-tambah-sk').on('submit', function(e) {
        e.preventDefault();
        uploadSK();
    });

    $('#form-edit-sk').on('submit', function(e) {
        e.preventDefault();
        updateSK();
    });

    // Modal reset handlers
    $('#modal-tambah-sk').on('hidden.bs.modal', function() {
        resetForm('form-tambah-sk');
    });

    $('#modal-edit-sk').on('hidden.bs.modal', function() {
        resetForm('form-edit-sk');
    });

    // File input change handlers
    $('#nama_file, #edit_nama_file').on('change', function() {
        validateFile(this);
    });
}

function uploadSK() {
    console.log('Form tambah SK being submitted');
    
    var formData = new FormData($('#form-tambah-sk')[0]);
    
    // Debug: Log FormData contents
    console.log('FormData contents:');
    for (var pair of formData.entries()) {
        console.log(pair[0] + ': ' + pair[1]);
    }
    
    // Disable submit button
    var submitBtn = $('#form-tambah-sk button[type="submit"]');
    var originalText = submitBtn.html();
    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
    
    $.ajax({
        url: 'ajax/upload_sk.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function(response) {
            console.log('Upload response:', response);
            if (response.success) {
                showAlert('success', response.message);
                $('#modal-tambah-sk').modal('hide');
                $('#table-sk').DataTable().ajax.reload();
            } else {
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('Upload error:', xhr.responseText);
            showAlert('error', 'Terjadi kesalahan saat menyimpan SK: ' + error);
        },
        complete: function() {
            // Re-enable submit button
            submitBtn.prop('disabled', false).html(originalText);
        }
    });
}

function editSK(skId) {
    $.ajax({
        url: 'ajax/get_sk_detail.php',
        type: 'POST',
        data: { sk_akreditasi_id: skId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var data = response.data;
                $('#edit_sk_akreditasi_id').val(data.sk_akreditasi_id);
                $('#edit_tgl_sk_akreditasi').val(data.tgl_sk_akreditasi);
                $('#edit_no_sk_akreditasi').val(data.no_sk_akreditasi);
                $('#edit_tentang').val(data.tentang);
                $('#edit_tahun_akreditasi').val(data.tahun_akreditasi);
                $('#edit_nm_lembaga').val(data.nm_lembaga);
                
                $('#modal-edit-sk').modal('show');
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Terjadi kesalahan saat memuat data SK');
        }
    });
}

function updateSK() {
    console.log('Form edit SK being submitted');
    
    var formData = new FormData($('#form-edit-sk')[0]);
    
    // Disable submit button
    var submitBtn = $('#form-edit-sk button[type="submit"]');
    var originalText = submitBtn.html();
    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Mengupdate...');
    
    $.ajax({
        url: 'ajax/update_sk.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function(response) {
            console.log('Update response:', response);
            if (response.success) {
                showAlert('success', response.message);
                $('#modal-edit-sk').modal('hide');
                $('#table-sk').DataTable().ajax.reload();
            } else {
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('Update error:', xhr.responseText);
            showAlert('error', 'Terjadi kesalahan saat mengupdate SK: ' + error);
        },
        complete: function() {
            // Re-enable submit button
            submitBtn.prop('disabled', false).html(originalText);
        }
    });
}

function previewSK(namaFile) {
    if (!namaFile) {
        showAlert('error', 'File tidak ditemukan');
        return;
    }
    
    // Open PDF in new tab
    var fileUrl = '../../../simak/files/sk_akreditasi/' + namaFile;
    window.open(fileUrl, '_blank');
}

function deleteSK(skId, namaFile) {
    if (!confirm('Apakah Anda yakin ingin menghapus SK ini?\n\nFile: ' + namaFile + '\n\nData yang dihapus tidak dapat dikembalikan!')) {
        return;
    }
    
    $.ajax({
        url: 'ajax/delete_sk.php',
        type: 'POST',
        data: {
            sk_akreditasi_id: skId,
            nama_file: namaFile
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showAlert('success', response.message);
                $('#table-sk').DataTable().ajax.reload();
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Terjadi kesalahan saat menghapus SK');
        }
    });
}

function validateFile(input) {
    var file = input.files[0];
    if (file) {
        // Check file type
        if (file.type !== 'application/pdf') {
            showAlert('error', 'Hanya file PDF yang diizinkan');
            input.value = '';
            return false;
        }
        
        // Check file size (10MB)
        if (file.size > 10 * 1024 * 1024) {
            showAlert('error', 'Ukuran file maksimal 10MB');
            input.value = '';
            return false;
        }
    }
    return true;
}

function resetForm(formId) {
    $('#' + formId)[0].reset();
}

function showAlert(type, message) {
    var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    var iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';
    
    var alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${iconClass}"></i> ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `;
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert at the top of content
    $('.content-wrapper .content').prepend(alertHtml);
    
    // Auto hide after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
