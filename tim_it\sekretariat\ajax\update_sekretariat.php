<?php
/**
 * AJAX handler untuk update data sekretariat dengan sinkronisasi ke tabel user
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    $required_fields = ['id_sekretariat', 'nm_staff', 'kota_id', 'jk', 'jabatan'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            throw new Exception("Field {$field} harus diisi");
        }
    }

    // Validasi khusus untuk status_keaktifan_id (boleh "0")
    if (!isset($_POST['status_keaktifan_id']) || $_POST['status_keaktifan_id'] === '') {
        throw new Exception("Field status_keaktifan_id harus diisi");
    }
    
    // Sanitasi input
    $id_sekretariat = intval($_POST['id_sekretariat']);
    $kd_sekretariat = trim($_POST['kd_sekretariat']); // Hidden field, tidak bisa diubah user
    $nm_staff = trim($_POST['nm_staff']);
    $ktp = isset($_POST['ktp']) ? trim($_POST['ktp']) : '';
    $unit_kerja = isset($_POST['unit_kerja']) ? trim($_POST['unit_kerja']) : '';
    $kota_id = trim($_POST['kota_id']);
    $no_hp = isset($_POST['no_hp']) ? trim($_POST['no_hp']) : '';
    $no_wa = isset($_POST['no_wa']) ? trim($_POST['no_wa']) : '';
    $tempat_lahir = isset($_POST['tempat_lahir']) ? trim($_POST['tempat_lahir']) : '';
    $tgl_lahir = isset($_POST['tgl_lahir']) && !empty($_POST['tgl_lahir']) ? $_POST['tgl_lahir'] : '0000-00-00';
    $jabatan = isset($_POST['jabatan']) ? trim($_POST['jabatan']) : '';
    $pendidikan = isset($_POST['pendidikan']) ? trim($_POST['pendidikan']) : '';
    $jk = trim($_POST['jk']);
    $alamat_kantor = isset($_POST['alamat_kantor']) ? trim($_POST['alamat_kantor']) : '';
    $alamat_rumah = isset($_POST['alamat_rumah']) ? trim($_POST['alamat_rumah']) : '';
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $status_keaktifan_id = trim($_POST['status_keaktifan_id']);
    $sebab = isset($_POST['sebab']) ? trim($_POST['sebab']) : '';
    $no_urut = isset($_POST['no_urut']) && !empty($_POST['no_urut']) ? intval($_POST['no_urut']) : 0;
    
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Validasi jenis kelamin
    $valid_jk = ['Pria', 'Wanita'];
    if (!in_array($jk, $valid_jk)) {
        throw new Exception('Jenis kelamin harus Pria atau Wanita');
    }
    
    // Validasi status keaktifan
    $valid_status = ['0', '1', '2'];
    if (!in_array($status_keaktifan_id, $valid_status)) {
        throw new Exception('Status keaktifan tidak valid');
    }

    // Validasi jabatan
    $valid_jabatan = ['Staff IT', 'Tim PADA', 'Staff Keuangan', 'Staff Sekretariat Umum', 'Asesor'];
    if (!in_array($jabatan, $valid_jabatan)) {
        throw new Exception('Jabatan tidak valid');
    }
    
    // Cek apakah data sekretariat ada dan sesuai dengan session user
    $check_query = "SELECT id_sekretariat, kd_sekretariat, nm_staff FROM sekretariat
                    WHERE id_sekretariat = $id_sekretariat AND provinsi_id = $provinsi_id_session AND soft_delete = '1'";
    $result_check = $conn->query($check_query);

    if ($result_check->num_rows == 0) {
        throw new Exception('Data sekretariat tidak ditemukan atau Anda tidak memiliki akses');
    }

    $existing_data = $result_check->fetch_assoc();

    // Validasi kota_id ada dan sesuai provinsi session
    $kota_id_check = $conn->real_escape_string($kota_id);
    $check_kota = "SELECT kota_id FROM kab_kota WHERE kota_id = '$kota_id_check' AND provinsi_id = $provinsi_id_session";
    $result_check_kota = $conn->query($check_kota);

    if ($result_check_kota->num_rows == 0) {
        throw new Exception('Kabupaten/Kota tidak valid atau tidak sesuai dengan provinsi Anda');
    }
    
    // Escape semua data untuk keamanan
    $nm_staff = $conn->real_escape_string($nm_staff);
    $ktp = $conn->real_escape_string($ktp);
    $unit_kerja = $conn->real_escape_string($unit_kerja);
    $kota_id = $conn->real_escape_string($kota_id);
    $no_hp = $conn->real_escape_string($no_hp);
    $no_wa = $conn->real_escape_string($no_wa);
    $tempat_lahir = $conn->real_escape_string($tempat_lahir);
    $tgl_lahir = $conn->real_escape_string($tgl_lahir);
    $jabatan = $conn->real_escape_string($jabatan);
    $pendidikan = $conn->real_escape_string($pendidikan);
    $jk = $conn->real_escape_string($jk);
    $alamat_kantor = $conn->real_escape_string($alamat_kantor);
    $alamat_rumah = $conn->real_escape_string($alamat_rumah);
    $email = $conn->real_escape_string($email);
    $status_keaktifan_id = $conn->real_escape_string($status_keaktifan_id);
    $sebab = $conn->real_escape_string($sebab);
    $kd_sekretariat = $conn->real_escape_string($kd_sekretariat);

    // Begin transaction
    $conn->autocommit(false);

    // 1. Update tabel sekretariat
    $sql_sekretariat = "UPDATE sekretariat SET
                nm_staff = '$nm_staff', ktp = '$ktp', unit_kerja = '$unit_kerja', kota_id = '$kota_id',
                no_hp = '$no_hp', no_wa = '$no_wa', tempat_lahir = '$tempat_lahir', tgl_lahir = '$tgl_lahir', jabatan = '$jabatan',
                pendidikan = '$pendidikan', jk = '$jk', alamat_kantor = '$alamat_kantor',
                alamat_rumah = '$alamat_rumah', email = '$email', status_keaktifan_id = '$status_keaktifan_id', sebab = '$sebab', no_urut = $no_urut
            WHERE id_sekretariat = $id_sekretariat AND provinsi_id = $provinsi_id_session";

    if (!$conn->query($sql_sekretariat)) {
        throw new Exception('Gagal mengupdate data sekretariat: ' . $conn->error);
    }
    
    // 2. Update tabel user dengan mapping field (kecuali username dan password)
    $sql_user = "UPDATE user SET
                nm_user = '$nm_staff',
                status_keaktifan_id = '$status_keaktifan_id',
                kota_id = '$kota_id',
                level = '$jabatan',
                sebagai = '$jabatan'
            WHERE kd_user = '$kd_sekretariat'";

    if (!$conn->query($sql_user)) {
        throw new Exception('Gagal mengupdate data user: ' . $conn->error);
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data sekretariat "' . $nm_staff . '" berhasil diupdate',
        'data' => [
            'id_sekretariat' => $id_sekretariat,
            'kd_sekretariat' => $kd_sekretariat,
            'nm_staff' => $nm_staff
        ]
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Rollback transaction jika ada error
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Update Sekretariat Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
