<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');
?>

<!-- Include header -->
<?php include '../header.php'; ?>

<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

<!-- Content Wrapper -->
<div class="content-wrapper">
    <!-- Content Header -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1><i class="fas fa-certificate"></i> Sertifikat Akreditasi Paud dan Ke<PERSON>araan</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item active">Sertifikat Paud dan Kesetaraan</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-list"></i> Data Sertifikat Akreditasi Paud dan Kesetaraan
                            </h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#modal-tambah-sertifikat">
                                    <i class="fas fa-plus"></i> Tambah Sertifikat
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="table-sertifikat" class="table table-bordered table-striped table-hover" width="100%">
                                    <thead>
                                        <tr>
                                            <th width="5%">NO</th>
                                            <th width="15%">NPSN</th>
                                            <th width="25%">NAMA SEKOLAH</th>
                                            <th width="15%">JENJANG</th>
                                            <th width="20%">KAB/KOTA</th>
                                            <th width="10%">TAHUN AKREDITASI</th>
                                            <th width="10%">AKSI</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data akan dimuat via AJAX -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Modal Tambah Sertifikat -->
<div class="modal fade" id="modal-tambah-sertifikat" tabindex="-1" role="dialog" aria-labelledby="modal-tambah-sertifikat-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="modal-tambah-sertifikat-label">
                    <i class="fas fa-upload"></i> Tambah Sertifikat Akreditasi
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-tambah-sertifikat" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="npsn">NPSN Sekolah <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="npsn" name="npsn" 
                               placeholder="Masukkan NPSN Sekolah" required maxlength="30">
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle"></i> Masukkan NPSN sekolah yang akan diupload sertifikatnya.
                        </small>
                    </div>
                    
                    <div class="form-group">
                        <label for="tahun_akreditasi">Tahun Akreditasi <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="tahun_akreditasi" name="tahun_akreditasi" 
                               min="2000" max="2030" required placeholder="Contoh: 2024">
                    </div>
                    
                    <div class="form-group">
                        <label for="nama_file">File Sertifikat (PDF) <span class="text-danger">*</span></label>
                        <input type="file" class="form-control-file" id="nama_file" name="nama_file" 
                               accept=".pdf" required>
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle"></i> Hanya file PDF yang diizinkan. Maksimal ukuran 10MB.
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Upload Sertifikat
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Include footer -->
<?php include '../footer.php'; ?>

<!-- DataTables & plugins -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap4.min.js"></script>

<!-- Custom JS -->
<script src="js/sertifikat.js"></script>

<style>
.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
}

.table td {
    vertical-align: middle;
}

.btn-action {
    margin: 0 2px;
}

.modal-header {
    border-bottom: none;
}

.modal-footer {
    border-top: none;
}

#nama_file {
    border: 2px dashed #ddd;
    padding: 10px;
    border-radius: 5px;
    background-color: #f9f9f9;
}

#nama_file:hover {
    border-color: #007bff;
    background-color: #f0f8ff;
}
</style>
