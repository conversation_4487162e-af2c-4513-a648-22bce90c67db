{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#composer-lock-the-lock-file", "This file is @generated automatically"], "hash": "bed930f26e019e0a48bd041d15c90e7d", "content-hash": "8a1a6e7f5a04b933b11b9677113dcc44", "packages": [], "packages-dev": [{"name": "doctrine/instantiator", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/8e884e78f9f0eb1329e445619e04456e64d8051d", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d", "shasum": ""}, "require": {"php": ">=5.3,<8.0-DEV"}, "require-dev": {"athletic/athletic": "~0.1.8", "ext-pdo": "*", "ext-phar": "*", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://github.com/doctrine/instantiator", "keywords": ["constructor", "instantiate"], "time": "2015-06-14 21:17:01"}, {"name": "myclabs/deep-copy", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "a8773992b362b58498eed24bf85005f363c34771"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/a8773992b362b58498eed24bf85005f363c34771", "reference": "a8773992b362b58498eed24bf85005f363c34771", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"doctrine/collections": "1.*", "phpunit/phpunit": "~4.1"}, "type": "library", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "homepage": "https://github.com/myclabs/DeepCopy", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "time": "2015-11-20 12:04:31"}, {"name": "phpdocumentor/reflection-common", "version": "1.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "144c307535e82c8fdcaacbcfc1d6d8eeb896687c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/144c307535e82c8fdcaacbcfc1d6d8eeb896687c", "reference": "144c307535e82c8fdcaacbcfc1d6d8eeb896687c", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2015-12-27 11:43:31"}, {"name": "phpdocumentor/reflection-docblock", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "9270140b940ff02e58ec577c237274e92cd40cdd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/9270140b940ff02e58ec577c237274e92cd40cdd", "reference": "9270140b940ff02e58ec577c237274e92cd40cdd", "shasum": ""}, "require": {"php": ">=5.5", "phpdocumentor/reflection-common": "^1.0@dev", "phpdocumentor/type-resolver": "^0.2.0", "webmozart/assert": "^1.0"}, "require-dev": {"mockery/mockery": "^0.9.4", "phpunit/phpunit": "^4.4"}, "type": "library", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2016-06-10 09:48:41"}, {"name": "phpdocumentor/type-resolver", "version": "0.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "b39c7a5b194f9ed7bd0dd345c751007a41862443"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/b39c7a5b194f9ed7bd0dd345c751007a41862443", "reference": "b39c7a5b194f9ed7bd0dd345c751007a41862443", "shasum": ""}, "require": {"php": ">=5.5", "phpdocumentor/reflection-common": "^1.0"}, "require-dev": {"mockery/mockery": "^0.9.4", "phpunit/phpunit": "^5.2||^4.8.24"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "time": "2016-06-10 07:14:17"}, {"name": "phpspec/prophecy", "version": "v1.6.1", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "58a8137754bc24b25740d4281399a4a3596058e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/58a8137754bc24b25740d4281399a4a3596058e0", "reference": "58a8137754bc24b25740d4281399a4a3596058e0", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2", "sebastian/comparator": "^1.1", "sebastian/recursion-context": "^1.0"}, "require-dev": {"phpspec/phpspec": "^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-0": {"Prophecy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "time": "2016-06-07 08:13:47"}, {"name": "phpunit/php-code-coverage", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "900370c81280cc0d942ffbc5912d80464eaee7e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/900370c81280cc0d942ffbc5912d80464eaee7e9", "reference": "900370c81280cc0d942ffbc5912d80464eaee7e9", "shasum": ""}, "require": {"php": "^5.6 || ^7.0", "phpunit/php-file-iterator": "~1.3", "phpunit/php-text-template": "~1.2", "phpunit/php-token-stream": "^1.4.2", "sebastian/code-unit-reverse-lookup": "~1.0", "sebastian/environment": "^1.3.2", "sebastian/version": "~1.0|~2.0"}, "require-dev": {"ext-xdebug": ">=2.1.4", "phpunit/phpunit": "^5.4"}, "suggest": {"ext-dom": "*", "ext-xdebug": ">=2.4.0", "ext-xmlwriter": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2016-06-03 05:03:56"}, {"name": "phpunit/php-file-iterator", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "6150bf2c35d3fc379e50c7602b75caceaa39dbf0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/6150bf2c35d3fc379e50c7602b75caceaa39dbf0", "reference": "6150bf2c35d3fc379e50c7602b75caceaa39dbf0", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2015-06-21 13:08:43"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21 13:50:34"}, {"name": "phpunit/php-timer", "version": "1.0.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "38e9124049cf1a164f1e4537caf19c99bf1eb260"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/38e9124049cf1a164f1e4537caf19c99bf1eb260", "reference": "38e9124049cf1a164f1e4537caf19c99bf1eb260", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4|~5"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2016-05-12 18:03:57"}, {"name": "phpunit/php-token-stream", "version": "1.4.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "3144ae21711fb6cac0b1ab4cbe63b75ce3d4e8da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/3144ae21711fb6cac0b1ab4cbe63b75ce3d4e8da", "reference": "3144ae21711fb6cac0b1ab4cbe63b75ce3d4e8da", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "time": "2015-09-15 10:49:45"}, {"name": "phpunit/phpunit", "version": "5.4.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "2f1fc94b77ea6418bd6a06c64a1dac0645fbce59"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/phpunit/zipball/2f1fc94b77ea6418bd6a06c64a1dac0645fbce59", "reference": "2f1fc94b77ea6418bd6a06c64a1dac0645fbce59", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-pcre": "*", "ext-reflection": "*", "ext-spl": "*", "myclabs/deep-copy": "~1.3", "php": "^5.6 || ^7.0", "phpspec/prophecy": "^1.3.1", "phpunit/php-code-coverage": "^4.0", "phpunit/php-file-iterator": "~1.4", "phpunit/php-text-template": "~1.2", "phpunit/php-timer": "^1.0.6", "phpunit/phpunit-mock-objects": "^3.2", "sebastian/comparator": "~1.1", "sebastian/diff": "~1.2", "sebastian/environment": "^1.3 || ^2.0", "sebastian/exporter": "~1.2", "sebastian/global-state": "~1.0", "sebastian/object-enumerator": "~1.0", "sebastian/resource-operations": "~1.0", "sebastian/version": "~1.0|~2.0", "symfony/yaml": "~2.1|~3.0"}, "conflict": {"phpdocumentor/reflection-docblock": "3.0.2"}, "suggest": {"phpunit/php-invoker": "~1.1"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.4.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2016-06-16 06:01:15"}, {"name": "phpunit/phpunit-mock-objects", "version": "3.2.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects.git", "reference": "b13d0d9426ced06958bd32104653526a6c998a52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects/zipball/b13d0d9426ced06958bd32104653526a6c998a52", "reference": "b13d0d9426ced06958bd32104653526a6c998a52", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.6 || ^7.0", "phpunit/php-text-template": "^1.2", "sebastian/exporter": "^1.2"}, "conflict": {"phpunit/phpunit": "<5.4.0"}, "require-dev": {"phpunit/phpunit": "^5.4"}, "suggest": {"ext-soap": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Mock Object library for PHPUnit", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "keywords": ["mock", "xunit"], "time": "2016-06-12 07:37:26"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "c36f5e7cfce482fde5bf8d10d41a53591e0198fe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/c36f5e7cfce482fde5bf8d10d41a53591e0198fe", "reference": "c36f5e7cfce482fde5bf8d10d41a53591e0198fe", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "~5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "time": "2016-02-13 06:45:14"}, {"name": "sebastian/comparator", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "937efb279bd37a375bcadf584dec0726f84dbf22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/937efb279bd37a375bcadf584dec0726f84dbf22", "reference": "937efb279bd37a375bcadf584dec0726f84dbf22", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/diff": "~1.2", "sebastian/exporter": "~1.2"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "http://www.github.com/sebastian<PERSON>mann/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2015-07-26 15:48:44"}, {"name": "sebastian/diff", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "13edfd8706462032c2f52b4b862974dd46b71c9e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/diff/zipball/13edfd8706462032c2f52b4b862974dd46b71c9e", "reference": "13edfd8706462032c2f52b4b862974dd46b71c9e", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff"], "time": "2015-12-08 07:14:41"}, {"name": "sebastian/environment", "version": "1.3.7", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "4e8f0da10ac5802913afc151413bc8c53b6c2716"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/environment/zipball/4e8f0da10ac5802913afc151413bc8c53b6c2716", "reference": "4e8f0da10ac5802913afc151413bc8c53b6c2716", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2016-05-17 03:18:57"}, {"name": "sebastian/exporter", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "42c4c2eec485ee3e159ec9884f95b431287edde4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/42c4c2eec485ee3e159ec9884f95b431287edde4", "reference": "42c4c2eec485ee3e159ec9884f95b431287edde4", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/recursion-context": "~1.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2016-06-17 09:04:28"}, {"name": "sebastian/global-state", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bc37d50fea7d017d3d340f230811c9f1d7280af4", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2015-10-12 03:26:01"}, {"name": "sebastian/object-enumerator", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "d4ca2fb70344987502567bc50081c03e6192fb26"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/d4ca2fb70344987502567bc50081c03e6192fb26", "reference": "d4ca2fb70344987502567bc50081c03e6192fb26", "shasum": ""}, "require": {"php": ">=5.6", "sebastian/recursion-context": "~1.0"}, "require-dev": {"phpunit/phpunit": "~5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "time": "2016-01-28 13:25:10"}, {"name": "sebastian/recursion-context", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "913401df809e99e4f47b27cdd781f4a258d58791"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/913401df809e99e4f47b27cdd781f4a258d58791", "reference": "913401df809e99e4f47b27cdd781f4a258d58791", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "time": "2015-11-11 19:50:13"}, {"name": "sebastian/resource-operations", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/resource-operations/zipball/ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "shasum": ""}, "require": {"php": ">=5.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "time": "2015-07-28 20:34:47"}, {"name": "sebastian/version", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c829badbd8fdf16a0bad8aa7fa7971c029f1b9c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/version/zipball/c829badbd8fdf16a0bad8aa7fa7971c029f1b9c5", "reference": "c829badbd8fdf16a0bad8aa7fa7971c029f1b9c5", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2016-02-04 12:56:52"}, {"name": "symfony/yaml", "version": "v3.1.2", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "2884c26ce4c1d61aebf423a8b912950fe7c764de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/2884c26ce4c1d61aebf423a8b912950fe7c764de", "reference": "2884c26ce4c1d61aebf423a8b912950fe7c764de", "shasum": ""}, "require": {"php": ">=5.5.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "time": "2016-06-29 05:41:56"}, {"name": "webmozart/assert", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/webmozart/assert.git", "reference": "30eed06dd6bc88410a4ff7f77b6d22f3ce13dbde"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/assert/zipball/30eed06dd6bc88410a4ff7f77b6d22f3ce13dbde", "reference": "30eed06dd6bc88410a4ff7f77b6d22f3ce13dbde", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "^4.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2015-08-24 13:29:44"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=5.3.2"}, "platform-dev": []}