<?php
	require_once('../../koneksi.php');
	require_once('../../check_session.php');
	requireLevel('Staff IT');
	require_once('../../library/format_tanggal/format_tgl_saja.php');
	require_once('../../library/dompdf/autoload.inc.php');
?>

<?php
	// Rererence the Dompdf namespace
	use Dompdf\Dompdf;

	// Instantiate dompdf class
	$dompdf = new dompdf();

?>

<?php
if(isset($_GET['kode'])) {
$id_mapping_validasi = $_GET['kode'];

$sqledt = "SELECT mapping_validasi.*, mapping_validasi.sekolah_id, sekolah.npsn, sekolah.nama_sekolah, sekolah.alamat, sekolah.kecamatan,
            sekolah.nama_kepsek, sekolah.no_hp_kepsek, sekolah.no_wa_kepsek, jenjang.nm_jenjang,
            kab_kota.nm_kota, asesor_1.nia1, asesor_1.nm_asesor1, asesor_1.no_hp as hp1,
            (SELECT kab_kota.nm_kota from asesor_1 LEFT JOIN kab_kota ON asesor_1.kota_id1=kab_kota.kota_id
             WHERE mapping_validasi.kd_asesor1=asesor_1.kd_asesor1) as kota1,
            asesor_2.nia2, asesor_2.nm_asesor2, asesor_2.no_hp as hp2,
            (SELECT kab_kota.nm_kota from asesor_2 LEFT JOIN kab_kota ON asesor_2.kota_id2=kab_kota.kota_id
            WHERE mapping_validasi.kd_asesor2=asesor_2.kd_asesor2) as kota2 FROM mapping_validasi
            LEFT JOIN sekolah ON mapping_validasi.sekolah_id=sekolah.sekolah_id
            LEFT JOIN jenjang ON sekolah.jenjang_id=jenjang.jenjang_id
            LEFT JOIN kab_kota ON sekolah.kota_id=kab_kota.kota_id
            LEFT JOIN asesor_1 ON mapping_validasi.kd_asesor1=asesor_1.kd_asesor1
             LEFT JOIN asesor_2 ON mapping_validasi.kd_asesor2=asesor_2.kd_asesor2
            WHERE mapping_validasi.id_mapping_validasi = '$id_mapping_validasi' AND mapping_validasi.provinsi_id = '".$_SESSION['provinsi_id']."' ";
$result = $conn->query($sqledt);
if($result->num_rows > 0) {
while($row = $result->fetch_assoc()) {
    
    $npsn = $row['npsn'];
    $sekolah = $row['nama_sekolah'];

    $sqlprov = " SELECT provinsi.*, kab_kota.nm_kota as kota_kantor FROM provinsi
                 LEFT JOIN kab_kota ON provinsi.kota_id=kab_kota.kota_id
                 WHERE provinsi.provinsi_id='".$_SESSION['provinsi_id']."' ";
    $hasil = $conn->query($sqlprov);
    if($hasil->num_rows > 0) {
    while($baris = $hasil->fetch_assoc()) {

        $nama_provinsi   = $baris['nama_provinsi'];
        $alamat          = $baris['alamat_provinsi'];
        $kota_kantor     = $baris['kota_kantor'];
        $alamat          = $baris['alamat_provinsi'];
        $nama_ketua_banp = $baris['nama_ketua_banp'];
        $ttd_ketua_banp  = $baris['ttd_ketua_banp'];

    // batas atas load html ke pdf
    $dompdf->load_html('

<html>
<style type="text/css">
    html{margin:10px 80px;}     
</style>
<body>

<table width="100%" border="0" cellspacing="0" cellpadding="0" align="center" style="font-family:Arial, Helvetica, sans-serif">
  <tr>
    <td width="10%"><img src="../../logo ban.png" width="120" height="120"></td>
    <td valign="top" align="center" width="50%">
        <b style="font-size:16px">BADAN AKREDITASI NASIONAL SEKOLAH/MADRASAH</b><br>
        <b style="font-size:16px">PROVINSI '.strtoupper($nama_provinsi).'</b> <br>
      <a style="font-size:14px">'.$alamat.'</a>
    </td>
  </tr>
  <tr>
    <td colspan="2"><b><hr width="100%"></b></td>
  </tr>
  <tr>
    <td colspan="2">&nbsp;</td>
  </tr>
</table>

<table width="100%" border="0" cellspacing="0" cellpadding="0" align="center" style="font-family:Arial, Helvetica, sans-serif">
    <tr>
      <td align="center" style="font-size:16px"><b>SURAT TUGAS VALIDASI</b></td>
    </tr>
    <tr>
      <td align="center" style="font-size:16px"><b>Nomor : '.$row["no_surat_validasi"].' </b></td>
    </tr>
</table>

<table width="90%" border="0" cellspacing="0" cellpadding="0" align="center" style="font-family:Arial, Helvetica, sans-serif; font-size:14px">
    <tr>
        <td colspan="5">&nbsp;</td>
    </tr>
    <tr>
        <td colspan="5">Badan Akreditasi Nasional Sekolah/Madrasah Provinsi '.$nama_provinsi.' menugaskan kepada :</td>
    </tr>
    <tr>
        <td colspan="5">&nbsp;</td>
    </tr>
    <tr valign="top">
        <td width="6%">&nbsp;</td>
        <td width="32%">Nama</td>
        <td width="1%">:&nbsp;</td>
        <td width="1%">1.</td>
        <td width="60%">'.$row["nm_asesor1"].'</td>
    </tr>
    <tr valign="top">
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td>2.</td>
        <td>'.$row["nm_asesor2"].' </td>
    </tr>
    <tr valign="top">
        <td>&nbsp;</td>
        <td>Jabatan</td>
        <td>:&nbsp;</td>
        <td colspan="2">Asesor</td>
    </tr>
    <tr>
        <td colspan="5">&nbsp;</td>
    </tr>
    <tr>
        <td colspan="5">Untuk melaksanakan validasi dan verifikasi hasil visitasi pada :</td>
    </tr>
    <tr>
        <td colspan="5">&nbsp;</td>
    </tr>
    <tr valign="top">
        <td>&nbsp;</td>
        <td>Nama Sekolah/Madrasah</td>
        <td>:&nbsp;</td>
        <td colspan="2">'.$row["nama_sekolah"].' </td>
    </tr>
    <tr valign="top">
        <td>&nbsp;</td>
        <td>NPSN</td>
        <td>:&nbsp;</td>
        <td colspan="2">'.$row["npsn"].' </td>
    </tr>
    <tr valign="top">
        <td>&nbsp;</td>
        <td>Kabupaten/Kota</td>
        <td>:&nbsp;</td>
        <td colspan="2">'.$row["nm_kota"].' </td>
    </tr>
    <tr valign="top">
        <td>&nbsp;</td>
        <td>Waktu Pelaksanaan</td>
        <td>:&nbsp;</td>
        <td colspan="2">Tanggal '.format_tgl_saja($row["tgl_mulai_validasi"]).' s.d '.format_tgl_saja($row["tgl_akhir_validasi"]).'</td>
    </tr>
    <tr>
        <td colspan="5">&nbsp;</td>
    </tr>
    <tr>
        <td colspan="5">Masing-masing asesor melaksanakan tugas sebagai berikut :</td>
    </tr>
    <tr>
        <td colspan="5">&nbsp;</td>
    </tr>
    <tr>
        <td colspan="5">
            <table width="100%">
                <tr valign="top">
                    <td width="6%">&nbsp;</td>
                    <td width="2%">1.&nbsp;</td>
                    <td width="92%">
                        Tim Validasi dan Verifikasi Hasil Visitasi melakukan pemeriksaan terhadap proses visitasi dengan melihat Berita Acara Pelaksanaan Visitasi dan Kartu Kendali Proses Visitasi.
                    </td>
                </tr>
                <tr valign="top">
                    <td>&nbsp;</td>
                    <td>2.&nbsp;</td>
                    <td>
                        Tim Validasi dan Verifikasi Hasil Visitasi melakukan pemeriksaan terhadap hasil visitasi melalui aplikasi Sispena-S/M, yang mencakup :
                    </td>
                </tr>
                <tr valign="top">
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>
                        <table>
                            <tr valign="top">
                                <td>a.&nbsp;</td>
                                <td>Validasi kelengkapan Data Pendukung yang diunggah Asesor meliputi:<br>(1) Pindaian/Foto Lembar Rekapitulasi Nilai Akreditasi yang sudah <br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ditandatangan lengkap oleh kedua asesor, <br>(2) Foto Visitasi atau Tangkapan Layar Visitasi Daring, <br>(3) Berita Acara Visitasi yang sudah ditandatangan
secara digital, dan <br>(4) Pakta Integritas yang sudah ditandatangan masing-masing asesor.
                                </td>
                            </tr>
                            <tr valign="top">
                                <td>b.&nbsp;</td>
                                <td>Verifikasi hasil penilaian asesor
                                </td>
                            </tr>
                            <tr valign="top">
                                <td>c.&nbsp;</td>
                                <td>Verifikasi kesesuaian antara nilai visitasi dan rekomendasi.
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    <tr>
        <td colspan="5">&nbsp;</td>
    </tr>
    <tr>
        <td colspan="5">Surat Tugas ini diberikan untuk dilaksanakan sebagaimana mestinya.</td>
    </tr>
    <tr>
        <td colspan="5">&nbsp;</td>
    </tr>
    <tr>
        <td colspan="5">&nbsp;</td>
    </tr>
    <tr>
        <td colspan="5">
            <table width="100%">
                <tr>
                    <td width="45%">&nbsp;</td>
                    <td width="55%" align="center">
                        <div>'.$kota_kantor.', '.format_tgl_saja($row["tgl_surat_validasi"]).' <br>
                        Ketua<br>
                   <img src="../../files/ttd_ketua_stempel/'.$ttd_ketua_banp.'" height="150" width="285" style="z-index:-2; position:absolute; left:280px; top:68%" ><br><br><br><br><br>
                    '.$nama_ketua_banp.'</div>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>
</body></html>;
    ');
    // batas bawah load html ke pdf

    
    // setup paper size
    $dompdf->setPaper('A4','portrait');
    
    // Render the HTML as PDF
    $dompdf->render();
    
    // Output the generate PDF
    $dompdf->stream($npsn .' ' .$sekolah, array("Attachment"=>0));
?>

<?php
}}}}}
?>