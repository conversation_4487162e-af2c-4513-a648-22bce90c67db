<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input
    if (!isset($_POST['nama_tahun']) || empty(trim($_POST['nama_tahun']))) {
        throw new Exception('Tahun akreditasi harus diisi');
    }
    
    $nama_tahun = $conn->real_escape_string(trim($_POST['nama_tahun']));
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Validasi format tahun (4 digit)
    if (!preg_match('/^\d{4}$/', $nama_tahun)) {
        throw new Exception('Tahun akreditasi harus berformat 4 digit angka');
    }
    
    // Begin transaction
    $conn->autocommit(false);
    
    // Cek apakah sudah ada record untuk provinsi ini
    $check_query = "SELECT id_mapping_tahun, nama_tahun FROM mapping_paud_validasi_tahun WHERE provinsi_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("i", $provinsi_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    $old_tahun = '';
    if ($check_result->num_rows > 0) {
        // Update existing record
        $existing_data = $check_result->fetch_assoc();
        $old_tahun = $existing_data['nama_tahun'];
        
        $update_query = "UPDATE mapping_paud_validasi_tahun SET nama_tahun = ? WHERE provinsi_id = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("si", $nama_tahun, $provinsi_id);
        
        if (!$update_stmt->execute()) {
            throw new Exception('Gagal mengupdate tahun akreditasi validasi: ' . $conn->error);
        }
        
        $operation = 'update';
        $record_id = $existing_data['id_mapping_tahun'];
    } else {
        // Insert new record
        $insert_query = "INSERT INTO mapping_paud_validasi_tahun (nama_tahun, provinsi_id) VALUES (?, ?)";
        $insert_stmt = $conn->prepare($insert_query);
        $insert_stmt->bind_param("si", $nama_tahun, $provinsi_id);
        
        if (!$insert_stmt->execute()) {
            throw new Exception('Gagal menyimpan tahun akreditasi validasi: ' . $conn->error);
        }
        
        $operation = 'insert';
        $record_id = $conn->insert_id;
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Log successful operation
    $log_message = "Tahun Akreditasi Validasi PAUD $operation - ID: $record_id, Old: '$old_tahun', New: '$nama_tahun', Provinsi: $provinsi_id, User: " . $_SESSION['nm_user'];
    error_log($log_message);
    
    echo json_encode([
        'success' => true,
        'message' => 'Tahun akreditasi validasi berhasil diupdate',
        'data' => [
            'id_mapping_tahun' => $record_id,
            'nama_tahun' => $nama_tahun,
            'provinsi_id' => $provinsi_id,
            'operation' => $operation,
            'old_tahun' => $old_tahun
        ]
    ]);
    
} catch (Exception $e) {
    // Rollback transaction
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Update Tahun Akreditasi Validasi Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
