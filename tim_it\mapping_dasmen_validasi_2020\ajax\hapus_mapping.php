<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi required fields
    if (!isset($_POST['id_mapping_validasi']) || empty(trim($_POST['id_mapping_validasi']))) {
        throw new Exception('ID mapping validasi harus diisi');
    }
    
    // Sanitasi input
    $id_mapping_validasi = intval($_POST['id_mapping_validasi']);
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Validasi ID mapping
    if ($id_mapping_validasi <= 0) {
        throw new Exception('ID mapping validasi tidak valid');
    }
    
    // Debug: Log values
    error_log("Hapus Mapping - ID: $id_mapping_validasi, Provinsi: $provinsi_id");
    
    // Cek apakah data mapping validasi exists dan milik provinsi yang benar
    $check_query = "SELECT mv.id_mapping_validasi, mv.sekolah_id, mv.tahun_akreditasi, mv.tahap,
                           s.nama_sekolah, s.npsn,
                           a1.nm_asesor1, a2.nm_asesor2
                    FROM mapping_validasi mv
                    LEFT JOIN sekolah s ON mv.sekolah_id = s.sekolah_id
                    LEFT JOIN asesor_1 a1 ON mv.kd_asesor1 = a1.kd_asesor1
                    LEFT JOIN asesor_2 a2 ON mv.kd_asesor2 = a2.kd_asesor2
                    WHERE mv.id_mapping_validasi = ? AND mv.provinsi_id = ?";
    
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("ii", $id_mapping_validasi, $provinsi_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows === 0) {
        throw new Exception('Data mapping validasi tidak ditemukan atau Anda tidak memiliki akses untuk menghapus data ini');
    }
    
    $mapping_data = $check_result->fetch_assoc();
    
    // Begin transaction untuk hard delete
    $conn->autocommit(false);
    
    // Hard delete dari tabel mapping_validasi
    $delete_query = "DELETE FROM mapping_validasi WHERE id_mapping_validasi = ? AND provinsi_id = ?";
    $delete_stmt = $conn->prepare($delete_query);
    $delete_stmt->bind_param("ii", $id_mapping_validasi, $provinsi_id);
    
    if (!$delete_stmt->execute()) {
        throw new Exception('Gagal menghapus data mapping validasi: ' . $conn->error);
    }
    
    // Cek apakah ada row yang terhapus
    if ($delete_stmt->affected_rows === 0) {
        throw new Exception('Tidak ada data yang dihapus. Data mungkin sudah tidak ada atau Anda tidak memiliki akses');
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Log successful deletion
    error_log("Hapus Mapping Success - ID: $id_mapping_validasi, Sekolah: " . $mapping_data['nama_sekolah'] . ", User: " . $_SESSION['nm_user']);
    
    echo json_encode([
        'success' => true,
        'message' => 'Data mapping validasi berhasil dihapus',
        'data' => [
            'id_mapping_validasi' => $id_mapping_validasi,
            'nama_sekolah' => $mapping_data['nama_sekolah'],
            'npsn' => $mapping_data['npsn'],
            'nm_asesor1' => $mapping_data['nm_asesor1'],
            'nm_asesor2' => $mapping_data['nm_asesor2'],
            'tahun_akreditasi' => $mapping_data['tahun_akreditasi'],
            'tahap' => $mapping_data['tahap']
        ]
    ]);
    
} catch (Exception $e) {
    // Rollback transaction
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Hapus Mapping Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
