<?php
/**
 * AJAX handler untuk mendapatkan dropdown options
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi input
    if (!isset($_POST['type']) || empty($_POST['type'])) {
        throw new Exception('Type dropdown tidak valid');
    }
    
    $type = $_POST['type'];
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    $data = [];
    
    switch ($type) {
        case 'kab_kota':
            // Query untuk mendapatkan kab/kota berdasarkan provinsi session
            $query = "SELECT kota_id, nm_kota FROM kab_kota WHERE provinsi_id = ? ORDER BY nm_kota ASC";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("i", $provinsi_id_session);
            $stmt->execute();
            $result = $stmt->get_result();
            
            while ($row = $result->fetch_assoc()) {
                $data[] = [
                    'kota_id' => $row['kota_id'],
                    'nm_kota' => $row['nm_kota']
                ];
            }
            break;
            
        case 'jenjang':
            // Query untuk mendapatkan jenjang
            $query = "SELECT jenjang_id, nm_jenjang FROM jenjang ORDER BY nm_jenjang ASC";
            $result = $conn->query($query);
            
            while ($row = $result->fetch_assoc()) {
                $data[] = [
                    'jenjang_id' => $row['jenjang_id'],
                    'nm_jenjang' => $row['nm_jenjang']
                ];
            }
            break;
            
        case 'status_keaktifan':
            // Query untuk mendapatkan status keaktifan
            $query = "SELECT status_keaktifan_id, nm_status FROM status_keaktifan ORDER BY status_keaktifan_id ASC";
            $result = $conn->query($query);
            
            while ($row = $result->fetch_assoc()) {
                $data[] = [
                    'status_keaktifan_id' => $row['status_keaktifan_id'],
                    'nm_status' => $row['nm_status']
                ];
            }
            break;
            
        default:
            throw new Exception('Type dropdown tidak dikenali');
    }
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Dropdown options berhasil dimuat',
        'data' => $data
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Log error
    error_log("Get Dropdown Options Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => []
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
