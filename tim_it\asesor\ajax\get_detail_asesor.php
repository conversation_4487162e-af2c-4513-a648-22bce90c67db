<?php
/**
 * AJAX handler untuk mengambil detail asesor
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi input
    if (!isset($_POST['id_asesor']) || empty($_POST['id_asesor'])) {
        throw new Exception('ID Asesor tidak valid');
    }
    
    $id_asesor = intval($_POST['id_asesor']);
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Query untuk mengambil detail asesor dengan JOIN
    $query = "SELECT a.*, k.nm_kota, sk.nm_status, j.nm_jenjang
              FROM asesor a
              LEFT JOIN kab_kota k ON a.kota_id = k.kota_id
              LEFT JOIN status_keaktifan sk ON a.status_keaktifan_id = sk.status_keaktifan_id
              LEFT JOIN jenjang j ON a.jenjang_id = j.jenjang_id
              WHERE a.id_asesor = ? AND a.provinsi_id = ? AND a.soft_delete = '1'";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ii", $id_asesor, $provinsi_id_session);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        throw new Exception('Data asesor tidak ditemukan');
    }
    
    $data = $result->fetch_assoc();
    
    // Format tanggal jika ada
    if ($data['tgl_lahir']) {
        $data['tgl_lahir'] = date('d-m-Y', strtotime($data['tgl_lahir']));
    }
    
    if ($data['thn_terbit_sertifikat']) {
        $data['thn_terbit_sertifikat'] = date('d-m-Y', strtotime($data['thn_terbit_sertifikat']));
    }
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Detail asesor berhasil dimuat',
        'data' => $data
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Log error
    error_log("Get Detail Asesor Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
