<?php
/**
 * Server-side processing untuk DataTables
 * Menampilkan data sekolah dengan pagination, search, dan sorting
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Ambil parameter dari DataTables
    $draw = isset($_POST['draw']) ? intval($_POST['draw']) : 1;
    $start = isset($_POST['start']) ? intval($_POST['start']) : 0;
    $length = isset($_POST['length']) ? intval($_POST['length']) : 10;
    $search_value = isset($_POST['search']['value']) ? $_POST['search']['value'] : '';
    $order_column = isset($_POST['order'][0]['column']) ? intval($_POST['order'][0]['column']) : 2;
    $order_dir = isset($_POST['order'][0]['dir']) ? $_POST['order'][0]['dir'] : 'asc';
    

    
    // Definisi kolom untuk sorting
    $columns = [
        0 => 'sekolah_id', // NO (tidak bisa di-sort)
        1 => 's.npsn',
        2 => 's.nama_sekolah',
        3 => 'j.nm_jenjang',
        4 => 'k.nm_kota',
        5 => 's.kecamatan',
        6 => 'sk.nm_status',
        7 => 'sekolah_id' // AKSI (tidak bisa di-sort)
    ];
    
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];

    // Base query dengan JOIN
    $base_query = "FROM sekolah s
                   LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                   LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
                   LEFT JOIN status_keaktifan sk ON s.status_keaktifan_id = sk.status_keaktifan_id
                   WHERE s.soft_delete = 1 AND s.rumpun = 'paud' AND s.provinsi_id = {$provinsi_id_session}";
    
    // Inisialisasi kondisi WHERE
    $where_conditions = [];
    $bind_params = [];
    $bind_types = '';
    
    // Tambahkan kondisi search
    if (!empty($search_value)) {
        $search_conditions = [
            "s.npsn LIKE ?",
            "s.nama_sekolah LIKE ?",
            "j.nm_jenjang LIKE ?",
            "k.nm_kota LIKE ?",
            "s.kecamatan LIKE ?",
            "sk.nm_status LIKE ?"
        ];
        $where_conditions[] = "(" . implode(" OR ", $search_conditions) . ")";

        // Tambahkan parameter search untuk setiap field
        for ($i = 0; $i < 6; $i++) {
            $bind_params[] = "%{$search_value}%";
            $bind_types .= 's';
        }
    }
    
    // Gabungkan semua kondisi WHERE
    if (!empty($where_conditions)) {
        $base_query .= " AND " . implode(" AND ", $where_conditions);
    }
    
    // Query untuk menghitung total records (tanpa filter)
    $total_query = "SELECT COUNT(*) as total FROM sekolah s WHERE s.soft_delete = 1 AND s.rumpun = 'paud' AND s.provinsi_id = {$provinsi_id_session}";
    $total_result = $conn->query($total_query);
    $total_records = $total_result->fetch_assoc()['total'];
    
    // Query untuk menghitung filtered records
    $filtered_query = "SELECT COUNT(*) as total " . $base_query;
    if (!empty($bind_params)) {
        $filtered_stmt = $conn->prepare($filtered_query);
        if (!empty($bind_types)) {
            $filtered_stmt->bind_param($bind_types, ...$bind_params);
        }
        $filtered_stmt->execute();
        $filtered_records = $filtered_stmt->get_result()->fetch_assoc()['total'];
    } else {
        $filtered_result = $conn->query($filtered_query);
        $filtered_records = $filtered_result->fetch_assoc()['total'];
    }
    
    // Query untuk mengambil data dengan pagination dan sorting
    $data_query = "SELECT s.sekolah_id, s.npsn, s.nama_sekolah, s.jenjang_id, s.kecamatan,
                          s.status_keaktifan_id, j.nm_jenjang, k.nm_kota, k.kota_id, sk.nm_status
                   " . $base_query;
    
    // Tambahkan ORDER BY
    if (isset($columns[$order_column])) {
        $data_query .= " ORDER BY " . $columns[$order_column] . " " . $order_dir;
    }
    
    // Tambahkan LIMIT untuk pagination
    if ($length != -1) {
        $data_query .= " LIMIT ?, ?";
        $bind_params[] = $start;
        $bind_params[] = $length;
        $bind_types .= 'ii';
    }
    
    // Execute query untuk data
    if (!empty($bind_params)) {
        $data_stmt = $conn->prepare($data_query);
        if (!empty($bind_types)) {
            $data_stmt->bind_param($bind_types, ...$bind_params);
        }
        $data_stmt->execute();
        $data_result = $data_stmt->get_result();
    } else {
        $data_result = $conn->query($data_query);
    }
    
    // Siapkan data untuk response
    $data = [];
    while ($row = $data_result->fetch_assoc()) {
        $data[] = [
            'sekolah_id' => $row['sekolah_id'],
            'npsn' => $row['npsn'],
            'nama_sekolah' => $row['nama_sekolah'],
            'nm_jenjang' => $row['nm_jenjang'],
            'nm_kota' => $row['nm_kota'],
            'kecamatan' => $row['kecamatan'],
            'status_keaktifan_id' => $row['status_keaktifan_id'],
            'nm_status' => $row['nm_status']
        ];
    }
    
    // Response JSON untuk DataTables
    $response = [
        "draw" => $draw,
        "recordsTotal" => intval($total_records),
        "recordsFiltered" => intval($filtered_records),
        "data" => $data
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Log error
    error_log("DataTables Error: " . $e->getMessage());
    
    // Response error
    $error_response = [
        "draw" => isset($draw) ? $draw : 1,
        "recordsTotal" => 0,
        "recordsFiltered" => 0,
        "data" => [],
        "error" => "Terjadi kesalahan saat memuat data"
    ];
    
    echo json_encode($error_response);
}
?>
