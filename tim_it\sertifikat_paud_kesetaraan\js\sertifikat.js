$(document).ready(function() {
    // Initialize DataTable
    initDataTable();
    
    // Event handlers
    initEventHandlers();
});

function initDataTable() {
    $('#table-sertifikat').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: 'ajax/get_sertifikat.php',
            type: 'POST'
        },
        columns: [
            { 
                data: null,
                orderable: false,
                searchable: false,
                render: function(data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { data: 'npsn' },
            { data: 'nama_sekolah' },
            { data: 'nm_jenjang' },
            { data: 'nm_kab_kota' },
            { 
                data: 'tahun_akreditasi',
                className: 'text-center'
            },
            { 
                data: null,
                orderable: false,
                searchable: false,
                className: 'text-center',
                render: function(data, type, row) {
                    return `
                        <button type="button" class="btn btn-info btn-sm btn-action" 
                                onclick="previewSertifikat('${row.nama_file}')" 
                                title="Preview Sertifikat">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-danger btn-sm btn-action" 
                                onclick="deleteSertifikat(${row.sertifikat_id}, '${row.nama_file}')" 
                                title="Hapus Sertifikat">
                            <i class="fas fa-trash"></i>
                        </button>
                    `;
                }
            }
        ],
        order: [[5, 'desc']], // Order by tahun_akreditasi descending
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: "Memuat data...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(disaring dari _MAX_ total data)",
            paginate: {
                first: "Pertama",
                last: "Terakhir",
                next: "Selanjutnya",
                previous: "Sebelumnya"
            },
            emptyTable: "Tidak ada data sertifikat"
        },
        responsive: true,
        autoWidth: false
    });
}

function initEventHandlers() {
    // Form submit handler
    $('#form-tambah-sertifikat').on('submit', function(e) {
        e.preventDefault();
        uploadSertifikat();
    });

    // Modal reset handler
    $('#modal-tambah-sertifikat').on('hidden.bs.modal', function() {
        resetForm();
    });

    // File input change handler
    $('#nama_file').on('change', function() {
        validateFile(this);
    });
}

function uploadSertifikat() {
    // Debug: Log form data
    console.log('Form being submitted');
    
    var formData = new FormData($('#form-tambah-sertifikat')[0]);
    
    // Debug: Log FormData contents
    console.log('FormData contents:');
    for (var pair of formData.entries()) {
        console.log(pair[0] + ': ' + pair[1]);
    }
    
    // Disable submit button
    var submitBtn = $('#form-tambah-sertifikat button[type="submit"]');
    var originalText = submitBtn.html();
    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Mengupload...');
    
    $.ajax({
        url: 'ajax/upload_sertifikat.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function(response) {
            console.log('Upload response:', response);
            if (response.success) {
                showAlert('success', response.message);
                $('#modal-tambah-sertifikat').modal('hide');
                $('#table-sertifikat').DataTable().ajax.reload();
            } else {
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('Upload error:', xhr.responseText);
            showAlert('error', 'Terjadi kesalahan saat mengupload sertifikat: ' + error);
        },
        complete: function() {
            // Re-enable submit button
            submitBtn.prop('disabled', false).html(originalText);
        }
    });
}

function previewSertifikat(namaFile) {
    if (!namaFile) {
        showAlert('error', 'File tidak ditemukan');
        return;
    }
    
    // Open PDF in new tab - path yang benar
    var fileUrl = '../../../simak/files/file_sertifikat_paud/' + namaFile;
    window.open(fileUrl, '_blank');
}

function deleteSertifikat(sertifikatId, namaFile) {
    if (!confirm('Apakah Anda yakin ingin menghapus sertifikat ini?\n\nFile: ' + namaFile + '\n\nData yang dihapus tidak dapat dikembalikan!')) {
        return;
    }
    
    $.ajax({
        url: 'ajax/delete_sertifikat.php',
        type: 'POST',
        data: {
            sertifikat_id: sertifikatId,
            nama_file: namaFile
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showAlert('success', response.message);
                $('#table-sertifikat').DataTable().ajax.reload();
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Terjadi kesalahan saat menghapus sertifikat');
        }
    });
}

function validateFile(input) {
    var file = input.files[0];
    if (file) {
        // Check file type
        if (file.type !== 'application/pdf') {
            showAlert('error', 'Hanya file PDF yang diizinkan');
            input.value = '';
            return false;
        }
        
        // Check file size (10MB)
        if (file.size > 10 * 1024 * 1024) {
            showAlert('error', 'Ukuran file maksimal 10MB');
            input.value = '';
            return false;
        }
    }
    return true;
}

function resetForm() {
    $('#form-tambah-sertifikat')[0].reset();
}

function showAlert(type, message) {
    var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    var iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';
    
    var alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${iconClass}"></i> ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `;
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert at the top of content
    $('.content-wrapper .content').prepend(alertHtml);
    
    // Auto hide after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
