<?php
/**
 * AJAX handler untuk DataTable mapping visitasi SM
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

header('Content-Type: application/json');

try {
    // Ambil provinsi_id dari session
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Parameter DataTable
    $draw = intval($_POST['draw']);
    $start = intval($_POST['start']);
    $length = intval($_POST['length']);
    $search_value = $_POST['search']['value'];
    
    // Base query untuk count total records
    $count_query = "SELECT COUNT(*) as total 
                   FROM mapping_2024 m
                   LEFT JOIN sekolah s ON m.sekolah_id = s.sekolah_id
                   WHERE m.provinsi_id = ?
                     AND s.rumpun IN ('sma', 'smk')
                     AND s.soft_delete = '1'";
    
    $count_stmt = $conn->prepare($count_query);
    $count_stmt->bind_param("i", $provinsi_id_session);
    $count_stmt->execute();
    $total_records = $count_stmt->get_result()->fetch_assoc()['total'];
    
    // Query utama dengan JOIN
    $main_query = "SELECT 
                    m.id_mapping,
                    m.tahun_akreditasi,
                    m.tahap,
                    s.npsn,
                    s.nama_sekolah,
                    s.rumpun,
                    j.nm_jenjang,
                    k.nm_kota,
                    a1.nia1,
                    a1.nm_asesor1,
                    a2.nia2,
                    a2.nm_asesor2
                   FROM mapping_2024 m
                   LEFT JOIN sekolah s ON m.sekolah_id = s.sekolah_id
                   LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                   LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
                   LEFT JOIN asesor_1 a1 ON m.kd_asesor1 = a1.kd_asesor1
                   LEFT JOIN asesor_2 a2 ON m.kd_asesor2 = a2.kd_asesor2
                   WHERE m.provinsi_id = ?
                     AND s.rumpun IN ('sma', 'smk')
                     AND s.soft_delete = '1'";
    
    // Add search filter
    if (!empty($search_value)) {
        $main_query .= " AND (s.npsn LIKE ? 
                        OR s.nama_sekolah LIKE ? 
                        OR j.nm_jenjang LIKE ?
                        OR s.rumpun LIKE ?
                        OR k.nm_kota LIKE ?
                        OR a1.nia1 LIKE ?
                        OR a1.nm_asesor1 LIKE ?
                        OR a2.nia2 LIKE ?
                        OR a2.nm_asesor2 LIKE ?
                        OR m.tahun_akreditasi LIKE ?)";
    }
    
    // Count filtered records
    $filtered_query = $main_query;
    $filtered_stmt = $conn->prepare($filtered_query);
    
    if (!empty($search_value)) {
        $search_param = "%{$search_value}%";
        $filtered_stmt->bind_param("issssssssss", 
            $provinsi_id_session,
            $search_param, $search_param, $search_param, $search_param, $search_param,
            $search_param, $search_param, $search_param, $search_param, $search_param
        );
    } else {
        $filtered_stmt->bind_param("i", $provinsi_id_session);
    }
    
    $filtered_stmt->execute();
    $filtered_records = $filtered_stmt->get_result()->num_rows;
    
    // Add ordering
    $order_column = $_POST['order'][0]['column'];
    $order_dir = $_POST['order'][0]['dir'];
    
    $columns = ['', 's.npsn', 's.nama_sekolah', 'j.nm_jenjang', 's.rumpun', 'k.nm_kota', 
                'a1.nia1', 'a1.nm_asesor1', 'a2.nia2', 'a2.nm_asesor2', 'm.tahun_akreditasi', 'm.tahap'];
    
    if (isset($columns[$order_column])) {
        $main_query .= " ORDER BY " . $columns[$order_column] . " " . $order_dir;
    } else {
        $main_query .= " ORDER BY s.nama_sekolah ASC";
    }
    
    // Add limit
    $main_query .= " LIMIT ?, ?";
    
    // Execute main query
    $main_stmt = $conn->prepare($main_query);
    
    if (!empty($search_value)) {
        $search_param = "%{$search_value}%";
        $main_stmt->bind_param("isssssssssii", 
            $provinsi_id_session,
            $search_param, $search_param, $search_param, $search_param, $search_param,
            $search_param, $search_param, $search_param, $search_param, $search_param,
            $start, $length
        );
    } else {
        $main_stmt->bind_param("iii", $provinsi_id_session, $start, $length);
    }
    
    $main_stmt->execute();
    $result = $main_stmt->get_result();
    
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = [
            'id_mapping' => $row['id_mapping'],
            'npsn' => $row['npsn'],
            'nama_sekolah' => $row['nama_sekolah'],
            'nm_jenjang' => $row['nm_jenjang'],
            'rumpun' => strtoupper($row['rumpun']),
            'nm_kota' => $row['nm_kota'],
            'nia1' => $row['nia1'],
            'nm_asesor1' => $row['nm_asesor1'],
            'nia2' => $row['nia2'],
            'nm_asesor2' => $row['nm_asesor2'],
            'tahun_akreditasi' => $row['tahun_akreditasi'],
            'tahap' => $row['tahap']
        ];
    }
    
    // Response untuk DataTable
    $response = [
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $filtered_records,
        'data' => $data
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    error_log("Get Mapping Visitasi Error: " . $e->getMessage());
    
    echo json_encode([
        'draw' => intval($_POST['draw']),
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => 'Terjadi kesalahan saat memuat data'
    ]);
}

$conn->close();
?>
