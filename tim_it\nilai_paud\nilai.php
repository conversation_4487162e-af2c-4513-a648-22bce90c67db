<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');
?>

<!-- Include header -->
<?php include '../header.php'; ?>

<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

<!-- Content Wrapper -->
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Data Nilai Akreditasi Paud</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../dashboard.php">Home</a></li>
                        <li class="breadcrumb-item active">Data Nilai Akreditasi Paud</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Alert container -->
            <div id="alert-container"></div>
            
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-line"></i> Data Nilai Akreditasi Paud
                            </h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-success btn-sm mr-2" id="btn-export-terakhir">
                                    <i class="fas fa-file-excel"></i> Export Nilai Terakhir
                                </button>
                                <button type="button" class="btn btn-info btn-sm mr-2" id="btn-export-keseluruhan">
                                    <i class="fas fa-download"></i> Export Keseluruhan
                                </button>
                                <button type="button" class="btn btn-primary btn-sm" id="btn-add">
                                    <i class="fas fa-plus"></i> Tambah Data
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <table id="table-nilai" class="table table-bordered table-striped" width="100%">
                                <thead>
                                    <tr>
                                        <th>NO</th>
                                        <th>NPSN</th>
                                        <th>NAMA SEKOLAH</th>
                                        <th>JENJANG</th>
                                        <th>KAB/KOTA</th>
                                        <th>NILAI AKHIR</th>
                                        <th>PERINGKAT</th>
                                        <th>STATUS AKREDITASI</th>
                                        <th>TAHUN AKREDITASI</th>
                                        <th>TAHUN AKREDITASI BERAKHIR</th>
                                        <th>AKSI</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Data akan dimuat via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Modal Tambah Data Nilai Akreditasi -->
<div class="modal fade" id="modal-tambah" tabindex="-1" role="dialog" aria-labelledby="modal-tambah-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="modal-tambah-label">
                    <i class="fas fa-plus"></i> Input Nilai Hasil Akreditasi Paud
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-tambah-nilai" novalidate>
                <div class="modal-body">
                    <!-- Form content akan dimuat via loadTambahForm() -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-primary" id="btn-simpan">
                        <i class="fas fa-save"></i> Simpan Data
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Detail Data Nilai Akreditasi -->
<div class="modal fade" id="modal-detail" tabindex="-1" role="dialog" aria-labelledby="modal-detail-label" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="modal-detail-label">
                    <i class="fas fa-eye"></i> Detail Riwayat Nilai Akreditasi
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-detail-content">
                <!-- Konten detail akan dimuat via AJAX -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Nested (muncul di atas modal detail) -->
<div class="modal fade" id="modal-edit-nested" tabindex="-1" role="dialog" aria-labelledby="modal-edit-nested-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="modal-edit-nested-label">
                    <i class="fas fa-edit"></i> Edit Nilai Hasil Akreditasi
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-edit-nilai-nested" novalidate>
                <div class="modal-body" id="modal-edit-nested-content">
                    <!-- Form edit akan dimuat via AJAX -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-warning" id="btn-update-nested">
                        <i class="fas fa-save"></i> Simpan Perubahan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Konfirmasi Delete -->
<div class="modal fade" id="modal-confirm-delete" tabindex="-1" role="dialog" aria-labelledby="modal-confirm-delete-label" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="modal-confirm-delete-label">
                    <i class="fas fa-exclamation-triangle"></i> Konfirmasi Hapus Data
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="fas fa-trash-alt text-danger" style="font-size: 3rem;"></i>
                </div>
                <h6 class="text-center mb-3">Yakin ingin menghapus data nilai akreditasi ini?</h6>
                <div class="alert alert-warning">
                    <div id="delete-info-content">
                        <!-- Info data yang akan dihapus -->
                    </div>
                </div>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <strong>Peringatan:</strong> Data yang dihapus tidak dapat dikembalikan!
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Batal
                </button>
                <button type="button" class="btn btn-danger" id="btn-confirm-delete">
                    <i class="fas fa-trash"></i> Ya, Hapus Permanen
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Notifikasi -->
<div class="modal fade" id="modal-notification" tabindex="-1" role="dialog" aria-labelledby="modal-notification-label" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header" id="modal-notification-header">
                <h5 class="modal-title" id="modal-notification-title">
                    <i id="modal-notification-icon"></i>
                    <span id="modal-notification-text"></span>
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-notification-message">
                <!-- Pesan notifikasi -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>

<style>
    /* Modal nested z-index untuk memastikan modal edit muncul di atas modal detail */
    #modal-edit-nested {
        z-index: 1060;
    }

    #modal-edit-nested .modal-backdrop {
        z-index: 1055;
    }

    /* Alert styling untuk form */
    .alert-sm {
        padding: 0.25rem 0.5rem;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }

    /* Smooth animations untuk modal detail refresh */
    .fade-in {
        animation: fadeIn 0.4s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Loading spinner enhancement */
    .spinner-border {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Success toast styling */
    .success-toast .alert {
        border: none;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border-left: 4px solid #28a745;
    }

    /* Table row - remove hover effects for modal detail */
    .table tbody tr {
        transition: none;
    }

    /* Only apply hover to main table, not modal detail */
    #table-nilai tbody tr:hover {
        background-color: rgba(0,123,255,0.05);
    }

    /* Modal content - remove unnecessary transitions */
    .modal-body {
        transition: none;
    }

    /* Only apply smooth transition to modal fade in/out */
    .modal.fade .modal-dialog {
        transition: transform 0.3s ease-out;
    }

    /* Button hover effects - subtle only */
    .btn {
        transition: background-color 0.2s ease, border-color 0.2s ease;
    }

    /* Remove transform hover effects for modal buttons */
    #modal-detail .btn:hover,
    #modal-edit-nested .btn:hover {
        transform: none;
        box-shadow: none;
    }

    /* Keep subtle hover only for main action buttons */
    #btn-add:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Modal detail specific styling - clean and professional */
    #modal-detail .table {
        margin-bottom: 0;
    }

    #modal-detail .table tbody tr {
        transition: none;
        cursor: default;
    }

    #modal-detail .table tbody tr:hover {
        background-color: transparent !important;
        transform: none !important;
    }

    /* Clean button styling in modal detail */
    #modal-detail .btn-sm {
        transition: opacity 0.2s ease;
    }

    #modal-detail .btn-sm:hover {
        opacity: 0.8;
        transform: none;
        box-shadow: none;
    }

    /* Override any DataTables or Bootstrap hover effects in modal detail */
    #modal-detail .table-striped tbody tr:nth-of-type(odd):hover,
    #modal-detail .table-striped tbody tr:nth-of-type(even):hover,
    #modal-detail .table tbody tr.odd:hover,
    #modal-detail .table tbody tr.even:hover {
        background-color: transparent !important;
    }

    /* Ensure no cursor pointer on table rows in modal detail */
    #modal-detail .table tbody tr td {
        cursor: default !important;
    }

    /* Clean focus states */
    #modal-detail .btn:focus,
    #modal-detail .btn:active {
        box-shadow: none !important;
        outline: none !important;
    }

    /* Modal konfirmasi delete styling */
    #modal-confirm-delete .modal-content {
        border: none;
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }

    #modal-confirm-delete .modal-header {
        border-radius: 10px 10px 0 0;
        border-bottom: none;
    }

    #modal-confirm-delete .modal-footer {
        border-top: none;
        padding-top: 0;
    }

    /* Delete button styling */
    #btn-confirm-delete {
        transition: all 0.2s ease;
    }

    #btn-confirm-delete:hover {
        background-color: #c82333;
        border-color: #bd2130;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    #btn-confirm-delete:disabled {
        transform: none;
        box-shadow: none;
    }
</style>

<!-- Include footer -->
<?php include '../footer.php'; ?>

<!-- JavaScript -->
<script src="js/nilai.js"></script>
