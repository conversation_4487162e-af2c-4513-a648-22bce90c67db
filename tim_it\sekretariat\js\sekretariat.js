/**
 * JavaScript untuk modul Data Sekretariat
 */

$(document).ready(function() {
    // Inisialisasi DataTable
    initDataTable();
    
    // Event handlers
    initEventHandlers();
});

/**
 * Inisialisasi DataTable
 */
function initDataTable() {
    $('#table-sekretariat').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: 'ajax/get_sekretariat.php',
            type: 'POST',
            error: function(xhr, error, thrown) {
                console.error('DataTables error:', error);
                showAlert('error', 'Gagal memuat data sekretariat');
            }
        },
        columns: [
            { 
                data: null,
                orderable: false,
                searchable: false,
                render: function(data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { 
                data: 'nm_staff',
                name: 'nm_staff',
                render: function(data, type, row) {
                    return '<strong>' + data + '</strong>';
                }
            },
            { 
                data: 'jk',
                name: 'jk',
                className: 'text-center'
            },
            { 
                data: 'jabatan',
                name: 'jabatan',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { 
                data: 'nm_kota',
                name: 'nm_kota',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { 
                data: 'unit_kerja',
                name: 'unit_kerja',
                render: function(data, type, row) {
                    if (data && data.length > 30) {
                        return data.substring(0, 30) + '...';
                    }
                    return data || '-';
                }
            },
            {
                data: 'status_keaktifan_id',
                name: 'status_keaktifan_id',
                className: 'text-center',
                render: function(data, type, row) {
                    if (data == '1') {
                        return '<span class="badge badge-success">Aktif</span>';
                    } else if (data == '0') {
                        return '<span class="badge badge-danger">Tidak Aktif</span>';
                    } else if (data == '2') {
                        return '<span class="badge badge-warning">Tidak Diketahui</span>';
                    } else {
                        return '<span class="badge badge-secondary">-</span>';
                    }
                }
            },
            {
                data: null,
                orderable: false,
                searchable: false,
                className: 'text-center',
                render: function(data, type, row) {
                    return '<button class="btn btn-info btn-sm mr-1" onclick="showDetail(' + 
                           row.id_sekretariat + ')" title="Detail">' +
                           '<i class="fas fa-eye"></i></button>' +
                           '<button class="btn btn-warning btn-sm mr-1" onclick="showEditModal(' + 
                           row.id_sekretariat + ')" title="Edit">' +
                           '<i class="fas fa-edit"></i></button>' +
                           '<button class="btn btn-danger btn-sm" onclick="confirmDelete(' + 
                           row.id_sekretariat + ', \'' + row.nm_staff.replace(/'/g, "\\'") + '\', \'' + 
                           row.kd_sekretariat + '\')" title="Hapus">' +
                           '<i class="fas fa-trash"></i></button>';
                }
            }
        ],
        order: [[1, 'asc']],
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: "Memuat data...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(difilter dari _MAX_ total data)",
            loadingRecords: "Memuat data...",
            zeroRecords: "Tidak ada data yang ditemukan",
            emptyTable: "Tidak ada data tersedia",
            paginate: {
                first: "Pertama",
                last: "Terakhir",
                next: "Selanjutnya",
                previous: "Sebelumnya"
            }
        },
        responsive: true,
        autoWidth: false,
        columnDefs: [
            {
                targets: 0, // Kolom NO
                width: '50px'
            },
            {
                targets: 2, // Kolom JK
                width: '80px'
            },
            {
                targets: 6, // Kolom STATUS
                width: '120px'
            },
            {
                targets: 7, // Kolom AKSI
                width: '150px'
            }
        ]
    });
}

/**
 * Inisialisasi event handlers
 */
function initEventHandlers() {
    // Event handler untuk tombol tambah
    $('#btn-add').on('click', function() {
        showTambahModal();
    });

    // Event handler untuk form tambah
    $('#form-tambah-sekretariat').on('submit', function(e) {
        e.preventDefault();
        simpanSekretariat();
    });

    // Event handler untuk form edit
    $('#form-edit-sekretariat').on('submit', function(e) {
        e.preventDefault();
        updateSekretariat();
    });

    // Event handler untuk konfirmasi hapus
    $('#btn-konfirmasi-hapus').on('click', function() {
        var sekretariatId = $(this).data('sekretariat-id');
        var namaSekretariat = $(this).data('nama-sekretariat');
        var kodeSekretariat = $(this).data('kode-sekretariat');

        // Tutup modal konfirmasi
        $('#modal-konfirmasi-hapus').modal('hide');

        // Proses hapus
        deleteSekretariat(sekretariatId, namaSekretariat, kodeSekretariat);
    });
}

/**
 * Fungsi untuk menampilkan modal tambah
 */
function showTambahModal() {
    // Load form tambah
    loadTambahForm();
    
    // Show modal
    $('#modal-tambah').modal('show');
}

/**
 * Fungsi untuk load form tambah
 */
function loadTambahForm() {
    $.ajax({
        url: 'ajax/get_dropdown_options.php',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var formHtml = generateTambahForm(response.data);
                $('#modal-tambah .modal-body').html(formHtml);
                
                // Auto-generate kode sekretariat setelah form dimuat
                generateKodeSekretariat();
            } else {
                $('#modal-tambah .modal-body').html('<div class="alert alert-danger">Gagal memuat form</div>');
            }
        },
        error: function() {
            $('#modal-tambah .modal-body').html('<div class="alert alert-danger">Gagal memuat form</div>');
        }
    });
}

/**
 * Fungsi untuk generate form tambah
 */
function generateTambahForm(dropdownData) {
    var kotaOptions = '<option value="">-- Pilih Kabupaten/Kota --</option>';
    if (dropdownData.kota) {
        dropdownData.kota.forEach(function(item) {
            kotaOptions += '<option value="' + item.kota_id + '">' + item.nm_kota + '</option>';
        });
    }

    return `
        <!-- Hidden field untuk kode sekretariat auto-generate -->
        <input type="hidden" id="kd_sekretariat" name="kd_sekretariat" value="">
        
        <div class="row">
            <!-- Kolom Kiri -->
            <div class="col-md-6">
                <div class="form-group">
                    <label for="nm_staff">Nama Staff <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="nm_staff" name="nm_staff" required maxlength="100">
                </div>
                
                <div class="form-group">
                    <label for="jk">Jenis Kelamin <span class="text-danger">*</span></label>
                    <select class="form-control" id="jk" name="jk" required>
                        <option value="">-- Pilih Jenis Kelamin --</option>
                        <option value="Pria">Pria</option>
                        <option value="Wanita">Wanita</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="ktp">Nomor KTP</label>
                    <input type="text" class="form-control" id="ktp" name="ktp" maxlength="20">
                </div>
                
                <div class="form-group">
                    <label for="tempat_lahir">Tempat Lahir</label>
                    <input type="text" class="form-control" id="tempat_lahir" name="tempat_lahir" maxlength="30">
                </div>
                
                <div class="form-group">
                    <label for="tgl_lahir">Tanggal Lahir</label>
                    <input type="date" class="form-control" id="tgl_lahir" name="tgl_lahir">
                </div>
                
                <div class="form-group">
                    <label for="pendidikan">Pendidikan</label>
                    <input type="text" class="form-control" id="pendidikan" name="pendidikan" maxlength="15">
                </div>
                
                <div class="form-group">
                    <label for="kota_id">Kabupaten/Kota <span class="text-danger">*</span></label>
                    <select class="form-control" id="kota_id" name="kota_id" required>
                        ${kotaOptions}
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="username">Username <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="username" name="username" required maxlength="20">
                    <small class="form-text text-muted">Username untuk login sistem</small>
                </div>
            </div>
            
            <!-- Kolom Kanan -->
            <div class="col-md-6">
                <div class="form-group">
                    <label for="jabatan">Jabatan di BAN SM Provinsi</label>
                    <select name="jabatan" id="jabatan" class="form-control" required>
                        <option value="">...</option>
                        <option value="Staff IT">Staff IT</option>
                        <option value="Tim PADA">Tim PADA</option>
                        <option value="Staff Keuangan">Staff Keuangan</option>
                        <option value="Staff Sekretariat Umum">Staff Sekretariat Umum</option>
                        <option value="Asesor">Asesor</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="unit_kerja">Unit Kerja</label>
                    <textarea class="form-control" id="unit_kerja" name="unit_kerja" rows="2" maxlength="300"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="no_hp">Nomor HP</label>
                    <input type="text" class="form-control" id="no_hp" name="no_hp" maxlength="20">
                </div>
                
                <div class="form-group">
                    <label for="no_wa">Nomor WhatsApp</label>
                    <input type="text" class="form-control" id="no_wa" name="no_wa" maxlength="15">
                </div>
                
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="text" class="form-control" id="email" name="email" maxlength="50">
                </div>
                
                <div class="form-group">
                    <label for="status_keaktifan_id">Status Keaktifan <span class="text-danger">*</span></label>
                    <select class="form-control" id="status_keaktifan_id" name="status_keaktifan_id" required>
                        <option value="">-- Pilih Status --</option>
                        <option value="1">Aktif</option>
                        <option value="0">Tidak Aktif</option>
                        <option value="2">Tidak Diketahui</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="no_urut">Nomor Urut</label>
                    <input type="number" class="form-control" id="no_urut" name="no_urut">
                </div>
                
                <div class="form-group">
                    <label for="password">Password <span class="text-danger">*</span></label>
                    <input type="password" class="form-control" id="password" name="password" required maxlength="50">
                    <small class="form-text text-muted">Password untuk login sistem</small>
                </div>
            </div>
        </div>
        
        <!-- Alamat (Full Width) -->
        <div class="row">
            <div class="col-12">
                <div class="form-group">
                    <label for="alamat_kantor">Alamat Kantor</label>
                    <textarea class="form-control" id="alamat_kantor" name="alamat_kantor" rows="2" maxlength="300"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="alamat_rumah">Alamat Rumah</label>
                    <textarea class="form-control" id="alamat_rumah" name="alamat_rumah" rows="2" maxlength="300"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="sebab">Sebab/Keterangan</label>
                    <textarea class="form-control" id="sebab" name="sebab" rows="2"></textarea>
                </div>
            </div>
        </div>
    `;
}

/**
 * Fungsi untuk menampilkan modal edit
 */
function showEditModal(sekretariatId) {
    // Show loading
    $('#modal-edit-content').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Memuat data...</div>');
    $('#modal-edit').modal('show');

    // Load form edit via AJAX
    $.ajax({
        url: 'ajax/get_edit_form.php',
        type: 'POST',
        data: { id_sekretariat: sekretariatId },
        dataType: 'html',
        success: function(response) {
            $('#modal-edit-content').html(response);
        },
        error: function(xhr, status, error) {
            $('#modal-edit-content').html('<div class="alert alert-danger">Gagal memuat form edit</div>');
        }
    });
}

/**
 * Fungsi untuk menampilkan detail
 */
function showDetail(sekretariatId) {
    // Show loading
    $('#modal-detail-content').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Memuat data...</div>');
    $('#modal-detail').modal('show');

    // Load detail via AJAX
    $.ajax({
        url: 'ajax/get_detail_sekretariat.php',
        type: 'POST',
        data: { id_sekretariat: sekretariatId },
        dataType: 'html',
        success: function(response) {
            $('#modal-detail-content').html(response);
        },
        error: function(xhr, status, error) {
            $('#modal-detail-content').html('<div class="alert alert-danger">Gagal memuat detail data</div>');
        }
    });
}

/**
 * Fungsi untuk konfirmasi hapus
 */
function confirmDelete(sekretariatId, namaSekretariat, kodeSekretariat) {
    // Set data ke modal
    $('#hapus-nama-sekretariat').text(namaSekretariat);
    $('#hapus-kode-sekretariat').text(kodeSekretariat);

    // Set data ke tombol konfirmasi
    $('#btn-konfirmasi-hapus').data('sekretariat-id', sekretariatId);
    $('#btn-konfirmasi-hapus').data('nama-sekretariat', namaSekretariat);
    $('#btn-konfirmasi-hapus').data('kode-sekretariat', kodeSekretariat);

    // Tampilkan modal konfirmasi
    $('#modal-konfirmasi-hapus').modal('show');
}

/**
 * Fungsi untuk simpan data sekretariat
 */
function simpanSekretariat() {
    // Disable tombol submit
    $('#btn-simpan').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    // Serialize form data
    var formData = $('#form-tambah-sekretariat').serialize();

    $.ajax({
        url: 'ajax/simpan_sekretariat.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Tutup modal
                $('#modal-tambah').modal('hide');

                // Reload DataTable
                $('#table-sekretariat').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat menyimpan data');
        },
        complete: function() {
            // Enable tombol submit
            $('#btn-simpan').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Data');
        }
    });
}

/**
 * Fungsi untuk update data sekretariat
 */
function updateSekretariat() {
    // Disable tombol submit
    $('#btn-update').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    // Serialize form data
    var formData = $('#form-edit-sekretariat').serialize();

    $.ajax({
        url: 'ajax/update_sekretariat.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Tutup modal
                $('#modal-edit').modal('hide');

                // Reload DataTable
                $('#table-sekretariat').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat menyimpan data');
        },
        complete: function() {
            // Enable tombol submit
            $('#btn-update').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Perubahan');
        }
    });
}

/**
 * Fungsi untuk hapus data sekretariat
 */
function deleteSekretariat(sekretariatId, namaSekretariat, kodeSekretariat) {
    $.ajax({
        url: 'ajax/hapus_sekretariat.php',
        type: 'POST',
        data: {
            id_sekretariat: sekretariatId,
            nm_staff: namaSekretariat,
            kd_sekretariat: kodeSekretariat
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Reload DataTable
                $('#table-sekretariat').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat menghapus data');
        }
    });
}

/**
 * Fungsi untuk generate kode sekretariat otomatis
 */
function generateKodeSekretariat() {
    // Generate unique ID menggunakan timestamp dan random
    var timestamp = Date.now();
    var random = Math.floor(Math.random() * 1000);
    var kodeSekretariat = 'SKR' + timestamp + random;

    // Set value ke hidden input
    $('#kd_sekretariat').val(kodeSekretariat);
}

/**
 * Fungsi untuk menampilkan alert
 */
function showAlert(type, message) {
    var alertClass = 'alert-info';
    var icon = 'fas fa-info-circle';
    var title = 'Informasi';

    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            icon = 'fas fa-check-circle';
            title = 'Berhasil';
            break;
        case 'error':
            alertClass = 'alert-danger';
            icon = 'fas fa-exclamation-circle';
            title = 'Error';
            break;
        case 'warning':
            alertClass = 'alert-warning';
            icon = 'fas fa-exclamation-triangle';
            title = 'Peringatan';
            break;
    }

    // Update modal notification
    $('#modal-notification-header').removeClass().addClass('modal-header ' + alertClass);
    $('#modal-notification-icon').removeClass().addClass(icon);
    $('#modal-notification-text').text(title);
    $('#modal-notification-message').text(message);

    // Show modal
    $('#modal-notification').modal('show');

    // Auto hide after 3 seconds for success messages
    if (type === 'success') {
        setTimeout(function() {
            $('#modal-notification').modal('hide');
        }, 3000);
    }
}
