<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi required fields (tgl_penetapan_kpa adalah opsional)
    $required_fields = ['sekolah_id', 'kd_asesor', 'tahun_akreditasi', 'tahap', 'provinsi_id'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            $field_names = [
                'sekolah_id' => 'Sekolah',
                'kd_asesor' => 'Asesor',
                'tahun_akreditasi' => 'Tahun Akreditasi',
                'tahap' => 'Tahap',
                'provinsi_id' => 'Provinsi'
            ];
            throw new Exception("Field " . $field_names[$field] . " harus diisi");
        }
    }
    
    // Sanitasi input
    $sekolah_id = intval($_POST['sekolah_id']);
    $kd_asesor = $conn->real_escape_string(trim($_POST['kd_asesor']));

    // Tanggal penetapan KPA adalah opsional - gunakan '0000-00-00' jika kosong
    $tgl_penetapan_kpa = isset($_POST['tgl_penetapan_kpa']) && !empty(trim($_POST['tgl_penetapan_kpa']))
                         ? $conn->real_escape_string(trim($_POST['tgl_penetapan_kpa']))
                         : '0000-00-00';

    $tahun_akreditasi = $conn->real_escape_string(trim($_POST['tahun_akreditasi']));
    $tahap = intval($_POST['tahap']);
    $provinsi_id = intval($_POST['provinsi_id']);
    
    // Validasi session provinsi
    if ($provinsi_id !== $_SESSION['provinsi_id']) {
        throw new Exception('Provinsi tidak valid');
    }
    
    // Validasi format tahun
    if (!preg_match('/^\d{4}$/', $tahun_akreditasi)) {
        throw new Exception('Format tahun akreditasi tidak valid');
    }
    
    // Validasi tahap
    if ($tahap < 1 || $tahap > 3) {
        throw new Exception('Tahap harus antara 1-3');
    }
    
    // Debug: Log values
    error_log("Insert Mapping KPA - Sekolah: $sekolah_id, Asesor: $kd_asesor, Tahun: $tahun_akreditasi, Tahap: $tahap");
    
    // Cek duplikasi mapping (sekolah + tahun yang sama)
    $check_query = "SELECT id_mapping FROM mapping_paud_kpa 
                    WHERE sekolah_id = ? AND tahun_akreditasi = ? AND provinsi_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("isi", $sekolah_id, $tahun_akreditasi, $provinsi_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows > 0) {
        throw new Exception('Mapping untuk sekolah dan tahun akreditasi ini sudah ada');
    }
    
    // Validasi apakah sekolah exists dan aktif
    $sekolah_query = "SELECT sekolah_id, nama_sekolah FROM sekolah 
                      WHERE sekolah_id = ? AND provinsi_id = ? AND rumpun = 'paud' 
                        AND soft_delete = '1' AND status_keaktifan_id = '1'";
    $sekolah_stmt = $conn->prepare($sekolah_query);
    $sekolah_stmt->bind_param("ii", $sekolah_id, $provinsi_id);
    $sekolah_stmt->execute();
    $sekolah_result = $sekolah_stmt->get_result();
    
    if ($sekolah_result->num_rows === 0) {
        throw new Exception('Data sekolah tidak ditemukan atau tidak aktif');
    }
    $sekolah_data = $sekolah_result->fetch_assoc();
    
    // Validasi apakah asesor exists dan aktif (semua rumpun: paud, dasmen, kesetaraan)
    $asesor_query = "SELECT kd_asesor, nm_asesor, rumpun FROM asesor
                     WHERE kd_asesor = ? AND provinsi_id = ?
                       AND soft_delete = '1' AND status_keaktifan_id = '1'";
    $asesor_stmt = $conn->prepare($asesor_query);
    $asesor_stmt->bind_param("si", $kd_asesor, $provinsi_id);
    $asesor_stmt->execute();
    $asesor_result = $asesor_stmt->get_result();
    
    if ($asesor_result->num_rows === 0) {
        throw new Exception('Data asesor tidak ditemukan atau tidak aktif');
    }
    $asesor_data = $asesor_result->fetch_assoc();
    
    // Begin transaction
    $conn->autocommit(false);

    // Insert mapping KPA (tanggal kosong akan jadi '0000-00-00')
    $insert_query = "INSERT INTO mapping_paud_kpa
                     (sekolah_id, kd_asesor, tgl_penetapan_kpa, tahap, tahun_akreditasi, provinsi_id)
                     VALUES (?, ?, ?, ?, ?, ?)";
    $insert_stmt = $conn->prepare($insert_query);
    $insert_stmt->bind_param("issisi", $sekolah_id, $kd_asesor, $tgl_penetapan_kpa, $tahap, $tahun_akreditasi, $provinsi_id);
    
    if (!$insert_stmt->execute()) {
        throw new Exception('Gagal menyimpan data mapping KPA: ' . $conn->error);
    }
    
    $new_mapping_id = $conn->insert_id;
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Log successful insertion
    error_log("Insert Mapping KPA Success - ID: $new_mapping_id, Sekolah: " . $sekolah_data['nama_sekolah'] . ", Asesor: " . $asesor_data['nm_asesor'] . ", User: " . $_SESSION['nm_user']);
    
    echo json_encode([
        'success' => true,
        'message' => 'Data mapping KPA berhasil disimpan',
        'data' => [
            'id_mapping' => $new_mapping_id,
            'sekolah_id' => $sekolah_id,
            'nama_sekolah' => $sekolah_data['nama_sekolah'],
            'kd_asesor' => $kd_asesor,
            'nm_asesor' => $asesor_data['nm_asesor'],
            'tgl_penetapan_kpa' => $tgl_penetapan_kpa,
            'tahun_akreditasi' => $tahun_akreditasi,
            'tahap' => $tahap
        ]
    ]);
    
} catch (Exception $e) {
    // Rollback transaction
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Insert Mapping KPA Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
