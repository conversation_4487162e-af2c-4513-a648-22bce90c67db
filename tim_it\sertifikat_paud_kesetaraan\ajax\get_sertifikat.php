<?php
// Debug: Log access
error_log("get_sertifikat.php accessed at " . date('Y-m-d H:i:s'));

session_start();
require_once '../../../koneksi.php';

// Cek session dan level
if (!isset($_SESSION['kd_user']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

// Debug: Log POST data
error_log("POST data: " . print_r($_POST, true));

// Ambil provinsi_id dari session
$provinsi_id = $_SESSION['provinsi_id'];

// DataTables parameters
$draw = isset($_POST['draw']) ? intval($_POST['draw']) : 1;
$start = isset($_POST['start']) ? intval($_POST['start']) : 0;
$length = isset($_POST['length']) ? intval($_POST['length']) : 25;
$search_value = isset($_POST['search']['value']) ? $_POST['search']['value'] : '';

// Debug: Log parameters
error_log("DataTables params - draw: $draw, start: $start, length: $length, search: $search_value");

// Column mapping for ordering
$columns = [
    0 => 'sertifikat_id', // NO (not orderable)
    1 => 'sk.npsn',
    2 => 'sk.nama_sekolah',
    3 => 'j.nm_jenjang',
    4 => 'kk.nm_kota',
    5 => 'sp.tahun_akreditasi',
    6 => 'sertifikat_id' // AKSI (not orderable)
];

$order_column_index = isset($_POST['order'][0]['column']) ? intval($_POST['order'][0]['column']) : 5;
$order_column = isset($columns[$order_column_index]) ? $columns[$order_column_index] : 'sp.tahun_akreditasi';
$order_dir = isset($_POST['order'][0]['dir']) ? $_POST['order'][0]['dir'] : 'desc';

// Debug: Log order
error_log("Order: column=$order_column, dir=$order_dir");

try {
    // Cek koneksi database
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Debug: Log provinsi_id
    error_log("Provinsi ID: $provinsi_id");

    // Base query dengan join ke tabel terkait
    $base_query = "FROM sertifikat_paud sp
                   LEFT JOIN sekolah sk ON sp.sekolah_id = sk.sekolah_id
                   LEFT JOIN jenjang j ON sk.jenjang_id = j.jenjang_id
                   LEFT JOIN kab_kota kk ON sk.kota_id = kk.kota_id
                   WHERE sp.provinsi_id = $provinsi_id
                     AND sk.rumpun IN ('paud', 'kesetaraan')
                     AND sk.soft_delete = '1'";

    // Debug: Log base query
    error_log("Base query: SELECT COUNT(*) as total $base_query");

    // Add search condition
    $search_query = "";
    if (!empty($search_value)) {
        $search_value = $conn->real_escape_string($search_value);
        $search_query = " AND (sk.npsn LIKE '%$search_value%'
                             OR sk.nama_sekolah LIKE '%$search_value%'
                             OR j.nm_jenjang LIKE '%$search_value%'
                             OR kk.nm_kota LIKE '%$search_value%'
                             OR sp.tahun_akreditasi LIKE '%$search_value%')";
    }

    // Count total records
    $total_query = "SELECT COUNT(*) as total $base_query";
    $total_result = $conn->query($total_query);
    if (!$total_result) {
        throw new Exception("Total query failed: " . $conn->error);
    }
    $total_records = $total_result->fetch_assoc()['total'];

    // Count filtered records
    $filtered_query = "SELECT COUNT(*) as total $base_query $search_query";
    $filtered_result = $conn->query($filtered_query);
    if (!$filtered_result) {
        throw new Exception("Filtered query failed: " . $conn->error);
    }
    $filtered_records = $filtered_result->fetch_assoc()['total'];

    // Get data with pagination
    $data_query = "SELECT sp.sertifikat_id,
                          sp.sekolah_id,
                          sp.tahun_akreditasi,
                          sp.nama_file,
                          sk.npsn,
                          sk.nama_sekolah,
                          j.nm_jenjang,
                          kk.nm_kota
                   $base_query
                   $search_query
                   ORDER BY $order_column $order_dir
                   LIMIT $start, $length";

    $data_result = $conn->query($data_query);
    if (!$data_result) {
        throw new Exception("Data query failed: " . $conn->error);
    }

    $data = [];
    if ($data_result->num_rows > 0) {
        while ($row = $data_result->fetch_assoc()) {
            $data[] = [
                'sertifikat_id' => $row['sertifikat_id'],
                'sekolah_id' => $row['sekolah_id'],
                'tahun_akreditasi' => $row['tahun_akreditasi'],
                'nama_file' => $row['nama_file'],
                'npsn' => $row['npsn'],
                'nama_sekolah' => $row['nama_sekolah'],
                'nm_jenjang' => $row['nm_jenjang'] ?: '-',
                'nm_kab_kota' => $row['nm_kota'] ?: '-'
            ];
        }
    }

    // Response for DataTables
    $response = [
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $filtered_records,
        'data' => $data
    ];

    // Debug: Log response
    error_log("Response data count: " . count($data));

    // Set proper content type
    header('Content-Type: application/json');
    echo json_encode($response);

} catch (Exception $e) {
    error_log("Get Sertifikat Error: " . $e->getMessage());
    header('Content-Type: application/json');
    echo json_encode([
        'draw' => $draw,
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => 'Terjadi kesalahan saat memuat data: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
