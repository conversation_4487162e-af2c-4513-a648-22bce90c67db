<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi parameter
    if (!isset($_GET['npsn']) || empty(trim($_GET['npsn']))) {
        throw new Exception('NPSN harus diisi');
    }
    
    $npsn = $conn->real_escape_string(trim($_GET['npsn']));
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Query untuk mencari sekolah berdasarkan NPSN
    $query = "SELECT s.sekolah_id, s.npsn, s.nama_sekolah, s.jenjang_id,
                     j.nm_jenjang,
                     kk.nm_kota
              FROM sekolah s
              LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
              LEFT JOIN kab_kota kk ON s.kota_id = kk.kota_id
              WHERE s.npsn = ? 
                AND s.provinsi_id = ?
                AND s.rumpun = 'paud'
                AND s.soft_delete = '1'
                AND s.status_keaktifan_id = '1'";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("si", $npsn, $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $data = $result->fetch_assoc();
        
        echo json_encode([
            'success' => true,
            'message' => 'Sekolah ditemukan',
            'data' => $data
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'NPSN tidak ditemukan atau sekolah tidak aktif'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Lookup NPSN Error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
