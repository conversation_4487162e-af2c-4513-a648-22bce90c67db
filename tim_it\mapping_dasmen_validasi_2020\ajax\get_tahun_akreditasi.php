<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Ambil provinsi_id dari session
    $provinsi_id = $_SESSION['provinsi_id'];

    // Debug: Log provinsi_id
    error_log("Get Tahun Akreditasi - Provinsi ID: " . $provinsi_id);

    // Query untuk mengambil data tahun akreditasi berdasarkan provinsi_id
    $query = "SELECT id_mapping_validasi_tahun, nama_tahun, provinsi_id
              FROM mapping_validasi_tahun
              WHERE provinsi_id = ?";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();

    // Debug: Log query result
    error_log("Get Tahun Akreditasi - Rows found: " . $result->num_rows);
    
    if ($result->num_rows > 0) {
        $data = $result->fetch_assoc();
        
        echo json_encode([
            'success' => true,
            'data' => [
                'id_mapping_validasi_tahun' => $data['id_mapping_validasi_tahun'],
                'nama_tahun' => $data['nama_tahun'],
                'provinsi_id' => $data['provinsi_id']
            ]
        ]);
    } else {
        // Jika belum ada data, return data kosong untuk insert baru
        echo json_encode([
            'success' => true,
            'data' => [
                'id_mapping_validasi_tahun' => null,
                'nama_tahun' => '',
                'provinsi_id' => $provinsi_id
            ]
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
