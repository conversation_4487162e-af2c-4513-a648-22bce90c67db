<?php
session_start();
require_once '../../../koneksi.php';

// Cek session dan level
if (!isset($_SESSION['kd_user']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

header('Content-Type: application/json');

try {
    // Validasi parameter
    if (!isset($_POST['id_mapping_validasi']) || empty($_POST['id_mapping_validasi'])) {
        throw new Exception('ID mapping validasi tidak valid');
    }
    
    if (!isset($_POST['tgl_mulai_validasi']) || !isset($_POST['tgl_akhir_validasi'])) {
        throw new Exception('Data tanggal validasi tidak lengkap');
    }
    
    $id_mapping_validasi = intval($_POST['id_mapping_validasi']);
    $tgl_mulai_validasi = $_POST['tgl_mulai_validasi'];
    $tgl_akhir_validasi = $_POST['tgl_akhir_validasi'];
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Validasi format tanggal
    if (!empty($tgl_mulai_validasi) && !validateDate($tgl_mulai_validasi)) {
        throw new Exception('Format tanggal mulai validasi tidak valid');
    }
    
    if (!empty($tgl_akhir_validasi) && !validateDate($tgl_akhir_validasi)) {
        throw new Exception('Format tanggal akhir validasi tidak valid');
    }
    
    // Validasi logika tanggal (akhir harus >= mulai)
    if (!empty($tgl_mulai_validasi) && !empty($tgl_akhir_validasi)) {
        if (strtotime($tgl_akhir_validasi) < strtotime($tgl_mulai_validasi)) {
            throw new Exception('Tanggal akhir validasi tidak boleh lebih kecil dari tanggal mulai');
        }
    }
    
    // Cek apakah mapping validasi exists dan milik provinsi yang benar
    $check_query = "SELECT id_mapping_validasi FROM mapping_validasi 
                    WHERE id_mapping_validasi = ? AND provinsi_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("ii", $id_mapping_validasi, $provinsi_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows === 0) {
        throw new Exception('Data mapping validasi tidak ditemukan atau tidak memiliki akses');
    }
    
    // Prepare nilai untuk update (handle empty dates)
    $tgl_mulai_update = !empty($tgl_mulai_validasi) ? $tgl_mulai_validasi : null;
    $tgl_akhir_update = !empty($tgl_akhir_validasi) ? $tgl_akhir_validasi : null;
    
    // Update tanggal validasi
    $update_query = "UPDATE mapping_validasi 
                     SET tgl_mulai_validasi = ?, 
                         tgl_akhir_validasi = ?
                     WHERE id_mapping_validasi = ? AND provinsi_id = ?";
    
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("ssii", $tgl_mulai_update, $tgl_akhir_update, $id_mapping_validasi, $provinsi_id);
    
    if (!$update_stmt->execute()) {
        throw new Exception('Gagal mengupdate tanggal validasi: ' . $conn->error);
    }
    
    if ($update_stmt->affected_rows === 0) {
        throw new Exception('Tidak ada data yang diupdate. Mungkin data tidak berubah.');
    }
    
    // Format tanggal untuk response
    function formatTanggalResponse($tanggal) {
        if (!$tanggal || $tanggal === '0000-00-00') {
            return '-';
        }
        return date('d/m/Y', strtotime($tanggal));
    }
    
    // Response success dengan data yang sudah diformat
    echo json_encode([
        'success' => true,
        'message' => 'Tanggal validasi berhasil diupdate',
        'data' => [
            'id_mapping_validasi' => $id_mapping_validasi,
            'tgl_mulai_validasi' => formatTanggalResponse($tgl_mulai_update),
            'tgl_akhir_validasi' => formatTanggalResponse($tgl_akhir_update),
            'tgl_mulai_validasi_raw' => $tgl_mulai_update,
            'tgl_akhir_validasi_raw' => $tgl_akhir_update
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Update Tanggal Validasi Error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// Function untuk validasi format tanggal
function validateDate($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

$conn->close();
?>
