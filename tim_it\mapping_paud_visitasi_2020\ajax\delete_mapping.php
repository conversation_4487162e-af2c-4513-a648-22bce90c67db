<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input ID mapping
    if (!isset($_POST['id_mapping']) || empty(trim($_POST['id_mapping']))) {
        throw new Exception('ID mapping harus diisi');
    }
    
    $id_mapping = intval($_POST['id_mapping']);
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Validasi ID mapping
    if ($id_mapping <= 0) {
        throw new Exception('ID mapping tidak valid');
    }
    
    // Cek apakah mapping exists dan milik provinsi yang benar
    $check_query = "SELECT 
                        mp.id_mapping, mp.sekolah_id, mp.tahun_a<PERSON>,
                        s.npsn, s.nama_sekolah,
                        a1.nm_asesor1, a2.nm_asesor2
                    FROM mapping_paud_visitasi mp
                    LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
                    LEFT JOIN asesor_1 a1 ON mp.kd_asesor1 = a1.kd_asesor1
                    LEFT JOIN asesor_2 a2 ON mp.kd_asesor2 = a2.kd_asesor2
                    WHERE mp.id_mapping = ? AND mp.provinsi_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("ii", $id_mapping, $provinsi_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows === 0) {
        throw new Exception('Data mapping tidak ditemukan atau Anda tidak memiliki akses');
    }
    
    $mapping_info = $check_result->fetch_assoc();
    
    // Begin transaction
    $conn->autocommit(false);
    
    // Hard delete mapping (sesuai permintaan user)
    $delete_query = "DELETE FROM mapping_paud_visitasi 
                     WHERE id_mapping = ? AND provinsi_id = ?";
    
    $delete_stmt = $conn->prepare($delete_query);
    $delete_stmt->bind_param("ii", $id_mapping, $provinsi_id);
    
    if (!$delete_stmt->execute()) {
        throw new Exception('Gagal menghapus data mapping: ' . $conn->error);
    }
    
    // Check if any rows were affected
    if ($conn->affected_rows === 0) {
        throw new Exception('Data mapping tidak ditemukan atau sudah dihapus');
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Log successful deletion
    error_log("Delete Mapping Success - ID: $id_mapping, " .
              "NPSN: " . ($mapping_info['npsn'] ?: 'NULL') . ", " .
              "Sekolah: " . ($mapping_info['nama_sekolah'] ?: 'NULL') . ", " .
              "Asesor1: " . ($mapping_info['nm_asesor1'] ?: 'NULL') . ", " .
              "Asesor2: " . ($mapping_info['nm_asesor2'] ?: 'NULL') . ", " .
              "User: " . $_SESSION['nm_user']);
    
    echo json_encode([
        'success' => true,
        'message' => 'Data mapping visitasi berhasil dihapus',
        'deleted_data' => [
            'id_mapping' => $id_mapping,
            'npsn' => $mapping_info['npsn'],
            'nama_sekolah' => $mapping_info['nama_sekolah'],
            'nm_asesor1' => $mapping_info['nm_asesor1'],
            'nm_asesor2' => $mapping_info['nm_asesor2']
        ]
    ]);
    
} catch (Exception $e) {
    // Rollback transaction
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Delete Mapping Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
