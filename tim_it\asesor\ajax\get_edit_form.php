<?php
/**
 * AJAX handler untuk mendapatkan form edit asesor dengan data yang sudah ter-populate
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input
    if (!isset($_POST['id_asesor']) || empty($_POST['id_asesor'])) {
        throw new Exception('ID asesor harus diisi');
    }
    
    $id_asesor = intval($_POST['id_asesor']);
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Query untuk mendapatkan data asesor
    $sql = "SELECT * FROM asesor WHERE id_asesor = ? AND provinsi_id = ? AND soft_delete = '1'";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $id_asesor, $provinsi_id_session);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        throw new Exception('Data asesor tidak ditemukan');
    }
    
    $row = $result->fetch_assoc();
    
    // Query untuk mendapatkan dropdown kab/kota
    $kota_query = "SELECT kota_id, nm_kota FROM kab_kota WHERE provinsi_id = ? ORDER BY nm_kota ASC";
    $kota_stmt = $conn->prepare($kota_query);
    $kota_stmt->bind_param("i", $provinsi_id_session);
    $kota_stmt->execute();
    $kota_result = $kota_stmt->get_result();
    
    $kota_options = '<option value="">-- Pilih Kabupaten/Kota --</option>';
    while ($kota_row = $kota_result->fetch_assoc()) {
        $selected = ($kota_row['kota_id'] == $row['kota_id']) ? 'selected' : '';
        $kota_options .= '<option value="' . $kota_row['kota_id'] . '" ' . $selected . '>' . $kota_row['nm_kota'] . '</option>';
    }
    
    // Generate form HTML dengan data yang sudah ter-populate
    ob_start();
?>
<div class="modal-header bg-success">
    <h5 class="modal-title">
        <i class="fas fa-edit"></i> Edit Data Asesor
    </h5>
    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<form id="form-edit-asesor" novalidate>
    <div class="modal-body">
        <div class="row">
            <!-- Kolom Kiri -->
            <div class="col-md-6">
                <h6 class="text-primary"><i class="fas fa-user"></i> Data Pribadi</h6>
                
                <div class="form-group">
                    <label for="edit_nia">NIA <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="edit_nia" name="nia" 
                           value="<?php echo htmlspecialchars($row['nia']); ?>" required maxlength="20">
                </div>
                
                <div class="form-group">
                    <label for="edit_nm_asesor">Nama Asesor <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="edit_nm_asesor" name="nm_asesor" 
                           value="<?php echo htmlspecialchars($row['nm_asesor']); ?>" required maxlength="100">
                </div>
                
                <div class="form-group">
                    <label for="edit_jk">Jenis Kelamin <span class="text-danger">*</span></label>
                    <select class="form-control" id="edit_jk" name="jk" required>
                        <option value="">-- Pilih Jenis Kelamin --</option>
                        <option value="Pria" <?php echo ($row['jk'] == 'Pria') ? 'selected' : ''; ?>>Pria</option>
                        <option value="Wanita" <?php echo ($row['jk'] == 'Wanita') ? 'selected' : ''; ?>>Wanita</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="edit_kota_id">Kabupaten/Kota <span class="text-danger">*</span></label>
                    <select class="form-control" id="edit_kota_id" name="kota_id" required>
                        <?php echo $kota_options; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="edit_rumpun">Rumpun <span class="text-danger">*</span></label>
                    <select class="form-control" id="edit_rumpun" name="rumpun" required>
                        <option value="">-- Pilih Rumpun --</option>
                        <option value="dasmen" <?php echo ($row['rumpun'] == 'dasmen') ? 'selected' : ''; ?>>Dasmen</option>
                        <option value="paud" <?php echo ($row['rumpun'] == 'paud') ? 'selected' : ''; ?>>PAUD</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="edit_jenjang_id">Jenjang</label>
                    <select class="form-control" id="edit_jenjang_id" name="jenjang_id">
                        <option value="">-- Pilih Jenjang --</option>
                        <?php
                        // Query untuk mendapatkan jenjang
                        $jenjang_query = "SELECT jenjang_id, nm_jenjang FROM jenjang ORDER BY nm_jenjang ASC";
                        $jenjang_result = $conn->query($jenjang_query);
                        while ($jenjang_row = $jenjang_result->fetch_assoc()) {
                            $selected = ($jenjang_row['jenjang_id'] == $row['jenjang_id']) ? 'selected' : '';
                            echo '<option value="' . $jenjang_row['jenjang_id'] . '" ' . $selected . '>' . $jenjang_row['nm_jenjang'] . '</option>';
                        }
                        ?>
                    </select>
                </div>

                <div class="form-group">
                    <label for="edit_kd_asesor">Kode Asesor</label>
                    <input type="text" class="form-control" id="edit_kd_asesor" name="kd_asesor"
                           value="<?php echo htmlspecialchars($row['kd_asesor']); ?>" maxlength="25" readonly>
                </div>
                
                <div class="form-group">
                    <label for="edit_ktp">No. KTP</label>
                    <input type="text" class="form-control" id="edit_ktp" name="ktp" 
                           value="<?php echo htmlspecialchars($row['ktp']); ?>" maxlength="20">
                </div>
                
                <div class="form-group">
                    <label for="edit_tempat_lahir">Tempat Lahir</label>
                    <input type="text" class="form-control" id="edit_tempat_lahir" name="tempat_lahir" 
                           value="<?php echo htmlspecialchars($row['tempat_lahir']); ?>" maxlength="30">
                </div>
                
                <div class="form-group">
                    <label for="edit_tgl_lahir">Tanggal Lahir</label>
                    <input type="date" class="form-control" id="edit_tgl_lahir" name="tgl_lahir" 
                           value="<?php echo $row['tgl_lahir']; ?>">
                </div>
            </div>
            
            <!-- Kolom Kanan -->
            <div class="col-md-6">
                <h6 class="text-primary"><i class="fas fa-briefcase"></i> Data Pekerjaan</h6>
                
                <div class="form-group">
                    <label for="edit_unit_kerja">Unit Kerja</label>
                    <textarea class="form-control" id="edit_unit_kerja" name="unit_kerja" rows="2" maxlength="300"><?php echo htmlspecialchars($row['unit_kerja']); ?></textarea>
                </div>
                
                <div class="form-group">
                    <label for="edit_jabatan">Jabatan</label>
                    <input type="text" class="form-control" id="edit_jabatan" name="jabatan" 
                           value="<?php echo htmlspecialchars($row['jabatan']); ?>" maxlength="50">
                </div>
                
                <div class="form-group">
                    <label for="edit_jabatan_struktural">Jabatan Struktural</label>
                    <input type="text" class="form-control" id="edit_jabatan_struktural" name="jabatan_struktural" 
                           value="<?php echo htmlspecialchars($row['jabatan_struktural']); ?>" maxlength="20">
                </div>
                
                <div class="form-group">
                    <label for="edit_pendidikan">Pendidikan</label>
                    <input type="text" class="form-control" id="edit_pendidikan" name="pendidikan" 
                           value="<?php echo htmlspecialchars($row['pendidikan']); ?>" maxlength="15">
                </div>
                
                <div class="form-group">
                    <label for="edit_grade">Grade</label>
                    <select class="form-control" id="edit_grade" name="grade">
                        <option value="">-- Pilih Grade --</option>
                        <option value="A" <?php echo ($row['grade'] == 'A') ? 'selected' : ''; ?>>A</option>
                        <option value="B" <?php echo ($row['grade'] == 'B') ? 'selected' : ''; ?>>B</option>
                        <option value="C" <?php echo ($row['grade'] == 'C') ? 'selected' : ''; ?>>C</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="edit_no_sertifikat">No. Sertifikat</label>
                    <input type="text" class="form-control" id="edit_no_sertifikat" name="no_sertifikat" 
                           value="<?php echo htmlspecialchars($row['no_sertifikat']); ?>" maxlength="30">
                </div>
                
                <div class="form-group">
                    <label for="edit_thn_terbit_sertifikat">Tahun Terbit Sertifikat</label>
                    <input type="date" class="form-control" id="edit_thn_terbit_sertifikat" name="thn_terbit_sertifikat" 
                           value="<?php echo $row['thn_terbit_sertifikat']; ?>">
                </div>
                
                <div class="form-group">
                    <label for="edit_status_keaktifan_id">Status Keaktifan <span class="text-danger">*</span></label>
                    <select class="form-control" id="edit_status_keaktifan_id" name="status_keaktifan_id" required>
                        <option value="">-- Pilih Status --</option>
                        <option value="1" <?php echo ($row['status_keaktifan_id'] == '1') ? 'selected' : ''; ?>>Aktif</option>
                        <option value="0" <?php echo ($row['status_keaktifan_id'] == '0') ? 'selected' : ''; ?>>Tidak Aktif</option>
                        <option value="2" <?php echo ($row['status_keaktifan_id'] == '2') ? 'selected' : ''; ?>>Tidak Diketahui</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- Baris Bawah - Kontak & Alamat -->
        <div class="row">
            <div class="col-md-12">
                <h6 class="text-primary"><i class="fas fa-address-book"></i> Kontak & Alamat</h6>
            </div>
            
            <div class="col-md-6">
                <div class="form-group">
                    <label for="edit_no_hp">No. HP</label>
                    <input type="text" class="form-control" id="edit_no_hp" name="no_hp" 
                           value="<?php echo htmlspecialchars($row['no_hp']); ?>" maxlength="50">
                </div>
                
                <div class="form-group">
                    <label for="edit_no_wa">No. WhatsApp</label>
                    <input type="text" class="form-control" id="edit_no_wa" name="no_wa" 
                           value="<?php echo htmlspecialchars($row['no_wa']); ?>" maxlength="50">
                </div>
                
                <div class="form-group">
                    <label for="edit_email">Email</label>
                    <input type="email" class="form-control" id="edit_email" name="email" 
                           value="<?php echo htmlspecialchars($row['email']); ?>" maxlength="50">
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="form-group">
                    <label for="edit_alamat_kantor">Alamat Kantor</label>
                    <textarea class="form-control" id="edit_alamat_kantor" name="alamat_kantor" rows="2" maxlength="300"><?php echo htmlspecialchars($row['alamat_kantor']); ?></textarea>
                </div>
                
                <div class="form-group">
                    <label for="edit_alamat_rumah">Alamat Rumah</label>
                    <textarea class="form-control" id="edit_alamat_rumah" name="alamat_rumah" rows="2" maxlength="300"><?php echo htmlspecialchars($row['alamat_rumah']); ?></textarea>
                </div>
            </div>
        </div>
        
        <!-- Hidden fields -->
        <input type="hidden" name="id_asesor" value="<?php echo $row['id_asesor']; ?>">
        <input type="hidden" name="original_nia" value="<?php echo $row['nia']; ?>">
        <input type="hidden" name="original_kd_asesor" value="<?php echo $row['kd_asesor']; ?>">
    </div>
    
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">
            <i class="fas fa-times"></i> Batal
        </button>
        <button type="button" class="btn btn-warning" id="btn-update">
            <i class="fas fa-save"></i> Update Data
        </button>
    </div>
</form>
<?php
    $form_html = ob_get_clean();
    
    // Return response
    echo json_encode([
        'success' => true,
        'html' => $form_html,
        'data' => $row
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
