Berikut ini adalah struktur tabel "mapping_2024" :
id_mapping	int(11)
sekolah_id	int(11)
kd_asesor1	varchar(25)
kd_asesor2	varchar(25)
tgl_pra_visitasi	date
tgl_surat_tugas_pra_visitasi	date
no_surat_tugas_pra_visitasi	varchar(30)
tgl_mulai_visitasi	date
tgl_akhir_visitasi	date
tgl_surat_tugas_visitasi	date
no_surat_tugas_visitasi	varchar(30)
tahap	int(11)
tahun_akreditasi	varchar(4)
file_format_3_1_hasil_penilaian_pra_visitasi_1	varchar(50)
file_format_3_1_hasil_penilaian_pra_visitasi_2	varchar(50)
file_format_3_2_lk_penggalian_data_pra_visitasi_1	varchar(50)
file_format_3_2_lk_penggalian_data_pra_visitasi_2	varchar(50)
file_format_4_1_surat_tugas_visitasi	varchar(50)
file_format_4_2_pakta_integritas_1	varchar(50)
file_format_4_2_pakta_integritas_2	varchar(50)
file_format_4_3_lembar_rekap_penggalian_data_penilaian_1 varchar(50)
file_format_4_3_lembar_rekap_penggalian_data_penilaian_2 varchar(50)
file_format_4_4_berita_acara_visitasi	varchar(50)
file_format_4_5_laporan_individu_1	varchar(50)
file_format_4_5_laporan_individu_2	varchar(50)
file_format_4_5_laporan_kelompok	varchar(50)
file_format_4_5_catatan_dan_saran	varchar(50)
file_foto_visitasi_2024	varchar(50)
provinsi_id	int(11)


Berikut ini struktur tabel "sekolah" :
sekolah_id int(11)
nama_sekolah varchar(100)
npsn varchar(30)
jenjang_id int(1)
rumpun varchar(20)
alamat text
tipe_sekolah_id int(1)
status_sekolah_id int(1)
provinsi_id int(2)
kota_id int(6)
desa_kelurahan varchar(200)
kecamatan varchar(100)
nama_kepsek varchar(100)
no_hp_kepsek varchar(50)
no_wa_kepsek varchar(50)
nama_operator varchar(50)
no_hp_operator varchar(20)
no_wa_operator varchar(20)
email varchar(100)
nama_yayasan varchar(200)
no_akte varchar(50)
tahun_berdiri varchar(4)
status_keaktifan_id varchar(1)
soft_delete rchar(1)

Berikut ini adalah struktur tabel "jenjang" :
id_jenjang int(11)
jenjang_id varchar(2)
nm_jenjang varchar(15)

Berikut ini struktur tabel "kab_kota" :
id_kota	int(11)	
kota_id	varchar(10)
nm_kota	varchar(50)
provinsi_id	int(11)
kd_user	varchar(25)

Berikut ini struktur tabel "asesor_1" :
id_asesor1int(11)
kd_asesor1 varchar(25)
nia1 varchar(20) 
nm_asesor1	varchar(100) 
ktp	varchar(20) 
unit_kerja	varchar(300) 
kota_id1 varchar(10)
provinsi_id	int(11)
no_sertifikat varchar(30)
no_hp varchar(50)
no_wa varchar(50)
tempat_lahir varchar(30)
tgl_lahir date
jabatan	varchar(50)
jabatan_struktural varchar(20)
pendidikan varchar(15)
jenjang_id int(11)
rumpun varchar(7)
grade varchar(1)
jk varchar(10)
alamat_kantor varchar(300)
alamat_rumah varchar(300)
email varchar(50)
thn_terbit_sertifikat	date
kegiatan varchar(50)
status_keaktifan_id	varchar(1)
sebab text
kd_user	varchar(50)
soft_delete	varchar(1)

Berikut ini tabel "asesor_2" :
id_asesor2 int(11)
kd_asesor2 varchar(25)
nia2 varchar(20)
nm_asesor2 varchar(100)
ktp varchar(20)
unit_kerja varchar(300)
kota_id2 varchar(10)
provinsi_id	int(11)
no_sertifikat varchar(30)
no_hp varchar(50)
no_wa varchar(50)
tempat_lahir varchar(30)
tgl_lahir date
jabatan	varchar(50)
jabatan_struktural	varchar(20)
pendidikan varchar(15)
jenjang_id int(11)
rumpun varchar(7)
grade varchar(1)
jk varchar(10)
alamat_kantor varchar(300)
alamat_rumah varchar(300)
email varchar(50)
thn_terbit_sertifikat date
kegiatan varchar(50)
status_keaktifan_id	varchar(1)
sebab text
kd_user	varchar(50)
soft_delete	varchar(1)

Berikut ini struktur tabel mapping_2024_tahun :
id_mapping_tahun int(11)
nama_tahun	int(4)
provinsi_id	int(11)


buatlah modul "Mapping Asesor Visitasi Dasmen" pada direktori tim_it/mapping_dasmen_2020/mapping_2024.php, juga tersedia sub direktori ajax dan js, tabel header yang digunakan adalah :
NO,
NPSN (sekolah.npsn),
NAMA SEKOLAH (sekolah.nama_sekolah),
JENJANG (jenjang.nm_jenjang),
KAB/KOTA (kab_kota.nm_kota),
NIA ASESOR 1 (asesor_1.nia1),
NAMA ASESOR 1 (asesor_1.nm_asesor1),
NIA ASESOR 2 (asesor.nia2),
NAMA ASESOR 2 (asesor.nm_asesor2),
TAHUN AKREDITASI (mapping_2024.tahun_akreditasi),
TAHAP VISITASI (mapping_2024.tahap),
AKSI.
Untuk kolom AKSI tampilkan tombol ikon detail (tombolnya saja dulu), sampai disini apakah anda sudah mengerti, ataukah ada yang perlu ditanyakan?

oh iya lupa, tambahkan
<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');
?>

// Include header
<?php include '../header.php'; ?>


<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

terakhir di paling bawah <!-- Include footer -->
<?php include '../footer.php'; ?>

tambahkan juga tombol "Input Data Mapping" dan tombol "Export Excel" dan "Import Excel" (buatkan tombolnya saja dulu)
==================================================

sekarang anda akan meng-aktifkan fungsi tombol "Input Data Mapping", jika tombol tersebut di-klik akan tampil modal "Input Data Mapping" dengan form sebagi berikut
- label NPSN Sekolah (form input text) akan menyimpan ke field mapping_2024.sekolah_id
- NIA Asesor 1 (form input text) akan menyimpan ke field mapping_2024.kd_asesor1
- Nia Asesor 2 (form input text) akan menyimpan ke field mapping_2024.kd_asesor2
- label Tanggal Mulai Visitasi (form input date) akan menyimpan ke field mapping_2024.tgl_mulai_visitasi
- label Tanggal Akhir Visitasi (form input date) akan menyimpan ke field mapping_2024.tgl_akhir_visitasi
- label Tahun Akreditasi (form input text) akan menyimpan ke field mapping_2024.tahun_akreditasi
- label Tahap Ke (form input text) akan menyimpan ke field mapping_2024.tahap
- label Model Pembelajaran (form input text) akan menyimpan ke field mapping_2024.model_pembelajaran
- label Cara Visitasi (form input text) akan menyimpan ke field mapping_2024.cara_visitasi
- label Nomor Surat Tugas Visitasi (form input text) akan menyimpan ke field mapping_2024.no_surat
- Tanggal Surat Tugas Visitasi (form input date) akan menyimpan ke field mapping_2024.tgl_surat

sampai disini apakah ada yang ingin anda tanyakan?
=============================================================================================

Sekarang kita akan membuat modal "Detail Mapping Visitasi Dasmen".
Ketika tombol icon detail yang ada di kolom "Aksi" pada tabel utama modul "Mapping Asesor Visitasi Dasmen" di-klik maka akan muncul sebuah modal dengan judul modal adalah "Detail Mapping Visitasi Dasmen" dengan ukuran modal-xl.
Di dalam modal "Detail Mapping Visitasi Dasmen" terdapat lima buah kolom, masing-masing kolom memiliki judul kolom dan isi kolom.
Kolom pertama dengan judul kolom "Data Sekolah" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:
- Label NPSN dengan kolom disebelahnya berisi field sekolah.npsn
- Label Nama Sekolah  dengan kolom disebelahnya berisi field sekolah.nama_sekolah
- Label Jenjang dengan kolom disebelahnya berisi field jenjang.nm_jenjang
- Label Kab/Kota dengan kolom disebelahnya berisi field kab_kota.nm_kota
- Label Nama Kepala Sekolah dengan kolom disebelahnya berisi field sekolah.nama_kepsek
- Label HP Kepala Sekolah dengan kolom disebelahnya berisi field sekolah.no_hp_kepsek
- No WA Kepala Sekolah dengan kolom disebelahnya berisi field sekolah.no_wa_kepsek

Kolom kedua dengan judul kolom "Data Asesor" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:
- Label Asesor 1 (colspan=2)
- Label NIA dengan kolom disebelahnya berisi field asesor_1.nia1
- Label Nama dengan kolom disebelahnya berisi field asesor_1.nm_asesor1
- Label No HP dengan kolom disebelahnya berisi field asesor_1.no_hp
- Label Kab/Kota dengan kolom disebelahnya berisi field kab_kota.nm_kota
- Label Asesor 2 (colspan=2)
- Label NIA dengan kolom disebelahnya berisi field asesor_2.nia2
- Label Nama dengan kolom disebelahnya berisi field asesor_2.nm_asesor2
- Label No HP dengan kolom disebelahnya berisi field asesor_2.no_hp
- Label Kab/Kota dengan kolom disebelahnya berisi field kab_kota.nm_kota

Kolom ketiga dengan judul kolom "Dokumen Unggahan" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:

- Label File Hasil Asesmen Kecukupan 1 dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_hasil_asesmen_kecukupan_1 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Hasil Asesmen Kecukupan 2 dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_hasil_asesmen_kecukupan_2 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Rekapitulasi Hasil Asesmen Kecukupan dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_rekapitulasi_hasil_asesmen_kecukupan ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Pakta Integritas 1 dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_pakta_integritas_1 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Pakta Integritas 2 dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_pakta_integritas_2 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Laporan Individu 1 dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_laporan_individu_1 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Laporan Individu 2 dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_laporan_individu_2 ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Laporan Kelompok dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_laporan_kelompok ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Rekomendasi dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_rekomendasi ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Berita Acara Visitasi dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_berita_acara_visitasi ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah

- Label File Foto Visitasi dengan kolom disebelahnya berisi tulisan "Sudah Upload" dengan warna putih terang di grade hijau terang jika field mapping_2024.file_foto_visitasi ada isinya, akan tetapi jika kosong maka tampil tulisan "Belum Upload" dengan tulisan putih terang dengan grade merah


Kolom keempat dengan judul kolom "Pelaksanaan Kegiatan" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:
- Label Tanggal Audit Dokumen dengan kolom disebelahnya berisi field mapping_2024.tgl_audit_dokumen
- Label No Surat Audit Dokumen dengan kolom disebelahnya berisi field mapping_2024.no_surat_audit_dokumen
- Label Tanggal Surat Audit Dokumen dengan kolom disebelahnya berisi mapping_2024.field tgl_surat_audit_dokumen
- Label Tanggal Visitasi Dimulai dengan kolom disebelahnya berisi mapping_2024.field tgl_mulai_visitasi
- Label Tanggal Visitasi Berakhir dengan kolom disebelahnya berisi field mapping_2024.tgl_akhir_visitasi
- Label No Surat Tugas Visitasi dengan kolom disebelahnya berisi field no_surat
- Label Tanggal Surat Tugas Visitasi dengan kolom disebelahnya berisi mapping_2024.field tgl_surat

Kolom keempat dengan judul kolom "Aksi" dengan warna fill biru langit dan tulisan putih terang dengan isi kolom berupa tabel dengan garis tipis berwarna abu-abu sebagai berikut:
- Tombol "Edit Tanggal Kegiatan"
- Tombol "Edit Asesor Perubahan"
- Tombol "Download Surat Tugas Visitasi"
- Tombol "Download Surat Tugas Audit Dokumen"
================================================================================================================

baik kawan, kita akan melanjutkan modul "Mapping Asesor Visitasi Dasmen" ini, pada kolom "Dokumen Unggahan" yang ada di modal "Detail Mapping Visitasi Dasmen" tertulis "Sudah Upload" dan "Belum Upload", jika yang tampil adalah tulisan "Sudah Upload" maka tulisan tersebut bisa di-klik sedangkan tulisan "Belum Upload" tidak bisa di-klik, untuk tulisan "Sudah Upload" jika di-klik akan membuka tab baru yang menampilkan file PDF. OK kita jelaskan lebih rinci lagi sebagai berikut:

- File Hasil Asesmen Kecukupan 1 jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_asesmen_kecukupan/, untuk nama file diambil dari field mapping_2024.file_hasil_asesmen_kecukupan_1

- File Hasil Asesmen Kecukupan 2 jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_asesmen_kecukupan/, untuk nama file diambil dari field mapping_2024.file_hasil_asesmen_kecukupan_2

- File Rekapitulasi Hasil Asesmen Kecukupan jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_asesmen_kecukupan/, untuk nama file diambil dari field mapping_2024.file_rekapitulasi_hasil_asesmen_kecukupan

- File Pakta Integritas 1 jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_visitasi/, untuk nama file diambil dari field mapping_2024.file_pakta_integritas_1

- File Pakta Integritas 2 jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_visitasi/, untuk nama file diambil dari field mapping_2024.file_pakta_integritas_2

- File Laporan Individu 1 jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_visitasi/, untuk nama file diambil dari field mapping_2024.file_laporan_individu_1

- File Laporan Individu 2 jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_visitasi/, untuk nama file diambil dari field mapping_2024.file_laporan_individu_2

- File Laporan Kelompok jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_visitasi/, untuk nama file diambil dari field mapping_2024.file_laporan_kelompok

- File Rekomendasi jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_visitasi/, untuk nama file diambil dari field mapping_2024.file_rekomendasi

- File Berita Acara Visitasi jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_visitasi/, untuk nama file diambil dari field mapping_2024.file_berita_acara_visitasi

- File Foto Visitasi jika yang tampil tulisan "Sudah Upload" maka jika di-klik akan membuka file di direktori ../../../simak/files/upload_file_hasil_visitasi/, untuk nama file diambil dari field mapping_2024.file_foto_visitasi

Sampai disini apakah anda mengerti? silahkan bertanya jika belum paham
================================================================================================================


baik kawan, kita akan melanjutkan modul "Mapping Asesor Visitasi Dasmen" ini, pada kolom "AKSI" yang ada di modal "Detail Mapping Visitasi Dasmen" terdapat tombol "Edit Tanggal Kegiatan" jika tombol tersebut di-klik akan menampilkan sebuah modal dengan ukuran kecil yang berisi form untuk edit atau update field mapping_2024.tgl_mulai_visitasi dan field mapping_2024.tgl_akhir_visitasi, perlu saya sampaikan bahwa dengan tampilnya modal yang berisi form edit tersebut tidak serta merta menutup modal "Detail Mapping Visitasi Dasmen", nantinya modal untuk edit/update mapping_2024.tgl_mulai_visitasi dan field mapping_2024.tgl_akhir_visitasi berada di atas modal "Detail Mapping Visitasi Dasmen" kemudian ketika di-klik tombol "Update perubaan" maka secara otomatis "Tanggal Visitasi Dimulai" dan "Tanggal Visitasi Berakhir" yang ada di kolom "PELAKSANAAN KEGIATAN" berubah tanpa refresh browser, sampai disini apakah anda mengerti dengan apa yang saya maksud? jika belum mengerti silahkan bertanya

================================================================================================================

kita lanjut ke "Edit Asesor Perubahan" dimana bisnis prosesnya sangat mirip dengan "Edit Tanggal Kegiatan" yaitu jika tombol "Edit Asesor Perubahan" di-klik akan menampilkan sebuah modal dengan ukuran kecil yang berisi form untuk edit atau update field mapping_2024.kd_asesor1 dan field mapping_2024.kd_asesor2, perlu saya sampaikan bahwa dengan tampilnya modal yang berisi form edit tersebut tidak serta merta menutup modal "Detail Mapping Visitasi Dasmen", nantinya modal untuk edit/update field mapping_2024.kd_asesor1 dan field mapping_2024.kd_asesor2 berada di atas modal "Detail Mapping Visitasi Dasmen" kemudian ketika di-klik tombol "Update perubaan" maka secara otomatis "NIA", "Nama", "No. HP", "Kota" kedua asesor yang ada di kolom "DATA ASESOR" berubah tanpa refresh browser, sampai disini apakah anda mengerti dengan apa yang saya maksud? jika belum mengerti silahkan bertanya, Oh.. hampir lupa bahwa data yang diinput ke form "Edit Asesor Perubahan" adalah NIA salah satu dan atau kedua asesor