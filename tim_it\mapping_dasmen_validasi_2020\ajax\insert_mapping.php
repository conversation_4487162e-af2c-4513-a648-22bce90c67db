<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi required fields
    $required_fields = ['sekolah_id', 'kd_asesor1', 'kd_asesor2', 'tahun_akreditasi', 'tahap'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            $field_names = [
                'sekolah_id' => 'NPSN Sekolah',
                'kd_asesor1' => 'NIA Asesor 1',
                'kd_asesor2' => 'NIA Asesor 2',
                'tahun_akreditasi' => 'Tahun Akreditasi',
                'tahap' => 'Tahap'
            ];
            throw new Exception("Field " . $field_names[$field] . " harus diisi");
        }
    }
    
    // Sanitasi input
    $sekolah_id = intval($_POST['sekolah_id']);
    $kd_asesor1 = $conn->real_escape_string(trim($_POST['kd_asesor1']));
    $kd_asesor2 = $conn->real_escape_string(trim($_POST['kd_asesor2']));
    $tahun_akreditasi = $conn->real_escape_string(trim($_POST['tahun_akreditasi']));
    $tahap = intval($_POST['tahap']);
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Optional fields - gunakan default value jika kosong
    $tgl_mulai_validasi = !empty($_POST['tgl_mulai_validasi']) ? $_POST['tgl_mulai_validasi'] : '0000-00-00';
    $tgl_akhir_validasi = !empty($_POST['tgl_akhir_validasi']) ? $_POST['tgl_akhir_validasi'] : '0000-00-00';
    $no_surat_validasi = !empty($_POST['no_surat_validasi']) ? trim($_POST['no_surat_validasi']) : '';
    $tgl_surat_validasi = !empty($_POST['tgl_surat_validasi']) ? $_POST['tgl_surat_validasi'] : '0000-00-00';

    // Debug: Log values
    error_log("Insert Mapping - Values: sekolah_id=$sekolah_id, kd_asesor1=$kd_asesor1, kd_asesor2=$kd_asesor2, tgl_mulai=$tgl_mulai_validasi, tgl_akhir=$tgl_akhir_validasi");
    
    // Validasi tahun akreditasi
    if (!is_numeric($tahun_akreditasi) || strlen($tahun_akreditasi) != 4) {
        throw new Exception('Tahun akreditasi harus berupa 4 digit angka');
    }
    
    // Validasi tahap
    if ($tahap < 1) {
        throw new Exception('Tahap harus minimal 1');
    }
    
    // Cek duplikasi mapping
    $check_query = "SELECT COUNT(*) as count FROM mapping_validasi 
                    WHERE sekolah_id = ? AND tahun_akreditasi = ? AND provinsi_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("isi", $sekolah_id, $tahun_akreditasi, $provinsi_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result()->fetch_assoc();
    
    if ($check_result['count'] > 0) {
        throw new Exception('Mapping validasi untuk sekolah dan tahun akreditasi ini sudah ada');
    }
    
    // Begin transaction
    $conn->autocommit(false);
    
    // Insert mapping validasi
    $insert_query = "INSERT INTO mapping_validasi
                     (sekolah_id, kd_asesor1, kd_asesor2, tgl_mulai_validasi, tgl_akhir_validasi,
                      no_surat_validasi, tgl_surat_validasi, tahun_akreditasi, tahap, provinsi_id)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    $insert_stmt = $conn->prepare($insert_query);
    $insert_stmt->bind_param("isssssssii", $sekolah_id, $kd_asesor1, $kd_asesor2, $tgl_mulai_validasi, $tgl_akhir_validasi, $no_surat_validasi, $tgl_surat_validasi, $tahun_akreditasi, $tahap, $provinsi_id);
    
    if (!$insert_stmt->execute()) {
        throw new Exception('Gagal menyimpan data mapping validasi: ' . $conn->error);
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    echo json_encode([
        'success' => true,
        'message' => 'Data mapping validasi berhasil disimpan',
        'data' => [
            'id_mapping_validasi' => $conn->insert_id,
            'sekolah_id' => $sekolah_id,
            'tahun_akreditasi' => $tahun_akreditasi,
            'tahap' => $tahap
        ]
    ]);
    
} catch (Exception $e) {
    // Rollback transaction
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
