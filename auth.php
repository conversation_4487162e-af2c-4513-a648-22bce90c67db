<?php
// Include koneksi database
require_once 'koneksi.php';

// Start session
session_start();

// Rate limiting untuk mencegah brute force attack
function check_rate_limit() {
    $max_attempts = 5; // Maksimal 5 percobaan
    $lockout_time = 900; // 15 menit lockout
    $current_time = time();

    // Inisialisasi jika belum ada
    if (!isset($_SESSION['login_attempts'])) {
        $_SESSION['login_attempts'] = 0;
        $_SESSION['last_attempt_time'] = 0;
    }

    // Reset counter jika sudah lewat waktu lockout
    if ($current_time - $_SESSION['last_attempt_time'] > $lockout_time) {
        $_SESSION['login_attempts'] = 0;
    }

    // Cek apakah masih dalam periode lockout
    if ($_SESSION['login_attempts'] >= $max_attempts) {
        $remaining_time = $lockout_time - ($current_time - $_SESSION['last_attempt_time']);
        if ($remaining_time > 0) {
            $minutes = ceil($remaining_time / 60);
            return "Terlalu banyak percobaan login. Coba lagi dalam {$minutes} menit.";
        }
    }

    return true;
}

// Fungsi untuk mencatat percobaan login gagal
function record_failed_attempt() {
    $_SESSION['login_attempts']++;
    $_SESSION['last_attempt_time'] = time();
}

// Fungsi untuk membersihkan input dan mencegah SQL injection
function clean_input($data) {
    global $conn;
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    // Tambahan escape untuk MySQLi (double protection)
    $data = $conn->real_escape_string($data);
    return $data;
}

// Fungsi untuk validasi format input (lebih fleksibel)
function validate_input($username, $password) {
    // Validasi panjang username (1-50 karakter, lebih fleksibel)
    if (strlen($username) < 1 || strlen($username) > 50) {
        return "Username harus antara 1-50 karakter!";
    }

    // Password bebas, tidak ada validasi

    // Hanya cek SQL injection yang sangat berbahaya
    $dangerous_sql = [
        '/(\bUNION\s+SELECT\b)/i',
        '/(\bDROP\s+TABLE\b)/i',
        '/(\bDELETE\s+FROM\b)/i',
        '/(\bINSERT\s+INTO\b)/i'
    ];

    foreach ($dangerous_sql as $pattern) {
        if (preg_match($pattern, $username) || preg_match($pattern, $password)) {
            return "Input mengandung SQL berbahaya!";
        }
    }

    return true;
}

// Cek apakah form sudah disubmit
if ($_SERVER["REQUEST_METHOD"] == "POST") {

    // Cek rate limiting terlebih dahulu
    $rate_limit_check = check_rate_limit();
    if ($rate_limit_check !== true) {
        $_SESSION['error_message'] = $rate_limit_check;
        header('Location: login.php');
        exit();
    }

    // Validasi CSRF token
    if (!isset($_POST['csrf_token']) || !isset($_SESSION['csrf_token']) ||
        !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        $_SESSION['error_message'] = "Token keamanan tidak valid. Silakan coba lagi!";
        header('Location: login.php');
        exit();
    }

    // Ambil input mentah terlebih dahulu
    $raw_username = isset($_POST['username']) ? $_POST['username'] : '';
    $raw_password = isset($_POST['password']) ? $_POST['password'] : '';
    $remember = isset($_POST['remember']) ? true : false;

    // Validasi input tidak boleh kosong
    if (empty($raw_username) || empty($raw_password)) {
        $_SESSION['error_message'] = "Username dan password harus diisi!";
        header('Location: login.php');
        exit();
    }

    // Validasi format input sebelum dibersihkan
    $validation_result = validate_input($raw_username, $raw_password);
    if ($validation_result !== true) {
        $_SESSION['error_message'] = $validation_result;
        header('Location: login.php');
        exit();
    }

    // Bersihkan input setelah validasi
    $username = clean_input($raw_username);
    $password = clean_input($raw_password);
    
    try {
        // Query untuk mencari user berdasarkan username
        $sql = "SELECT id_user, kd_user, nm_user, username, password, level, sebagai_2, kota_id, provinsi_id, status_keaktifan_id
                FROM user
                WHERE username = ? AND soft_delete = 1";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows == 1) {
            $user = $result->fetch_assoc();
            
            // Cek status keaktifan user
            if ($user['status_keaktifan_id'] != 1) {
                $_SESSION['error_message'] = "Akun Anda tidak aktif. Silakan hubungi administrator!";
                header('Location: login.php');
                exit();
            }
            
            // Verifikasi password menggunakan MD5
            $hashed_password = md5($password);
            
            if ($user['password'] === $hashed_password) {
                // Login berhasil, reset counter dan regenerate CSRF token
                $_SESSION['login_attempts'] = 0;
                $_SESSION['last_attempt_time'] = 0;
                $_SESSION['csrf_token'] = bin2hex(random_bytes(32)); // Regenerate CSRF token

                $_SESSION['kd_user'] = $user['kd_user'];
                $_SESSION['nm_user'] = $user['nm_user'];
                $_SESSION['level'] = $user['level'];
                $_SESSION['sebagai_2'] = $user['sebagai_2'];
                $_SESSION['kota_id'] = $user['kota_id'];
                $_SESSION['provinsi_id'] = $user['provinsi_id'];
                $_SESSION['login_time'] = time();
                
                // Set cookie jika remember me dicentang
                if ($remember) {
                    $cookie_value = base64_encode($user['kd_user'] . '|' . $user['username']);
                    setcookie('sim4k_remember', $cookie_value, time() + (86400 * 30), "/"); // 30 hari
                }
                
                // Redirect berdasarkan level user
                switch ($user['level']) {
                    case 'Staff IT':
                        header('Location: tim_it/dashboard/dashboard.php');
                        break;
                    case 'Asesor':
                        header('Location: asesor/dashboard/dashboard.php');
                        break;
                    case 'Sekolah':
                        header('Location: sekolah/dashboard/dashboard.php');
                        break;
                    default:
                        $_SESSION['error_message'] = "Level user tidak dikenali!";
                        session_destroy();
                        header('Location: login.php');
                        break;
                }
                exit();
                
            } else {
                // Password salah, catat percobaan gagal
                record_failed_attempt();
                $_SESSION['error_message'] = "Username atau password salah!";
                header('Location: login.php');
                exit();
            }

        } else {
            // User tidak ditemukan, catat percobaan gagal
            record_failed_attempt();
            $_SESSION['error_message'] = "Username atau password salah!";
            header('Location: login.php');
            exit();
        }
        
    } catch (Exception $e) {
        // Error database
        error_log("Login error: " . $e->getMessage());
        $_SESSION['error_message'] = "Terjadi kesalahan sistem. Silakan coba lagi!";
        header('Location: login.php');
        exit();
    }
    
} else {
    // Jika tidak ada POST request, redirect ke login
    header('Location: login.php');
    exit();
}
?>
