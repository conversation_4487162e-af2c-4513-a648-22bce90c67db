<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi parameter
    if (!isset($_GET['nia']) || empty(trim($_GET['nia']))) {
        throw new Exception('NIA harus diisi');
    }
    
    $nia = $conn->real_escape_string(trim($_GET['nia']));
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Query untuk mencari asesor berdasarkan NIA (semua rumpun: paud, dasmen, kesetaraan)
    $query = "SELECT a.kd_asesor, a.nia, a.nm_asesor, a.no_hp, a.unit_kerja, a.rumpun,
                     kk.nm_kota
              FROM asesor a
              LEFT JOIN kab_kota kk ON a.kota_id = kk.kota_id
              WHERE a.nia = ?
                AND a.provinsi_id = ?
                AND a.soft_delete = '1'
                AND a.status_keaktifan_id = '1'";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("si", $nia, $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $data = $result->fetch_assoc();
        
        echo json_encode([
            'success' => true,
            'message' => 'Asesor ditemukan',
            'data' => $data
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'NIA tidak ditemukan atau asesor tidak aktif'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Lookup NIA Error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
