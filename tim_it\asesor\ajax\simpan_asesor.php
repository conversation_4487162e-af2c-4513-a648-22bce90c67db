<?php
/**
 * AJAX handler untuk menyimpan data asesor baru
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    $required_fields = ['nia', 'nm_asesor', 'jk', 'kota_id', 'rumpun', 'status_keaktifan_id'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            $field_names = [
                'nia' => 'NIA',
                'nm_asesor' => '<PERSON><PERSON>or',
                'jk' => '<PERSON><PERSON>',
                'kota_id' => 'Kabupaten/Kota',
                'rumpun' => 'Rumpun',
                'status_keaktifan_id' => 'Status Keaktifan'
            ];
            throw new Exception("Field " . $field_names[$field] . " harus diisi");
        }
    }
    
    // Sanitasi input dengan real_escape_string
    $nia = $conn->real_escape_string(trim($_POST['nia']));
    $nm_asesor = $conn->real_escape_string(trim($_POST['nm_asesor']));
    $jk = $conn->real_escape_string(trim($_POST['jk']));
    $kota_id = $conn->real_escape_string(trim($_POST['kota_id']));
    $provinsi_id_session = intval($_SESSION['provinsi_id']);

    // Optional fields dengan real_escape_string
    $kd_asesor = isset($_POST['kd_asesor']) ? $conn->real_escape_string(trim($_POST['kd_asesor'])) : '';

    // Debug: Log kd_asesor
    error_log("DEBUG - kd_asesor received: '" . (isset($_POST['kd_asesor']) ? $_POST['kd_asesor'] : 'NOT_SET') . "'");
    error_log("DEBUG - kd_asesor after escape: '" . $kd_asesor . "'");
    $ktp = isset($_POST['ktp']) ? $conn->real_escape_string(trim($_POST['ktp'])) : '';
    $unit_kerja = isset($_POST['unit_kerja']) ? $conn->real_escape_string(trim($_POST['unit_kerja'])) : '';
    $no_sertifikat = isset($_POST['no_sertifikat']) ? $conn->real_escape_string(trim($_POST['no_sertifikat'])) : '';
    $no_hp = isset($_POST['no_hp']) ? $conn->real_escape_string(trim($_POST['no_hp'])) : '';
    $no_wa = isset($_POST['no_wa']) ? $conn->real_escape_string(trim($_POST['no_wa'])) : '';
    $tempat_lahir = isset($_POST['tempat_lahir']) ? $conn->real_escape_string(trim($_POST['tempat_lahir'])) : '';
    $jabatan = isset($_POST['jabatan']) ? $conn->real_escape_string(trim($_POST['jabatan'])) : '';
    $jabatan_struktural = isset($_POST['jabatan_struktural']) ? $conn->real_escape_string(trim($_POST['jabatan_struktural'])) : '';
    $pendidikan = isset($_POST['pendidikan']) ? $conn->real_escape_string(trim($_POST['pendidikan'])) : '';
    $rumpun = isset($_POST['rumpun']) ? $conn->real_escape_string(trim($_POST['rumpun'])) : '';
    $grade = isset($_POST['grade']) ? $conn->real_escape_string(trim($_POST['grade'])) : '';
    $alamat_kantor = isset($_POST['alamat_kantor']) ? $conn->real_escape_string(trim($_POST['alamat_kantor'])) : '';
    $alamat_rumah = isset($_POST['alamat_rumah']) ? $conn->real_escape_string(trim($_POST['alamat_rumah'])) : '';
    $email = isset($_POST['email']) ? $conn->real_escape_string(trim($_POST['email'])) : '';
    $kegiatan = isset($_POST['kegiatan']) ? $conn->real_escape_string(trim($_POST['kegiatan'])) : '';
    $status_keaktifan_id = isset($_POST['status_keaktifan_id']) ? $conn->real_escape_string(trim($_POST['status_keaktifan_id'])) : '1';
    $sebab = isset($_POST['sebab']) ? $conn->real_escape_string(trim($_POST['sebab'])) : '';

    // Tambah kd_user dari session
    $kd_user = isset($_SESSION['kd_user']) ? $conn->real_escape_string($_SESSION['kd_user']) : '';

    // Handle date fields - gunakan empty string jika kosong (bukan NULL)
    $tgl_lahir = "''";
    if (isset($_POST['tgl_lahir']) && !empty($_POST['tgl_lahir'])) {
        $tgl_lahir_input = trim($_POST['tgl_lahir']);
        if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $tgl_lahir_input)) {
            $tgl_lahir = "'" . $conn->real_escape_string($tgl_lahir_input) . "'";
        }
    }

    $thn_terbit_sertifikat = "''";
    if (isset($_POST['thn_terbit_sertifikat']) && !empty($_POST['thn_terbit_sertifikat'])) {
        $thn_terbit_input = trim($_POST['thn_terbit_sertifikat']);
        if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $thn_terbit_input)) {
            $thn_terbit_sertifikat = "'" . $conn->real_escape_string($thn_terbit_input) . "'";
        }
    }

    // Handle jenjang_id
    $jenjang_id = 'NULL';
    if (isset($_POST['jenjang_id']) && !empty($_POST['jenjang_id'])) {
        $jenjang_id_input = trim($_POST['jenjang_id']);
        if (is_numeric($jenjang_id_input)) {
            $jenjang_id = intval($jenjang_id_input);
        } else {
            $jenjang_id = "'" . $conn->real_escape_string($jenjang_id_input) . "'";
        }
    }
    
    // Validasi jenis kelamin (gunakan data original sebelum escape)
    $jk_original = trim($_POST['jk']);
    if (!in_array($jk_original, ['Pria', 'Wanita'])) {
        throw new Exception('Jenis kelamin harus Pria atau Wanita');
    }

    // Validasi rumpun jika diisi
    $rumpun_original = isset($_POST['rumpun']) ? trim($_POST['rumpun']) : '';
    if (!empty($rumpun_original) && !in_array($rumpun_original, ['dasmen', 'paud'])) {
        throw new Exception('Rumpun harus dasmen atau paud');
    }

    // Validasi grade jika diisi
    $grade_original = isset($_POST['grade']) ? trim($_POST['grade']) : '';
    if (!empty($grade_original) && !in_array($grade_original, ['A', 'B', 'C'])) {
        throw new Exception('Grade harus A, B, atau C');
    }

    // Validasi status keaktifan jika diisi
    $status_original = isset($_POST['status_keaktifan_id']) ? trim($_POST['status_keaktifan_id']) : '';
    if (!empty($status_original) && !in_array($status_original, ['0', '1', '2'])) {
        throw new Exception('Status keaktifan tidak valid');
    }
    
    // Cek duplikasi NIA (hanya yang soft_delete = '1')
    $check_nia_sql = "SELECT COUNT(*) as count FROM asesor WHERE nia = '$nia' AND soft_delete = '1'";
    $nia_result = $conn->query($check_nia_sql);
    if (!$nia_result) {
        throw new Exception('Error checking NIA: ' . $conn->error);
    }
    $nia_count = $nia_result->fetch_assoc()['count'];

    if ($nia_count > 0) {
        throw new Exception('NIA sudah ada, gunakan NIA yang berbeda');
    }

    // Cek duplikasi kode asesor jika diisi (hanya yang soft_delete = '1')
    if (!empty($kd_asesor)) {
        $check_kd_sql = "SELECT COUNT(*) as count FROM asesor WHERE kd_asesor = '$kd_asesor' AND soft_delete = '1'";
        $kd_result = $conn->query($check_kd_sql);
        if (!$kd_result) {
            throw new Exception('Error checking kode asesor: ' . $conn->error);
        }
        $kd_count = $kd_result->fetch_assoc()['count'];

        if ($kd_count > 0) {
            throw new Exception('Kode asesor sudah ada, gunakan kode yang berbeda');
        }
    }

    // Cek duplikasi di tabel user - kd_user
    if (!empty($kd_asesor)) {
        $check_user_kd_sql = "SELECT COUNT(*) as count FROM user WHERE kd_user = '$kd_asesor' AND soft_delete = '1'";
        $user_kd_result = $conn->query($check_user_kd_sql);
        if (!$user_kd_result) {
            throw new Exception('Error checking kd_user: ' . $conn->error);
        }
        $user_kd_count = $user_kd_result->fetch_assoc()['count'];

        if ($user_kd_count > 0) {
            throw new Exception('Kode user sudah ada, gunakan kode yang berbeda');
        }
    }

    // Cek duplikasi di tabel user - nianpsn
    $check_user_nia_sql = "SELECT COUNT(*) as count FROM user WHERE nianpsn = '$nia' AND soft_delete = '1'";
    $user_nia_result = $conn->query($check_user_nia_sql);
    if (!$user_nia_result) {
        throw new Exception('Error checking nianpsn: ' . $conn->error);
    }
    $user_nia_count = $user_nia_result->fetch_assoc()['count'];

    if ($user_nia_count > 0) {
        throw new Exception('NIA sudah terdaftar sebagai user, gunakan NIA yang berbeda');
    }
    
    // Begin transaction
    $conn->autocommit(false);

    // Query insert langsung dengan real_escape_string
    $sql = "INSERT INTO asesor (
                kd_asesor, nia, nm_asesor, ktp, unit_kerja, kota_id, provinsi_id,
                no_sertifikat, no_hp, no_wa, tempat_lahir, tgl_lahir, jabatan,
                jabatan_struktural, pendidikan, jenjang_id, rumpun, grade, jk,
                alamat_kantor, alamat_rumah, email, thn_terbit_sertifikat,
                kegiatan, status_keaktifan_id, sebab, kd_user, soft_delete
            ) VALUES (
                '$kd_asesor', '$nia', '$nm_asesor', '$ktp', '$unit_kerja', '$kota_id', $provinsi_id_session,
                '$no_sertifikat', '$no_hp', '$no_wa', '$tempat_lahir', $tgl_lahir, '$jabatan',
                '$jabatan_struktural', '$pendidikan', $jenjang_id, '$rumpun', '$grade', '$jk',
                '$alamat_kantor', '$alamat_rumah', '$email', $thn_terbit_sertifikat,
                '$kegiatan', '$status_keaktifan_id', '$sebab', '$kd_user', '1'
            )";

    // Execute query untuk tabel asesor
    $result = $conn->query($sql);

    if (!$result) {
        throw new Exception('Gagal menyimpan data asesor: ' . $conn->error);
    }

    $id_asesor = $conn->insert_id;

    // Sinkronisasi ke tabel asesor_1
    $sql_asesor1 = "INSERT INTO asesor_1 (
                        id_asesor1, kd_asesor1, nia1, nm_asesor1, ktp, unit_kerja, kota_id1, provinsi_id,
                        no_sertifikat, no_hp, no_wa, tempat_lahir, tgl_lahir, jabatan,
                        jabatan_struktural, pendidikan, jenjang_id, rumpun, grade, jk,
                        alamat_kantor, alamat_rumah, email, thn_terbit_sertifikat,
                        kegiatan, status_keaktifan_id, sebab, kd_user, soft_delete
                    ) VALUES (
                        $id_asesor, '$kd_asesor', '$nia', '$nm_asesor', '$ktp', '$unit_kerja', '$kota_id', $provinsi_id_session,
                        '$no_sertifikat', '$no_hp', '$no_wa', '$tempat_lahir', $tgl_lahir, '$jabatan',
                        '$jabatan_struktural', '$pendidikan', $jenjang_id, '$rumpun', '$grade', '$jk',
                        '$alamat_kantor', '$alamat_rumah', '$email', $thn_terbit_sertifikat,
                        '$kegiatan', '$status_keaktifan_id', '$sebab', '$kd_user', '1'
                    )";

    $result_asesor1 = $conn->query($sql_asesor1);
    if (!$result_asesor1) {
        throw new Exception('Gagal sinkronisasi ke tabel asesor_1: ' . $conn->error);
    }

    // Sinkronisasi ke tabel asesor_2
    $sql_asesor2 = "INSERT INTO asesor_2 (
                        id_asesor2, kd_asesor2, nia2, nm_asesor2, ktp, unit_kerja, kota_id2, provinsi_id,
                        no_sertifikat, no_hp, no_wa, tempat_lahir, tgl_lahir, jabatan,
                        jabatan_struktural, pendidikan, jenjang_id, rumpun, grade, jk,
                        alamat_kantor, alamat_rumah, email, thn_terbit_sertifikat,
                        kegiatan, status_keaktifan_id, sebab, kd_user, soft_delete
                    ) VALUES (
                        $id_asesor, '$kd_asesor', '$nia', '$nm_asesor', '$ktp', '$unit_kerja', '$kota_id', $provinsi_id_session,
                        '$no_sertifikat', '$no_hp', '$no_wa', '$tempat_lahir', $tgl_lahir, '$jabatan',
                        '$jabatan_struktural', '$pendidikan', $jenjang_id, '$rumpun', '$grade', '$jk',
                        '$alamat_kantor', '$alamat_rumah', '$email', $thn_terbit_sertifikat,
                        '$kegiatan', '$status_keaktifan_id', '$sebab', '$kd_user', '1'
                    )";

    $result_asesor2 = $conn->query($sql_asesor2);
    if (!$result_asesor2) {
        throw new Exception('Gagal sinkronisasi ke tabel asesor_2: ' . $conn->error);
    }

    // Sinkronisasi ke tabel user
    $sql_user = "INSERT INTO user (
                     kd_user, nianpsn, nm_user, jenjang_id, status_keaktifan_id,
                     provinsi_id, kota_id, username, password, level, soft_delete
                 ) VALUES (
                     '$kd_asesor', '$nia', '$nm_asesor', $jenjang_id, '$status_keaktifan_id',
                     $provinsi_id_session, '$kota_id', '$nia', '$nia', 'Asesor', '1'
                 )";

    $result_user = $conn->query($sql_user);
    if (!$result_user) {
        throw new Exception('Gagal sinkronisasi ke tabel user: ' . $conn->error);
    }

    // Commit transaction jika semua berhasil
    $conn->commit();
    $conn->autocommit(true);
    
    // Response sukses (gunakan data original untuk response)
    $response = [
        'success' => true,
        'message' => 'Data asesor "' . trim($_POST['nm_asesor']) . '" berhasil disimpan',
        'data' => [
            'id_asesor' => $id_asesor,
            'nia' => trim($_POST['nia']),
            'nm_asesor' => trim($_POST['nm_asesor'])
        ]
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Rollback transaction jika ada error
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Simpan Asesor Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
