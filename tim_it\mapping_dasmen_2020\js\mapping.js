$(document).ready(function() {
    // Initialize DataTable
    initDataTable();
    
    // Event handlers
    initEventHandlers();
});

function initDataTable() {
    $('#table-mapping').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: 'ajax/get_mapping.php',
            type: 'POST'
        },
        columns: [
            { 
                data: null,
                orderable: false,
                searchable: false,
                render: function(data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { data: 'npsn' },
            {
                data: 'nama_sekolah',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { data: 'nm_jenjang' },
            { data: 'nm_kota' },
            { data: 'nia1' },
            {
                data: 'nm_asesor1',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { data: 'nia2' },
            {
                data: 'nm_asesor2',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { 
                data: 'tahun_akreditasi',
                className: 'text-center'
            },
            { 
                data: 'tahap',
                className: 'text-center',
                render: function(data, type, row) {
                    if (data) {
                        var badgeClass = '';
                        switch(data) {
                            case '1':
                                badgeClass = 'badge-primary';
                                break;
                            case '2':
                                badgeClass = 'badge-success';
                                break;
                            case '3':
                                badgeClass = 'badge-warning';
                                break;
                            default:
                                badgeClass = 'badge-secondary';
                        }
                        return '<span class="badge ' + badgeClass + ' badge-tahap">' + data + '</span>';
                    }
                    return '-';
                }
            },
            { 
                data: null,
                orderable: false,
                searchable: false,
                className: 'text-center',
                render: function(data, type, row) {
                    return `
                        <button type="button" class="btn btn-info btn-sm btn-action" 
                                onclick="detailMapping(${row.id_mapping})" 
                                title="Detail Mapping">
                            <i class="fas fa-eye"></i>
                        </button>
                    `;
                }
            }
        ],
        order: [[9, 'desc']], // Order by tahun_akreditasi descending
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: "Memuat data...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(disaring dari _MAX_ total data)",
            paginate: {
                first: "Pertama",
                last: "Terakhir",
                next: "Selanjutnya",
                previous: "Sebelumnya"
            },
            emptyTable: "Tidak ada data mapping"
        },
        responsive: true,
        autoWidth: true,
        scrollX: true
    });
}

function initEventHandlers() {
    // Button click handlers
    $('#btn-input-mapping').on('click', function() {
        inputDataMapping();
    });

    $('#btn-export-excel').on('click', function() {
        exportExcel();
    });

    $('#btn-import-excel').on('click', function() {
        importExcel();
    });

    $('#btn-tahun-akreditasi').on('click', function() {
        filterTahunAkreditasi();
    });

    // Form submit handler for tahun akreditasi
    $('#form-tahun-akreditasi').on('submit', function(e) {
        e.preventDefault();
        updateTahunAkreditasi();
    });

    // Form submit handler for input mapping
    $('#form-input-mapping').on('submit', function(e) {
        e.preventDefault();
        submitInputMapping();
    });

    // Modal reset handlers
    $('#modal-input-mapping').on('hidden.bs.modal', function() {
        $('#form-input-mapping')[0].reset();
    });

    $('#modal-tahun-akreditasi').on('hidden.bs.modal', function() {
        $('#form-tahun-akreditasi')[0].reset();
    });

    // Event handler untuk tombol Hapus di modal detail
    $(document).on('click', '#btn-hapus-mapping', function(e) {
        e.preventDefault();
        console.log('Hapus Mapping clicked');
        openKonfirmasiHapusMappingModal();
    });

    // Event handler untuk konfirmasi hapus mapping
    $(document).on('click', '#btn-konfirmasi-hapus-mapping', function(e) {
        e.preventDefault();
        submitHapusMapping();
    });
}

function detailMapping(mappingId) {
    console.log('Detail mapping ID:', mappingId);

    // Load detail data
    $.ajax({
        url: 'ajax/get_detail_mapping.php',
        type: 'POST',
        data: { id_mapping: mappingId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                populateDetailModal(response.data);
                $('#modal-detail-mapping').modal('show');
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Terjadi kesalahan saat memuat detail mapping');
        }
    });
}

function populateDetailModal(data) {
    // Data Sekolah
    $('#detail-npsn').text(data.npsn || '-');
    $('#detail-nama-sekolah').text(data.nama_sekolah || '-');
    $('#detail-jenjang').text(data.nm_jenjang || '-');
    $('#detail-kab-kota').text(data.nm_kota || '-');
    $('#detail-nama-kepsek').text(data.nama_kepsek || '-');
    $('#detail-hp-kepsek').text(data.no_hp_kepsek || '-');
    $('#detail-wa-kepsek').text(data.no_wa_kepsek || '-');

    // Data Asesor 1
    $('#detail-nia1').text(data.nia1 || '-');
    $('#detail-nama-asesor1').text(data.nm_asesor1 || '-');
    $('#detail-hp-asesor1').text(data.no_hp_asesor1 || '-');
    $('#detail-kota-asesor1').text(data.nm_kota_asesor1 || '-');

    // Data Asesor 2
    $('#detail-nia2').text(data.nia2 || '-');
    $('#detail-nama-asesor2').text(data.nm_asesor2 || '-');
    $('#detail-hp-asesor2').text(data.no_hp_asesor2 || '-');
    $('#detail-kota-asesor2').text(data.nm_kota_asesor2 || '-');

    // Status File Upload dengan Preview
    $('#detail-file-kecukupan1').html(getFileStatus(data.file_hasil_asesmen_kecukupan_1, 'kecukupan'));
    $('#detail-file-kecukupan2').html(getFileStatus(data.file_hasil_asesmen_kecukupan_2, 'kecukupan'));
    $('#detail-file-rekapitulasi').html(getFileStatus(data.file_rekapitulasi_hasil_asesmen_kecukupan, 'kecukupan'));
    $('#detail-file-pakta1').html(getFileStatus(data.file_pakta_integritas_1, 'visitasi'));
    $('#detail-file-pakta2').html(getFileStatus(data.file_pakta_integritas_2, 'visitasi'));
    $('#detail-file-individu1').html(getFileStatus(data.file_laporan_individu_1, 'visitasi'));
    $('#detail-file-individu2').html(getFileStatus(data.file_laporan_individu_2, 'visitasi'));
    $('#detail-file-kelompok').html(getFileStatus(data.file_laporan_kelompok, 'visitasi'));
    $('#detail-file-rekomendasi').html(getFileStatus(data.file_rekomendasi, 'visitasi'));
    $('#detail-file-berita-acara').html(getFileStatus(data.file_berita_acara_visitasi, 'visitasi'));
    $('#detail-file-foto').html(getFileStatus(data.file_foto_visitasi, 'visitasi'));

    // Pelaksanaan Kegiatan
    $('#detail-tgl-audit').text(formatDate(data.tgl_audit_dokumen) || '-');
    $('#detail-no-surat-audit').text(data.no_surat_audit_dokumen || '-');
    $('#detail-tgl-surat-audit').text(formatDate(data.tgl_surat_audit_dokumen) || '-');
    $('#detail-tgl-mulai-visitasi').text(formatDate(data.tgl_mulai_visitasi) || '-');
    $('#detail-tgl-akhir-visitasi').text(formatDate(data.tgl_akhir_visitasi) || '-');
    $('#detail-no-surat-visitasi').text(data.no_surat || '-');
    $('#detail-tgl-surat-visitasi').text(formatDate(data.tgl_surat) || '-');

    // Store data untuk edit
    $('#modal-detail-mapping').data('mapping-data', data);
}

function getFileStatus(filename, fileType) {
    if (filename && filename.trim() !== '') {
        return '<span class="badge badge-success text-white file-preview-link" data-filename="' + filename + '" data-filetype="' + fileType + '" style="cursor: pointer;">Sudah Upload</span>';
    } else {
        return '<span class="badge badge-danger text-white">Belum Upload</span>';
    }
}

function formatDate(dateString) {
    if (!dateString || dateString === '0000-00-00') return '';

    const date = new Date(dateString);
    const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    };
    return date.toLocaleDateString('id-ID', options);
}

// Event handler untuk tombol Download Surat Tugas Visitasi
$(document).on('click', '#btn-download-surat-visitasi', function(e) {
    e.preventDefault();

    const mappingData = $('#modal-detail-mapping').data('mapping-data');

    if (mappingData && mappingData.id_mapping) {
        // Buka surat tugas dengan DOMPDF di tab baru
        const url = 'mapping_st_visitasi.php?kode=' + mappingData.id_mapping;
        window.open(url, '_blank');
    } else {
        showAlert('error', 'Data mapping tidak ditemukan');
    }
});

// Event handler untuk tombol Download Surat Tugas Audit Dokumen
$(document).on('click', '#btn-download-surat-audit', function(e) {
    e.preventDefault();

    const mappingData = $('#modal-detail-mapping').data('mapping-data');

    if (mappingData && mappingData.id_mapping) {
        // Buka surat tugas audit dokumen dengan DOMPDF di tab baru
        const url = 'mapping_st_audit_dokumen.php?kode=' + mappingData.id_mapping;
        window.open(url, '_blank');
    } else {
        showAlert('error', 'Data mapping tidak ditemukan');
    }
});

// Event handler untuk preview file
$(document).on('click', '.file-preview-link', function(e) {
    e.preventDefault();

    const filename = $(this).data('filename');
    const fileType = $(this).data('filetype');

    if (filename && filename.trim() !== '') {
        previewFile(filename, fileType);
    }
});

function previewFile(filename, fileType) {
    let basePath = '';

    // Tentukan path berdasarkan jenis file
    if (fileType === 'kecukupan') {
        basePath = '../../../simak/files/upload_file_hasil_asesmen_kecukupan/';
    } else if (fileType === 'visitasi') {
        basePath = '../../../simak/files/upload_file_hasil_visitasi/';
    }

    const fileUrl = basePath + filename;

    // Buka file di tab baru
    window.open(fileUrl, '_blank');
}

// Event handler untuk tombol Edit Tanggal Kegiatan
$(document).on('click', '#btn-edit-tanggal', function(e) {
    e.preventDefault();

    const mappingData = $('#modal-detail-mapping').data('mapping-data');

    if (mappingData) {
        // Populate form edit tanggal
        $('#edit-id-mapping').val(mappingData.id_mapping);
        $('#edit-tgl-mulai-visitasi').val(mappingData.tgl_mulai_visitasi || '');
        $('#edit-tgl-akhir-visitasi').val(mappingData.tgl_akhir_visitasi || '');

        // Show modal edit tanggal
        $('#modal-edit-tanggal').modal('show');
    } else {
        showAlert('error', 'Data mapping tidak ditemukan');
    }
});

// Event handler untuk form submit edit tanggal
$(document).on('submit', '#form-edit-tanggal', function(e) {
    e.preventDefault();

    const formData = {
        id_mapping: $('#edit-id-mapping').val(),
        tgl_mulai_visitasi: $('#edit-tgl-mulai-visitasi').val(),
        tgl_akhir_visitasi: $('#edit-tgl-akhir-visitasi').val()
    };

    // Validasi tanggal
    if (formData.tgl_mulai_visitasi && formData.tgl_akhir_visitasi) {
        if (new Date(formData.tgl_mulai_visitasi) > new Date(formData.tgl_akhir_visitasi)) {
            showAlert('error', 'Tanggal mulai visitasi tidak boleh lebih besar dari tanggal akhir visitasi');
            return;
        }
    }

    // Submit via AJAX
    $.ajax({
        url: 'ajax/update_tanggal_visitasi.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Update tampilan di modal detail
                $('#detail-tgl-mulai-visitasi').text(formatDate(formData.tgl_mulai_visitasi) || '-');
                $('#detail-tgl-akhir-visitasi').text(formatDate(formData.tgl_akhir_visitasi) || '-');

                // Update data yang tersimpan
                const currentData = $('#modal-detail-mapping').data('mapping-data');
                currentData.tgl_mulai_visitasi = formData.tgl_mulai_visitasi;
                currentData.tgl_akhir_visitasi = formData.tgl_akhir_visitasi;
                $('#modal-detail-mapping').data('mapping-data', currentData);

                // Tutup modal edit
                $('#modal-edit-tanggal').modal('hide');

                // Refresh DataTables untuk update tabel utama
                if ($.fn.DataTable.isDataTable('#table-mapping')) {
                    $('#table-mapping').DataTable().ajax.reload(null, false);
                }
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Terjadi kesalahan saat mengupdate tanggal visitasi');
        }
    });
});

// Event handler untuk tombol Edit Asesor Perubahan
$(document).on('click', '#btn-edit-asesor', function(e) {
    e.preventDefault();

    const mappingData = $('#modal-detail-mapping').data('mapping-data');

    if (mappingData) {
        // Populate form edit asesor
        $('#edit-asesor-id-mapping').val(mappingData.id_mapping);
        $('#edit-nia-asesor1').val(''); // Kosongkan untuk input baru
        $('#edit-nia-asesor2').val(''); // Kosongkan untuk input baru

        // Show modal edit asesor
        $('#modal-edit-asesor').modal('show');
    } else {
        showAlert('error', 'Data mapping tidak ditemukan');
    }
});

// Event handler untuk form submit edit asesor
$(document).on('submit', '#form-edit-asesor', function(e) {
    e.preventDefault();

    const formData = {
        id_mapping: $('#edit-asesor-id-mapping').val(),
        nia_asesor1: $('#edit-nia-asesor1').val().trim(),
        nia_asesor2: $('#edit-nia-asesor2').val().trim()
    };

    // Validasi minimal salah satu NIA harus diisi
    if (!formData.nia_asesor1 && !formData.nia_asesor2) {
        showAlert('error', 'Minimal salah satu NIA asesor harus diisi');
        return;
    }

    // Submit via AJAX
    $.ajax({
        url: 'ajax/update_asesor_mapping.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Update tampilan di modal detail
                updateAsesorDisplay(response.data);

                // Update data yang tersimpan
                const currentData = $('#modal-detail-mapping').data('mapping-data');
                if (response.data.asesor1) {
                    currentData.kd_asesor1 = response.data.asesor1.kd_asesor1;
                    currentData.nia1 = response.data.asesor1.nia1;
                    currentData.nm_asesor1 = response.data.asesor1.nm_asesor1;
                    currentData.no_hp_asesor1 = response.data.asesor1.no_hp;
                    currentData.nm_kota_asesor1 = response.data.asesor1.nm_kota;
                }
                if (response.data.asesor2) {
                    currentData.kd_asesor2 = response.data.asesor2.kd_asesor2;
                    currentData.nia2 = response.data.asesor2.nia2;
                    currentData.nm_asesor2 = response.data.asesor2.nm_asesor2;
                    currentData.no_hp_asesor2 = response.data.asesor2.no_hp;
                    currentData.nm_kota_asesor2 = response.data.asesor2.nm_kota;
                }
                $('#modal-detail-mapping').data('mapping-data', currentData);

                // Tutup modal edit
                $('#modal-edit-asesor').modal('hide');

                // Refresh DataTables untuk update tabel utama
                if ($.fn.DataTable.isDataTable('#table-mapping')) {
                    $('#table-mapping').DataTable().ajax.reload(null, false);
                }
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Terjadi kesalahan saat mengupdate data asesor');
        }
    });
});

function updateAsesorDisplay(data) {
    // Update Asesor 1 jika ada
    if (data.asesor1) {
        $('#detail-nia1').text(data.asesor1.nia1 || '-');
        $('#detail-nama-asesor1').text(data.asesor1.nm_asesor1 || '-');
        $('#detail-hp-asesor1').text(data.asesor1.no_hp || '-');
        $('#detail-kota-asesor1').text(data.asesor1.nm_kota || '-');
    }

    // Update Asesor 2 jika ada
    if (data.asesor2) {
        $('#detail-nia2').text(data.asesor2.nia2 || '-');
        $('#detail-nama-asesor2').text(data.asesor2.nm_asesor2 || '-');
        $('#detail-hp-asesor2').text(data.asesor2.no_hp || '-');
        $('#detail-kota-asesor2').text(data.asesor2.nm_kota || '-');
    }
}

function inputDataMapping() {
    console.log('Input data mapping clicked');
    $('#modal-input-mapping').modal('show');
}

function submitInputMapping() {
    console.log('Submit input mapping');

    var formData = new FormData($('#form-input-mapping')[0]);

    // Disable submit button
    var submitBtn = $('#form-input-mapping button[type="submit"]');
    var originalText = submitBtn.html();
    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    $.ajax({
        url: 'ajax/insert_mapping.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function(response) {
            console.log('Insert mapping response:', response);
            if (response.success) {
                showAlert('success', response.message);
                $('#modal-input-mapping').modal('hide');
                var table = $('#table-mapping').DataTable();
                table.ajax.reload(function() {
                    // Fix alignment setelah reload
                    setTimeout(function() {
                        table.columns.adjust();
                    }, 100);
                });
            } else {
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('Insert mapping error:', xhr.responseText);
            showAlert('error', 'Terjadi kesalahan saat menyimpan data mapping: ' + error);
        },
        complete: function() {
            // Re-enable submit button
            submitBtn.prop('disabled', false).html(originalText);
        }
    });
}

function exportExcel() {
    console.log('Export Excel clicked');
    showAlert('info', 'Fitur export Excel akan segera tersedia');
    
    // TODO: Implement Excel export functionality
    // This will export the current mapping data to Excel format
}

function importExcel() {
    console.log('Import Excel clicked');
    showAlert('info', 'Fitur import Excel akan segera tersedia');

    // TODO: Implement Excel import functionality
    // This will allow importing mapping data from Excel file
}

function filterTahunAkreditasi() {
    console.log('Filter tahun akreditasi clicked');

    // Load current tahun akreditasi value
    $.ajax({
        url: 'ajax/get_tahun.php',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $('#nama_tahun').val(response.data.nama_tahun);
                $('#modal-tahun-akreditasi').modal('show');
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Terjadi kesalahan saat memuat data tahun akreditasi');
        }
    });
}

function updateTahunAkreditasi() {
    console.log('Update tahun akreditasi');

    var formData = new FormData($('#form-tahun-akreditasi')[0]);

    // Disable submit button
    var submitBtn = $('#form-tahun-akreditasi button[type="submit"]');
    var originalText = submitBtn.html();
    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    $.ajax({
        url: 'ajax/update_tahun.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function(response) {
            console.log('Update tahun response:', response);
            if (response.success) {
                $('#modal-tahun-akreditasi').modal('hide');
                // Reload DataTable untuk menerapkan filter tahun baru
                var table = $('#table-mapping').DataTable();
                table.ajax.reload(function() {
                    // Fix alignment setelah reload
                    setTimeout(function() {
                        table.columns.adjust();
                    }, 100);
                });
            } else {
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('Update tahun error:', xhr.responseText);
            showAlert('error', 'Terjadi kesalahan saat menyimpan tahun akreditasi: ' + error);
        },
        complete: function() {
            // Re-enable submit button
            submitBtn.prop('disabled', false).html(originalText);
        }
    });
}

// Function untuk membuka modal konfirmasi hapus mapping
function openKonfirmasiHapusMappingModal() {
    console.log('Opening konfirmasi hapus mapping modal');

    // Ambil data mapping dari modal detail yang sedang terbuka
    const mappingData = $('#modal-detail-mapping').data('mapping-data');

    if (!mappingData) {
        showAlert('error', 'Data mapping tidak ditemukan. Silakan tutup dan buka kembali modal detail.');
        return;
    }

    // Populate data di modal konfirmasi
    $('#hapus-nama-sekolah').text(mappingData.nama_sekolah || '-');
    $('#hapus-npsn').text(mappingData.npsn || '-');
    $('#hapus-asesor1').text(mappingData.nm_asesor1 || '-');
    $('#hapus-asesor2').text(mappingData.nm_asesor2 || '-');
    $('#hapus-tahun').text(mappingData.tahun_akreditasi || '-');
    $('#hapus-tahap').text(mappingData.tahap || '-');

    // Simpan ID mapping yang akan dihapus
    $('#id-mapping-akan-dihapus').val(mappingData.id_mapping);

    // Show modal dengan z-index yang lebih tinggi untuk nested modal
    $('#modal-konfirmasi-hapus-mapping').css('z-index', 1070).modal('show');

    console.log('Konfirmasi hapus modal opened with ID:', mappingData.id_mapping);
}

// Function untuk submit hapus mapping
function submitHapusMapping() {
    console.log('Submitting hapus mapping');

    const idMapping = $('#id-mapping-akan-dihapus').val();

    if (!idMapping) {
        showAlert('error', 'ID mapping tidak valid');
        return;
    }

    $.ajax({
        url: 'ajax/hapus_mapping.php',
        type: 'POST',
        data: { id_mapping: idMapping },
        dataType: 'json',
        beforeSend: function() {
            // Disable tombol konfirmasi dan show loading
            $('#btn-konfirmasi-hapus-mapping').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menghapus...');
        },
        success: function(response) {
            if (response.success) {
                console.log('Mapping berhasil dihapus:', response.data);

                // Tutup modal konfirmasi hapus
                $('#modal-konfirmasi-hapus-mapping').modal('hide');

                // Tutup modal detail
                $('#modal-detail-mapping').modal('hide');

                // Reload DataTable tanpa refresh halaman
                $('#table-mapping').DataTable().ajax.reload(null, false);

                // Silent success - no notification to avoid clutter
                console.log('Data mapping visitasi berhasil dihapus:', response.data);

            } else {
                showAlert('error', response.message || 'Gagal menghapus data mapping visitasi');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error submitting hapus mapping:', error);
            showAlert('error', 'Terjadi kesalahan saat menghapus data mapping visitasi');
        },
        complete: function() {
            // Enable tombol konfirmasi kembali
            $('#btn-konfirmasi-hapus-mapping').prop('disabled', false).html('<i class="fas fa-trash"></i> Ya, Hapus Data');
        }
    });
}

function showAlert(type, message) {
    var alertClass = '';
    var iconClass = '';

    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            iconClass = 'fa-check-circle';
            break;
        case 'error':
            alertClass = 'alert-danger';
            iconClass = 'fa-exclamation-triangle';
            break;
        case 'warning':
            alertClass = 'alert-warning';
            iconClass = 'fa-exclamation-triangle';
            break;
        case 'info':
            alertClass = 'alert-info';
            iconClass = 'fa-info-circle';
            break;
        default:
            alertClass = 'alert-info';
            iconClass = 'fa-info-circle';
    }

    var alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${iconClass}"></i> ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `;

    // Remove existing alerts
    $('.alert').remove();

    // Add new alert at the top of content
    $('.content-wrapper .content').prepend(alertHtml);

    // Auto hide after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
