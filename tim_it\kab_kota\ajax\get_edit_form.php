<?php
/**
 * AJAX handler untuk menampilkan form edit kabupaten/kota
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    if (!isset($_POST['id_kota']) || empty($_POST['id_kota'])) {
        throw new Exception('ID Kota tidak valid');
    }
    
    $id_kota = intval($_POST['id_kota']);
    
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Query untuk mendapatkan data kabupaten/kota dengan filter session
    $sql = "SELECT * FROM kab_kota WHERE id_kota = ? AND provinsi_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $id_kota, $provinsi_id_session);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        throw new Exception('Data kabupaten/kota tidak ditemukan atau Anda tidak memiliki akses');
    }
    
    $row = $result->fetch_assoc();
    
    ?>
    
    <!-- Hidden field untuk ID -->
    <input type="hidden" name="id_kota" value="<?php echo $row['id_kota']; ?>">
    <input type="hidden" name="provinsi_id" value="<?php echo $row['provinsi_id']; ?>">
    
    <div class="row">
        <!-- Kolom Kiri -->
        <div class="col-md-6">
            <div class="form-group">
                <label for="edit_kota_id">Kode Kabupaten/Kota <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="edit_kota_id" name="kota_id" 
                       value="<?php echo htmlspecialchars($row['kota_id']); ?>" required maxlength="10">
                <small class="form-text text-muted">Kode unik kabupaten/kota</small>
            </div>
            
            <div class="form-group">
                <label for="edit_nm_kota">Nama Kabupaten/Kota <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="edit_nm_kota" name="nm_kota" 
                       value="<?php echo htmlspecialchars($row['nm_kota']); ?>" required maxlength="50">
            </div>
        </div>
        
        <!-- Kolom Kanan -->
        <div class="col-md-6">
            <div class="form-group">
                <label for="edit_kd_user">Kode User</label>
                <input type="text" class="form-control" id="edit_kd_user" name="kd_user" 
                       value="<?php echo htmlspecialchars($row['kd_user']); ?>" maxlength="25">
                <small class="form-text text-muted">Kode user yang mengelola data ini</small>
            </div>
            
            <div class="form-group">
                <label>Provinsi</label>
                <input type="text" class="form-control" value="<?php 
                    // Ambil nama provinsi
                    $provinsi_query = "SELECT nama_provinsi FROM provinsi WHERE provinsi_id = ?";
                    $stmt_provinsi = $conn->prepare($provinsi_query);
                    $stmt_provinsi->bind_param("i", $row['provinsi_id']);
                    $stmt_provinsi->execute();
                    $provinsi_result = $stmt_provinsi->get_result();
                    if ($provinsi_result->num_rows > 0) {
                        $provinsi_row = $provinsi_result->fetch_assoc();
                        echo htmlspecialchars($provinsi_row['nama_provinsi']);
                    } else {
                        echo 'Provinsi tidak ditemukan';
                    }
                ?>" readonly>
                <small class="form-text text-muted">Provinsi tidak dapat diubah</small>
            </div>
        </div>
    </div>
    
    <?php
    
} catch (Exception $e) {
    echo '<div class="alert alert-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
