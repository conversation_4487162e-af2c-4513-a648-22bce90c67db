<?php
require_once '../../../koneksi.php';
require_once '../../../check_session.php';

// Validasi level akses
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Ambil NPSN dari POST
    $npsn = trim($_POST['npsn'] ?? '');
    
    if (empty($npsn)) {
        echo json_encode([
            'success' => false,
            'message' => 'NPSN tidak boleh kosong'
        ]);
        exit;
    }
    
    // Ambil provinsi_id dari session
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Query untuk validasi NPSN sekolah PAUD
    $query = "SELECT 
                s.sekolah_id,
                s.nama_sekolah,
                s.npsn,
                s.rumpun,
                j.nm_jenjang,
                kk.nm_kota
              FROM sekolah s
              LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
              LEFT JOIN kab_kota kk ON s.kota_id = kk.kota_id
              WHERE s.npsn = ? 
                AND s.provinsi_id = ?
                AND s.soft_delete = '1'";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param('si', $npsn, $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => 'NPSN tidak ditemukan atau tidak terdaftar di provinsi Anda'
        ]);
        exit;
    }
    
    $sekolah = $result->fetch_assoc();
    
    // Validasi apakah sekolah PAUD
    if (strtolower($sekolah['rumpun']) !== 'paud') {
        echo json_encode([
            'success' => false,
            'message' => 'Sekolah ini bukan sekolah PAUD. Rumpun: ' . $sekolah['rumpun']
        ]);
        exit;
    }
    
    // Jika valid, return data sekolah
    echo json_encode([
        'success' => true,
        'data' => [
            'sekolah_id' => $sekolah['sekolah_id'],
            'nama_sekolah' => $sekolah['nama_sekolah'],
            'npsn' => $sekolah['npsn'],
            'jenjang' => $sekolah['nm_jenjang'],
            'kota' => $sekolah['nm_kota']
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Validate NPSN Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan saat validasi NPSN'
    ]);
}

$conn->close();
?>
