<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // DataTables parameters
    $draw = isset($_POST['draw']) ? intval($_POST['draw']) : 1;
    $start = isset($_POST['start']) ? intval($_POST['start']) : 0;
    $length = isset($_POST['length']) ? intval($_POST['length']) : 25;
    $search_value = isset($_POST['search']['value']) ? $_POST['search']['value'] : '';
    $order_column = isset($_POST['order'][0]['column']) ? intval($_POST['order'][0]['column']) : 2;
    $order_dir = isset($_POST['order'][0]['dir']) ? $_POST['order'][0]['dir'] : 'asc';
    
    // Column mapping untuk ordering
    $columns = [
        0 => 'mp.id_mapping',
        1 => 's.npsn',
        2 => 's.nama_sekolah',
        3 => 'j.nm_jenjang',
        4 => 'kk.nm_kota',
        5 => 'a1.nia1',
        6 => 'a1.nm_asesor1',
        7 => 'a2.nia2',
        8 => 'a2.nm_asesor2',
        9 => 'mp.tahun_akreditasi',
        10 => 'mp.tahap'
    ];
    
    $order_by = isset($columns[$order_column]) ? $columns[$order_column] : 's.nama_sekolah';
    
    // Base query dengan LEFT JOIN untuk semua tabel terkait dan filter tahun akreditasi
    $base_query = "FROM mapping_paud_validasi mp
                   LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
                   LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                   LEFT JOIN kab_kota kk ON s.kota_id = kk.kota_id
                   LEFT JOIN asesor_1 a1 ON mp.kd_asesor1 = a1.kd_asesor1
                   LEFT JOIN asesor_2 a2 ON mp.kd_asesor2 = a2.kd_asesor2
                   LEFT JOIN mapping_paud_validasi_tahun mpvt ON mp.tahun_akreditasi = mpvt.nama_tahun
                   WHERE mp.provinsi_id = ?
                     AND mp.tahun_akreditasi = mpvt.nama_tahun
                     AND mpvt.provinsi_id = ?
                     AND s.rumpun = 'paud'
                     AND s.soft_delete = '1'";
    
    // Search condition
    $search_condition = "";
    $search_params = [$provinsi_id, $provinsi_id]; // Dua parameter provinsi_id untuk WHERE clause

    if (!empty($search_value)) {
        $search_condition = " AND (s.npsn LIKE ? OR s.nama_sekolah LIKE ? OR j.nm_jenjang LIKE ? OR
                             kk.nm_kota LIKE ? OR a1.nia1 LIKE ? OR a1.nm_asesor1 LIKE ? OR
                             a2.nia2 LIKE ? OR a2.nm_asesor2 LIKE ? OR mp.tahun_akreditasi LIKE ? OR
                             mp.tahap LIKE ?)";
        $search_term = "%$search_value%";
        for ($i = 0; $i < 10; $i++) {
            $search_params[] = $search_term;
        }
    }
    
    // Count total records
    $count_query = "SELECT COUNT(*) as total " . $base_query . $search_condition;
    $count_stmt = $conn->prepare($count_query);

    // Build parameter types: 'ii' untuk 2 provinsi_id, lalu 's' untuk search terms
    $param_types = 'ii' . str_repeat('s', count($search_params) - 2);
    $count_stmt->bind_param($param_types, ...$search_params);
    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $total_records = $count_result->fetch_assoc()['total'];

    // Main data query
    $data_query = "SELECT
                      mp.id_mapping,
                      s.npsn,
                      s.nama_sekolah,
                      j.nm_jenjang,
                      kk.nm_kota,
                      a1.nia1,
                      a1.nm_asesor1,
                      a2.nia2,
                      a2.nm_asesor2,
                      mp.tahun_akreditasi,
                      mp.tahap
                   " . $base_query . $search_condition . "
                   ORDER BY $order_by $order_dir
                   LIMIT $start, $length";

    $data_stmt = $conn->prepare($data_query);
    $data_stmt->bind_param($param_types, ...$search_params);
    $data_stmt->execute();
    $data_result = $data_stmt->get_result();
    
    $data = [];
    while ($row = $data_result->fetch_assoc()) {
        $data[] = [
            'id_mapping' => $row['id_mapping'],
            'npsn' => $row['npsn'] ?: '-',
            'nama_sekolah' => $row['nama_sekolah'] ?: '-',
            'nm_jenjang' => $row['nm_jenjang'] ?: '-',
            'nm_kota' => $row['nm_kota'] ?: '-',
            'nia1' => $row['nia1'] ?: '-',
            'nm_asesor1' => $row['nm_asesor1'] ?: '-',
            'nia2' => $row['nia2'] ?: '-',
            'nm_asesor2' => $row['nm_asesor2'] ?: '-',
            'tahun_akreditasi' => $row['tahun_akreditasi'] ?: '-',
            'tahap' => $row['tahap'] ?: '-'
        ];
    }
    
    // Response untuk DataTables
    $response = [
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $total_records,
        'data' => $data
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log("Get Mapping Validasi Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'draw' => isset($_POST['draw']) ? intval($_POST['draw']) : 1,
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => 'Terjadi kesalahan saat memuat data'
    ]);
}

$conn->close();
?>
