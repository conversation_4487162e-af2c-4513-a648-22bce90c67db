<?php
/**
 * AJAX handler untuk menampilkan detail riwayat nilai akreditasi sekolah
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    if (!isset($_POST['sekolah_id']) || empty($_POST['sekolah_id'])) {
        throw new Exception('ID Sekolah tidak valid');
    }
    
    $sekolah_id = intval($_POST['sekolah_id']);
    
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Query untuk mendapatkan info sekolah
    $sekolah_query = "SELECT s.npsn, s.nama_sekolah, j.nm_jenjang, k.nm_kota, pr.nama_provinsi
                      FROM sekolah s
                      LEFT JOIN jenjang j ON s.jenjang_id = j.id_jenjang
                      LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
                      LEFT JOIN provinsi pr ON s.provinsi_id = pr.provinsi_id
                      WHERE s.sekolah_id = $sekolah_id AND s.rumpun = 'dasmen'";
    
    $result_sekolah = $conn->query($sekolah_query);
    
    if ($result_sekolah->num_rows == 0) {
        throw new Exception('Data sekolah tidak ditemukan');
    }
    
    $sekolah_data = $result_sekolah->fetch_assoc();
    
    // Query untuk mendapatkan semua riwayat nilai akreditasi sekolah dengan filter session
    $nilai_query = "SELECT ha.*,
                           DATE_FORMAT(ha.tgl_sk_penetapan, '%d-%m-%Y') as tgl_sk_formatted,
                           j2pa.nm_prog_ahli
                    FROM hasil_akreditasi ha
                    LEFT JOIN jurusan_2_prog_ahli j2pa ON ha.id_prog_ahli = j2pa.id_prog_ahli
                    WHERE ha.sekolah_id = $sekolah_id
                      AND ha.provinsi_id = $provinsi_id_session
                    ORDER BY ha.tahun_akreditasi DESC";
    
    $result_nilai = $conn->query($nilai_query);
    
    ?>
    
    <!-- Info Sekolah -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless mb-0">
                                <tr>
                                    <td width="30%"><strong>NPSN</strong></td>
                                    <td width="5%">:</td>
                                    <td><?php echo htmlspecialchars($sekolah_data['npsn']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Nama Sekolah</strong></td>
                                    <td>:</td>
                                    <td><?php echo htmlspecialchars($sekolah_data['nama_sekolah']); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless mb-0">
                                <tr>
                                    <td width="30%"><strong>Jenjang</strong></td>
                                    <td width="5%">:</td>
                                    <td><?php echo htmlspecialchars($sekolah_data['nm_jenjang'] ?: '-'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Kab/Kota</strong></td>
                                    <td>:</td>
                                    <td><?php echo htmlspecialchars($sekolah_data['nm_kota'] ?: '-'); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Riwayat Nilai Akreditasi -->
    <div class="row">
        <div class="col-12">
            <h5><i class="fas fa-history"></i> Riwayat Nilai Akreditasi</h5>
            
            <?php if ($result_nilai->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped" width="100%">
                        <thead class="bg-primary text-white">
                            <tr>
                                <th class="text-center">No</th>
                                <th class="text-center">Tahun Akreditasi</th>
                                <th class="text-center">Tahun Berakhir</th>
                                <th class="text-center">Nilai Akhir</th>
                                <th class="text-center">Peringkat</th>
                                <th class="text-center">Status</th>
                                <th class="text-center">Tanggal SK</th>
                                <th class="text-center">Nomor SK</th>
                                <th class="text-center">Untuk Akreditasi</th>
                                <th class="text-center">Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $no = 1;
                            while ($row = $result_nilai->fetch_assoc()): 
                                // Format peringkat badge
                                $peringkat_badge = 'badge-secondary';
                                if ($row['peringkat'] == 'A') {
                                    $peringkat_badge = 'badge-success';
                                } elseif ($row['peringkat'] == 'B') {
                                    $peringkat_badge = 'badge-info';
                                } elseif ($row['peringkat'] == 'C') {
                                    $peringkat_badge = 'badge-warning';
                                } elseif ($row['peringkat'] == 'TT') {
                                    $peringkat_badge = 'badge-danger';
                                }
                                
                                // Format status badge
                                $status_badge = 'badge-warning';
                                if ($row['status'] == 'Terakreditasi') {
                                    $status_badge = 'badge-success';
                                } elseif ($row['status'] == 'Tidak Terakreditasi') {
                                    $status_badge = 'badge-danger';
                                }
                            ?>
                            <tr>
                                <td class="text-center"><?php echo $no++; ?></td>
                                <td class="text-center">
                                    <strong><?php echo $row['tahun_akreditasi']; ?></strong>
                                </td>
                                <td class="text-center"><?php echo $row['tahun_berakhir']; ?></td>
                                <td class="text-center">
                                    <span class="badge badge-primary"><?php echo $row['nilai_akhir']; ?></span>
                                </td>
                                <td class="text-center">
                                    <span class="badge <?php echo $peringkat_badge; ?>"><?php echo $row['peringkat']; ?></span>
                                </td>
                                <td class="text-center">
                                    <span class="badge <?php echo $status_badge; ?>"><?php echo $row['status']; ?></span>
                                </td>
                                <td class="text-center"><?php echo $row['tgl_sk_formatted'] ?: '-'; ?></td>
                                <td class="text-center"><?php echo htmlspecialchars($row['no_sk'] ?: '-'); ?></td>
                                <td class="text-center"><?php echo htmlspecialchars($row['nm_prog_ahli'] ?: '-'); ?></td>
                                <td class="text-center">
                                    <button class="btn btn-warning btn-sm mr-1" onclick="editNilaiFromDetail(<?php echo $row['id_hasil_akreditasi']; ?>)" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger btn-sm" onclick="deleteNilaiFromDetail(<?php echo $row['id_hasil_akreditasi']; ?>, '<?php echo htmlspecialchars($row['tahun_akreditasi']); ?>')" title="Hapus">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Belum ada data nilai akreditasi untuk sekolah ini.
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <?php
    
} catch (Exception $e) {
    echo '<div class="alert alert-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
