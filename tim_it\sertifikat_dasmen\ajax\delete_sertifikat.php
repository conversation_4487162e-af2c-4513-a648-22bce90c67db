<?php
session_start();
require_once '../../../koneksi.php';

// Cek session dan level
if (!isset($_SESSION['kd_user']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Ambil provinsi_id dari session
$provinsi_id = $_SESSION['provinsi_id'];

try {
    // Cek koneksi database
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Validasi input
    if (!isset($_POST['sertifikat_id']) || !isset($_POST['nama_file'])) {
        echo json_encode(['success' => false, 'message' => 'Parameter tidak lengkap']);
        exit;
    }

    $sertifikat_id = intval($_POST['sertifikat_id']);
    $nama_file = $conn->real_escape_string($_POST['nama_file']);

    if ($sertifikat_id <= 0 || empty($nama_file)) {
        echo json_encode(['success' => false, 'message' => 'Parameter tidak valid']);
        exit;
    }

    // Cek apakah sertifikat ada dan milik provinsi yang sama
    $check_query = "SELECT s.sertifikat_id, s.nama_file, sk.provinsi_id
                    FROM sertifikat s
                    LEFT JOIN sekolah sk ON s.sekolah_id = sk.sekolah_id
                    WHERE s.sertifikat_id = $sertifikat_id
                      AND s.provinsi_id = $provinsi_id";
    
    $check_result = $conn->query($check_query);

    if (!$check_result || $check_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Sertifikat tidak ditemukan atau tidak memiliki akses']);
        exit;
    }

    $sertifikat_data = $check_result->fetch_assoc();

    // Path file yang akan dihapus
    $file_path = '../../../../simak/files/file_sertifikat/' . $nama_file;

    // Hapus record dari database
    $delete_query = "DELETE FROM sertifikat WHERE sertifikat_id = $sertifikat_id";
    
    if ($conn->query($delete_query)) {
        // Hapus file fisik jika ada
        if (file_exists($file_path)) {
            if (unlink($file_path)) {
                echo json_encode([
                    'success' => true, 
                    'message' => 'Sertifikat berhasil dihapus'
                ]);
            } else {
                // File tidak bisa dihapus, tapi record sudah terhapus
                error_log("Failed to delete file: $file_path");
                echo json_encode([
                    'success' => true, 
                    'message' => 'Sertifikat berhasil dihapus (file mungkin sudah tidak ada)'
                ]);
            }
        } else {
            // File tidak ada, tapi record sudah terhapus
            echo json_encode([
                'success' => true, 
                'message' => 'Sertifikat berhasil dihapus (file sudah tidak ada)'
            ]);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Gagal menghapus sertifikat dari database']);
    }

} catch (Exception $e) {
    error_log("Delete Sertifikat Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
}

$conn->close();
?>
