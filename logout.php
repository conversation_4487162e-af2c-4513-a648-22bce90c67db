<?php
// Start session
session_start();

// <PERSON>k apakah user sudah login
if (isset($_SESSION['kd_user'])) {

    // Hapus semua session
    session_unset();
    session_destroy();

    // Hapus cookie remember me jika ada
    if (isset($_COOKIE['sim4k_remember'])) {
        setcookie('sim4k_remember', '', time() - 3600, "/");
    }

    // Set pesan sukses logout
    session_start();
    $_SESSION['success_message'] = "Anda telah berhasil logout dari sistem.";
}

// Redirect ke halaman login
header('Location: login.php');
exit();
?>
