$(document).ready(function() {
    // Initialize DataTable
    initDataTable();
    
    // Event handlers
    initEventHandlers();
});

function initDataTable() {
    $('#table-mapping-visitasi').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: 'ajax/get_mapping_visitasi.php',
            type: 'POST'
        },
        columns: [
            { 
                data: null,
                orderable: false,
                searchable: false,
                render: function(data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { data: 'npsn' },
            {
                data: 'nama_sekolah',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { data: 'nm_jenjang' },
            { data: 'nm_kota' },
            { data: 'nia1' },
            {
                data: 'nm_asesor1',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { data: 'nia2' },
            {
                data: 'nm_asesor2',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { 
                data: 'tahun_akreditasi',
                className: 'text-center'
            },
            { 
                data: 'tahap',
                className: 'text-center',
                render: function(data, type, row) {
                    if (data) {
                        var badgeClass = '';
                        switch(data) {
                            case '1':
                                badgeClass = 'badge-primary';
                                break;
                            case '2':
                                badgeClass = 'badge-success';
                                break;
                            case '3':
                                badgeClass = 'badge-warning';
                                break;
                            default:
                                badgeClass = 'badge-secondary';
                        }
                        return '<span class="badge ' + badgeClass + ' badge-tahap">' + data + '</span>';
                    }
                    return '-';
                }
            },
            { 
                data: null,
                orderable: false,
                searchable: false,
                className: 'text-center',
                render: function(data, type, row) {
                    return `
                        <button type="button" class="btn btn-info btn-sm btn-action btn-detail-mapping"
                                data-mapping-id="${row.id_mapping}"
                                title="Detail Mapping Visitasi">
                            <i class="fas fa-eye"></i>
                        </button>
                    `;
                }
            }
        ],
        order: [[9, 'desc']], // Order by tahun_akreditasi descending
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: "Memuat data...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(disaring dari _MAX_ total data)",
            paginate: {
                first: "Pertama",
                last: "Terakhir",
                next: "Selanjutnya",
                previous: "Sebelumnya"
            },
            emptyTable: "Tidak ada data mapping visitasi PAUD"
        },
        responsive: true,
        autoWidth: true,
        scrollX: true
    });
}

function initEventHandlers() {
    // Button click handlers
    $('#btn-input-mapping-visitasi').on('click', function() {
        inputMappingVisitasi();
    });

    $('#btn-export-excel').on('click', function() {
        exportMappingVisitasiToExcel();
    });

    $('#btn-import-excel').on('click', function() {
        importExcel();
    });

    $('#btn-tahun-akreditasi').on('click', function() {
        filterTahunAkreditasi();
    });

    // Auto lookup NPSN Sekolah
    $('#npsn_sekolah').on('input', function() {
        var npsn = $(this).val().trim();
        if (npsn.length >= 8) { // NPSN minimal 8 digit
            lookupSekolah(npsn);
        } else {
            $('#info-sekolah').hide();
            $('#sekolah_id').val('');
        }
    });

    // Auto lookup NIA Asesor A
    $('#nia_asesor_a').on('input', function() {
        var nia = $(this).val().trim();
        if (nia.length >= 3) {
            lookupAsesor1(nia);
        } else {
            $('#info-asesor-a').hide();
            $('#kd_asesor_a').val('');
        }
    });

    // Auto lookup NIA Asesor B
    $('#nia_asesor_b').on('input', function() {
        var nia = $(this).val().trim();
        if (nia.length >= 3) {
            lookupAsesor2(nia);
        } else {
            $('#info-asesor-b').hide();
            $('#kd_asesor_b').val('');
        }
    });

    // Form submit handler
    $('#form-input-mapping-visitasi').on('submit', function(e) {
        e.preventDefault();
        submitInputMappingVisitasi();
    });

    // Form submit handler untuk tahun akreditasi
    $('#form-tahun-akreditasi').on('submit', function(e) {
        e.preventDefault();
        submitTahunAkreditasi();
    });

    // Event handlers untuk action buttons di modal detail
    $(document).on('click', '#btn-edit-tanggal-kegiatan', function(e) {
        e.preventDefault();
        console.log('Edit tanggal kegiatan clicked');
        openEditTanggalKegiatanModal();
    });

    $(document).on('click', '#btn-edit-asesor-perubahan', function(e) {
        e.preventDefault();
        console.log('Edit asesor perubahan clicked');
        openEditAsesorPerubahanModal();
    });

    $(document).on('click', '#btn-download-surat-tugas', function(e) {
        e.preventDefault();
        console.log('Download surat tugas clicked');
        downloadSuratTugasVisitasi();
    });

    $(document).on('click', '#btn-hapus-mapping', function(e) {
        e.preventDefault();
        console.log('Hapus mapping clicked');
        openKonfirmasiHapusMappingVisitasiModal();
    });

    // Event handler untuk konfirmasi hapus mapping visitasi
    $(document).on('click', '#btn-konfirmasi-hapus-visitasi', function(e) {
        e.preventDefault();
        submitHapusMappingVisitasi();
    });

    // Event handler untuk button detail mapping di DataTable
    $(document).on('click', '.btn-detail-mapping', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const mappingId = $(this).data('mapping-id');
        console.log('Detail button clicked for mapping ID:', mappingId);

        if (mappingId) {
            detailMappingVisitasi(mappingId);
        } else {
            console.error('No mapping ID found');
            showAlert('error', 'ID mapping tidak ditemukan');
        }

        return false;
    });

    // Event handler untuk file download links
    $(document).on('click', '.file-download-link', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const fileUrl = $(this).data('file-url');
        const fileName = $(this).data('file-name');

        console.log('File download clicked:', fileName, 'URL:', fileUrl);

        if (fileUrl && fileName) {
            openFileInNewTab(fileUrl, fileName);
        } else {
            console.error('No file URL or name found');
            showAlert('error', 'Data file tidak valid');
        }

        return false;
    });

    // Form submit handler untuk edit tanggal kegiatan
    $('#form-edit-tanggal-kegiatan').on('submit', function(e) {
        e.preventDefault();
        submitEditTanggalKegiatan();
    });

    // Form submit handler untuk edit asesor perubahan
    $('#form-edit-asesor-perubahan').on('submit', function(e) {
        e.preventDefault();
        submitEditAsesorPerubahan();
    });

    // Auto lookup NIA Asesor A di edit modal
    $('#edit_nia_asesor_a').on('input', function() {
        var nia = $(this).val().trim();
        if (nia.length >= 3) {
            editLookupAsesor1(nia);
        } else {
            $('#edit-info-asesor-a').hide();
            $('#edit_kd_asesor_a').val('');
        }
    });

    // Auto lookup NIA Asesor B di edit modal
    $('#edit_nia_asesor_b').on('input', function() {
        var nia = $(this).val().trim();
        if (nia.length >= 3) {
            editLookupAsesor2(nia);
        } else {
            $('#edit-info-asesor-b').hide();
            $('#edit_kd_asesor_b').val('');
        }
    });
}

function detailMappingVisitasi(mappingId) {
    console.log('Detail mapping visitasi clicked - ID:', mappingId);

    // Prevent any default behavior
    event.preventDefault();
    event.stopPropagation();

    // Validate mapping ID
    if (!mappingId || mappingId <= 0) {
        console.error('Invalid mapping ID:', mappingId);
        showAlert('error', 'ID mapping tidak valid');
        return false;
    }

    // Load detail data via AJAX
    loadDetailMappingVisitasi(mappingId);

    return false;
}

function inputMappingVisitasi() {
    console.log('Input mapping visitasi clicked');

    // Reset form
    $('#form-input-mapping-visitasi')[0].reset();

    // Clear all info displays
    $('#info-sekolah').hide();
    $('#info-asesor-a').hide();
    $('#info-asesor-b').hide();

    // Clear hidden fields
    $('#sekolah_id').val('');
    $('#kd_asesor_a').val('');
    $('#kd_asesor_b').val('');

    // Show modal
    $('#modal-input-mapping-visitasi').modal('show');
}

function exportExcel() {
    console.log('Export Excel clicked');
    
    // TODO: Implement Excel export functionality
    showAlert('info', 'Fitur export Excel akan segera tersedia');
}

function importExcel() {
    console.log('Import Excel clicked');
    
    // TODO: Implement Excel import functionality
    showAlert('info', 'Fitur import Excel akan segera tersedia');
}

function filterTahunAkreditasi() {
    console.log('Filter tahun akreditasi clicked');

    // Load current tahun akreditasi
    loadCurrentTahunAkreditasi();

    // Show modal
    $('#modal-tahun-akreditasi').modal('show');
}

function showAlert(type, message) {
    var alertClass = '';
    var iconClass = '';
    
    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            iconClass = 'fa-check-circle';
            break;
        case 'error':
            alertClass = 'alert-danger';
            iconClass = 'fa-exclamation-triangle';
            break;
        case 'warning':
            alertClass = 'alert-warning';
            iconClass = 'fa-exclamation-triangle';
            break;
        case 'info':
            alertClass = 'alert-info';
            iconClass = 'fa-info-circle';
            break;
        default:
            alertClass = 'alert-info';
            iconClass = 'fa-info-circle';
    }
    
    var alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${iconClass}"></i> ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `;
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert at the top of content
    $('.content-wrapper .content').prepend(alertHtml);
    
    // Auto hide after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

// Auto lookup functions
function lookupSekolah(npsn) {
    $.ajax({
        url: 'ajax/lookup_sekolah.php',
        type: 'POST',
        data: { npsn: npsn },
        dataType: 'json',
        success: function(response) {
            if (response.success && response.data) {
                // Show sekolah info
                $('#info-nama-sekolah').text(response.data.nama_sekolah);
                $('#info-jenjang-sekolah').text(response.data.nm_jenjang);
                $('#info-kota-sekolah').text(response.data.nm_kota);
                $('#info-sekolah').show();

                // Set hidden field
                $('#sekolah_id').val(response.data.sekolah_id);

                console.log('Sekolah found:', response.data);
            } else {
                $('#info-sekolah').hide();
                $('#sekolah_id').val('');
            }
        },
        error: function() {
            $('#info-sekolah').hide();
            $('#sekolah_id').val('');
        }
    });
}

function lookupAsesor1(nia) {
    $.ajax({
        url: 'ajax/lookup_asesor1.php',
        type: 'POST',
        data: { nia: nia },
        dataType: 'json',
        success: function(response) {
            if (response.success && response.data) {
                // Show asesor A info
                $('#info-nama-asesor-a').text(response.data.nm_asesor1);
                $('#info-kota-asesor-a').text(response.data.nm_kota + ' - ' + response.data.rumpun);
                $('#info-asesor-a').show();

                // Set hidden field
                $('#kd_asesor_a').val(response.data.kd_asesor1);

                console.log('Asesor A found:', response.data);
            } else {
                $('#info-asesor-a').hide();
                $('#kd_asesor_a').val('');
            }
        },
        error: function() {
            $('#info-asesor-a').hide();
            $('#kd_asesor_a').val('');
        }
    });
}

function lookupAsesor2(nia) {
    $.ajax({
        url: 'ajax/lookup_asesor2.php',
        type: 'POST',
        data: { nia: nia },
        dataType: 'json',
        success: function(response) {
            if (response.success && response.data) {
                // Show asesor B info
                $('#info-nama-asesor-b').text(response.data.nm_asesor2);
                $('#info-kota-asesor-b').text(response.data.nm_kota + ' - ' + response.data.rumpun);
                $('#info-asesor-b').show();

                // Set hidden field
                $('#kd_asesor_b').val(response.data.kd_asesor2);

                console.log('Asesor B found:', response.data);
            } else {
                $('#info-asesor-b').hide();
                $('#kd_asesor_b').val('');
            }
        },
        error: function() {
            $('#info-asesor-b').hide();
            $('#kd_asesor_b').val('');
        }
    });
}

function submitInputMappingVisitasi() {
    console.log('Submitting input mapping visitasi');

    // Validasi dual asesor tidak boleh sama
    var niaA = $('#nia_asesor_a').val().trim();
    var niaB = $('#nia_asesor_b').val().trim();

    if (niaA && niaB && niaA === niaB) {
        showAlert('error', 'NIA Asesor A dan Asesor B tidak boleh sama');
        return;
    }

    // Validasi required fields
    var sekolahId = $('#sekolah_id').val();
    var kdAsesorA = $('#kd_asesor_a').val();
    var kdAsesorB = $('#kd_asesor_b').val();
    var tahunAkreditasi = $('#tahun_akreditasi').val().trim();
    var tahap = $('#tahap').val().trim();

    if (!sekolahId) {
        showAlert('error', 'NPSN Sekolah harus diisi dan valid');
        return;
    }
    if (!kdAsesorA) {
        showAlert('error', 'NIA Asesor A harus diisi dan valid');
        return;
    }
    if (!kdAsesorB) {
        showAlert('error', 'NIA Asesor B harus diisi dan valid');
        return;
    }
    if (!tahunAkreditasi) {
        showAlert('error', 'Tahun Akreditasi harus diisi');
        return;
    }
    if (!tahap) {
        showAlert('error', 'Tahap Ke harus diisi');
        return;
    }

    // Submit form via AJAX
    var formData = new FormData($('#form-input-mapping-visitasi')[0]);

    $.ajax({
        url: 'ajax/simpan_mapping_visitasi.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        beforeSend: function() {
            // Disable submit button
            $('#form-input-mapping-visitasi button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
        },
        success: function(response) {
            if (response.success) {
                // Close modal
                $('#modal-input-mapping-visitasi').modal('hide');

                // Reload DataTable tanpa refresh browser
                $('#table-mapping-visitasi').DataTable().ajax.reload(null, false);

                // Silent success - no notification
                console.log('Mapping visitasi berhasil disimpan:', response.data);
            } else {
                showAlert('error', response.message || 'Gagal menyimpan data mapping visitasi');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error submitting mapping visitasi:', error);
            showAlert('error', 'Terjadi kesalahan saat menyimpan data mapping visitasi');
        },
        complete: function() {
            // Re-enable submit button
            $('#form-input-mapping-visitasi button[type="submit"]').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Data');
        }
    });
}

function loadCurrentTahunAkreditasi() {
    console.log('Loading current tahun akreditasi');

    $.ajax({
        url: 'ajax/get_tahun_akreditasi.php',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.success && response.data) {
                // Set current year in form
                $('#nama_tahun').val(response.data.nama_tahun);

                // Show current year info
                $('#current-year-display').text(response.data.nama_tahun);
                $('#info-current-year').show();

                console.log('Current tahun akreditasi loaded:', response.data.nama_tahun);
            } else {
                // No current year, set default
                $('#nama_tahun').val('');
                $('#info-current-year').hide();
                console.log('No current tahun akreditasi found');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading current tahun akreditasi:', error);
            $('#nama_tahun').val('');
            $('#info-current-year').hide();
        }
    });
}

function submitTahunAkreditasi() {
    console.log('Submitting tahun akreditasi');

    var namaTahun = $('#nama_tahun').val().trim();

    if (!namaTahun) {
        showAlert('error', 'Tahun Akreditasi harus diisi');
        return;
    }

    var formData = new FormData($('#form-tahun-akreditasi')[0]);

    $.ajax({
        url: 'ajax/update_tahun_akreditasi.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        beforeSend: function() {
            // Disable submit button
            $('#form-tahun-akreditasi button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
        },
        success: function(response) {
            if (response.success) {
                // Close modal
                $('#modal-tahun-akreditasi').modal('hide');

                // Reload DataTable tanpa refresh browser untuk reflect perubahan filter
                $('#table-mapping-visitasi').DataTable().ajax.reload(null, false);

                // Silent success - no notification
                console.log('Tahun akreditasi berhasil diupdate:', response.data);
            } else {
                showAlert('error', response.message || 'Gagal mengupdate tahun akreditasi');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error submitting tahun akreditasi:', error);
            showAlert('error', 'Terjadi kesalahan saat mengupdate tahun akreditasi');
        },
        complete: function() {
            // Re-enable submit button
            $('#form-tahun-akreditasi button[type="submit"]').prop('disabled', false).html('<i class="fas fa-save"></i> Update Filter');
        }
    });
}

function loadDetailMappingVisitasi(mappingId) {
    console.log('Loading detail mapping visitasi ID:', mappingId);

    $.ajax({
        url: 'ajax/get_detail_mapping_visitasi.php',
        type: 'POST',
        data: { id_mapping: mappingId },
        dataType: 'json',
        timeout: 10000, // 10 second timeout
        success: function(response) {
            console.log('AJAX response received:', response);

            if (response && response.success && response.data) {
                try {
                    // Populate modal dengan data
                    populateDetailModal(response.data);

                    // Show modal setelah data loaded
                    $('#modal-detail-mapping-visitasi').modal('show');

                    console.log('Detail mapping visitasi loaded successfully');
                } catch (error) {
                    console.error('Error populating modal:', error);
                    showAlert('error', 'Terjadi kesalahan saat menampilkan data');
                }
            } else {
                console.error('Invalid response:', response);
                showAlert('error', response.message || 'Gagal memuat detail mapping visitasi');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', {
                status: status,
                error: error,
                responseText: xhr.responseText,
                statusCode: xhr.status
            });

            let errorMessage = 'Terjadi kesalahan saat memuat detail mapping visitasi';
            if (status === 'timeout') {
                errorMessage = 'Request timeout - silakan coba lagi';
            } else if (xhr.status === 404) {
                errorMessage = 'File tidak ditemukan';
            } else if (xhr.status === 500) {
                errorMessage = 'Server error - silakan hubungi administrator';
            }

            showAlert('error', errorMessage);
        }
    });
}

function populateDetailModal(data) {
    console.log('Populating detail modal with data:', data);

    // Data Sekolah
    $('#detail-npsn').text(data.npsn || '-');
    $('#detail-nama-sekolah').text(data.nama_sekolah || '-');
    $('#detail-jenjang').text(data.nm_jenjang || '-');
    $('#detail-kab-kota-sekolah').text(data.nm_kota_sekolah || '-');
    $('#detail-nama-kepsek').text(data.nama_kepsek || '-');
    $('#detail-hp-kepsek').text(data.no_hp_kepsek || '-');
    $('#detail-wa-kepsek').text(data.no_wa_kepsek || '-');

    // Data Asesor A
    $('#detail-nia-asesor-a').text(data.nia1 || '-');
    $('#detail-nama-asesor-a').text(data.nm_asesor1 || '-');
    $('#detail-hp-asesor-a').text(data.no_hp_asesor1 || '-');
    $('#detail-kab-kota-asesor-a').text(data.nm_kota_asesor1 || '-');

    // Data Asesor B
    $('#detail-nia-asesor-b').text(data.nia2 || '-');
    $('#detail-nama-asesor-b').text(data.nm_asesor2 || '-');
    $('#detail-hp-asesor-b').text(data.no_hp_asesor2 || '-');
    $('#detail-kab-kota-asesor-b').text(data.nm_kota_asesor2 || '-');

    // Dokumen Unggahan dengan conditional formatting
    populateFileStatus('detail-file-pakta-1', data.file_pakta_integritas_1);
    populateFileStatus('detail-file-pakta-2', data.file_pakta_integritas_2);
    populateFileStatus('detail-file-berita-acara', data.file_berita_acara_visitasi);
    populateFileStatus('detail-file-temuan-hasil', data.file_temuan_hasil_visitasi);
    populateFileStatus('detail-file-absen-pembuka', data.file_absen_pembuka);
    populateFileStatus('detail-file-absen-penutup', data.file_absen_penutup);
    populateFileStatus('detail-file-foto-visitasi', data.file_foto_visitasi);
    populateFileStatus('detail-file-laporan-individu-1', data.file_laporan_individu_1);
    populateFileStatus('detail-file-laporan-individu-2', data.file_laporan_individu_2);
    populateFileStatus('detail-file-laporan-kelompok', data.file_laporan_kelompok);
    populateFileStatus('detail-file-penjelasan-hasil', data.file_penjelasan_hasil_akreditasi);

    // Pelaksanaan Kegiatan
    $('#detail-tgl-mulai-visitasi').text(formatDate(data.tgl_mulai_visitasi) || '-');
    $('#detail-tgl-akhir-visitasi').text(formatDate(data.tgl_akhir_visitasi) || '-');
    $('#detail-no-surat').text(data.no_surat || '-');
    $('#detail-tgl-surat').text(formatDate(data.tgl_surat) || '-');

    // Store data untuk keperluan edit functions
    $('#modal-detail-mapping-visitasi').data('mapping-data', data);

    console.log('Modal detail populated successfully with clickable file links');
}

function populateFileStatus(elementId, fileValue) {
    const element = $('#' + elementId);

    if (fileValue && fileValue.trim() !== '' && fileValue !== '0000-00-00') {
        // File ada - clickable link hijau dengan cursor pointer
        const fileUrl = `../../../simak/files/upload_file_hasil_visitasi_paud/${fileValue}`;
        element.html(`
            <a href="#" class="badge badge-success file-download-link"
               data-file-url="${fileUrl}"
               data-file-name="${fileValue}"
               style="cursor: pointer; text-decoration: none;">
                <i class="fas fa-download"></i> Sudah Upload
            </a>
        `);
    } else {
        // File kosong - merah dengan text putih (non-clickable)
        element.html('<span class="badge badge-danger" style="cursor: default;">Belum Upload</span>');
    }
}

function formatDate(dateString) {
    if (!dateString || dateString === '0000-00-00' || dateString === '' || dateString === null) {
        return '';
    }

    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            return dateString; // Return original jika tidak bisa diparse
        }

        // Format: DD/MM/YYYY
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();

        return `${day}/${month}/${year}`;
    } catch (error) {
        console.error('Error formatting date:', error);
        return dateString;
    }
}

function openFileInNewTab(fileUrl, fileName) {
    console.log('Opening file in new tab:', fileName);

    // Check if file exists first via AJAX HEAD request
    $.ajax({
        url: fileUrl,
        type: 'HEAD',
        timeout: 5000,
        success: function() {
            // File exists, open in new tab
            console.log('File exists, opening in new tab:', fileUrl);
            window.open(fileUrl, '_blank');
        },
        error: function(xhr, status, error) {
            console.error('File not found or error accessing file:', {
                status: xhr.status,
                error: error,
                url: fileUrl
            });

            // Show user-friendly error message
            if (xhr.status === 404) {
                showAlert('error', `File "${fileName}" tidak ditemukan di server`);
            } else if (status === 'timeout') {
                showAlert('error', 'Timeout saat mengakses file - silakan coba lagi');
            } else {
                showAlert('error', `Gagal mengakses file "${fileName}" - silakan hubungi administrator`);
            }
        }
    });
}

function openEditTanggalKegiatanModal() {
    console.log('Opening edit tanggal kegiatan modal');

    // Get current mapping data from detail modal
    const mappingData = $('#modal-detail-mapping-visitasi').data('mapping-data');

    if (!mappingData) {
        console.error('No mapping data found');
        showAlert('error', 'Data mapping tidak ditemukan');
        return;
    }

    // Pre-fill form dengan data current
    $('#edit_id_mapping').val(mappingData.id_mapping);
    $('#edit_tgl_mulai_visitasi').val(mappingData.tgl_mulai_visitasi || '');
    $('#edit_tgl_akhir_visitasi').val(mappingData.tgl_akhir_visitasi || '');
    $('#edit_no_surat').val(mappingData.no_surat || '');
    $('#edit_tgl_surat').val(mappingData.tgl_surat || '');

    // Show nested modal
    $('#modal-edit-tanggal-kegiatan').modal('show');

    console.log('Edit tanggal kegiatan modal opened with data:', {
        id_mapping: mappingData.id_mapping,
        tgl_mulai_visitasi: mappingData.tgl_mulai_visitasi,
        tgl_akhir_visitasi: mappingData.tgl_akhir_visitasi,
        no_surat: mappingData.no_surat,
        tgl_surat: mappingData.tgl_surat
    });
}

function submitEditTanggalKegiatan() {
    console.log('Submitting edit tanggal kegiatan');

    const formData = new FormData($('#form-edit-tanggal-kegiatan')[0]);

    $.ajax({
        url: 'ajax/update_tanggal_kegiatan.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        beforeSend: function() {
            // Disable submit button
            $('#form-edit-tanggal-kegiatan button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
        },
        success: function(response) {
            if (response.success) {
                // Close edit modal
                $('#modal-edit-tanggal-kegiatan').modal('hide');

                // Update fields di parent modal (real-time update)
                updatePelaksanaanKegiatanFields(response.data);

                // Update stored data di detail modal
                const currentData = $('#modal-detail-mapping-visitasi').data('mapping-data');
                if (currentData) {
                    currentData.tgl_mulai_visitasi = response.data.tgl_mulai_visitasi;
                    currentData.tgl_akhir_visitasi = response.data.tgl_akhir_visitasi;
                    currentData.no_surat = response.data.no_surat;
                    currentData.tgl_surat = response.data.tgl_surat;
                    $('#modal-detail-mapping-visitasi').data('mapping-data', currentData);
                }

                // Reload DataTable tabel utama tanpa refresh browser untuk reflect perubahan tanggal
                $('#table-mapping-visitasi').DataTable().ajax.reload(null, false);

                // Silent success - no notification
                console.log('Tanggal kegiatan berhasil diupdate:', response.data);
                console.log('Tabel utama mapping visitasi telah di-reload untuk reflect perubahan tanggal');
            } else {
                showAlert('error', response.message || 'Gagal mengupdate tanggal kegiatan');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error submitting edit tanggal kegiatan:', error);
            showAlert('error', 'Terjadi kesalahan saat mengupdate tanggal kegiatan');
        },
        complete: function() {
            // Re-enable submit button
            $('#form-edit-tanggal-kegiatan button[type="submit"]').prop('disabled', false).html('<i class="fas fa-save"></i> Update Perubahan');
        }
    });
}

function updatePelaksanaanKegiatanFields(data) {
    console.log('Updating pelaksanaan kegiatan fields with new data:', data);

    // Update fields di kolom "Pelaksanaan Kegiatan" dengan real-time
    $('#detail-tgl-mulai-visitasi').text(formatDate(data.tgl_mulai_visitasi) || '-');
    $('#detail-tgl-akhir-visitasi').text(formatDate(data.tgl_akhir_visitasi) || '-');
    $('#detail-no-surat').text(data.no_surat || '-');
    $('#detail-tgl-surat').text(formatDate(data.tgl_surat) || '-');

    console.log('Pelaksanaan kegiatan fields updated successfully');
}

function openEditAsesorPerubahanModal() {
    console.log('Opening edit asesor perubahan modal');

    // Get current mapping data from detail modal
    const mappingData = $('#modal-detail-mapping-visitasi').data('mapping-data');

    if (!mappingData) {
        console.error('No mapping data found');
        showAlert('error', 'Data mapping tidak ditemukan');
        return;
    }

    // Reset form dan info displays
    $('#form-edit-asesor-perubahan')[0].reset();
    $('#edit-info-asesor-a').hide();
    $('#edit-info-asesor-b').hide();
    $('#edit_kd_asesor_a').val('');
    $('#edit_kd_asesor_b').val('');

    // Pre-fill form dengan NIA current
    $('#edit_asesor_id_mapping').val(mappingData.id_mapping);
    $('#edit_nia_asesor_a').val(mappingData.nia1 || '');
    $('#edit_nia_asesor_b').val(mappingData.nia2 || '');

    // Trigger lookup untuk current NIAs jika ada
    if (mappingData.nia1) {
        editLookupAsesor1(mappingData.nia1);
    }
    if (mappingData.nia2) {
        editLookupAsesor2(mappingData.nia2);
    }

    // Show nested modal
    $('#modal-edit-asesor-perubahan').modal('show');

    console.log('Edit asesor perubahan modal opened with data:', {
        id_mapping: mappingData.id_mapping,
        nia1: mappingData.nia1,
        nia2: mappingData.nia2
    });
}

function editLookupAsesor1(nia) {
    $.ajax({
        url: 'ajax/lookup_asesor1.php',
        type: 'POST',
        data: { nia: nia },
        dataType: 'json',
        success: function(response) {
            if (response.success && response.data) {
                // Show asesor A info
                $('#edit-info-nama-asesor-a').text(response.data.nm_asesor1);
                $('#edit-info-kota-asesor-a').text(response.data.nm_kota + ' - ' + response.data.rumpun);
                $('#edit-info-asesor-a').show();

                // Set hidden field
                $('#edit_kd_asesor_a').val(response.data.kd_asesor1);

                console.log('Edit Asesor A found:', response.data);
            } else {
                $('#edit-info-asesor-a').hide();
                $('#edit_kd_asesor_a').val('');
            }
        },
        error: function() {
            $('#edit-info-asesor-a').hide();
            $('#edit_kd_asesor_a').val('');
        }
    });
}

function editLookupAsesor2(nia) {
    $.ajax({
        url: 'ajax/lookup_asesor2.php',
        type: 'POST',
        data: { nia: nia },
        dataType: 'json',
        success: function(response) {
            if (response.success && response.data) {
                // Show asesor B info
                $('#edit-info-nama-asesor-b').text(response.data.nm_asesor2);
                $('#edit-info-kota-asesor-b').text(response.data.nm_kota + ' - ' + response.data.rumpun);
                $('#edit-info-asesor-b').show();

                // Set hidden field
                $('#edit_kd_asesor_b').val(response.data.kd_asesor2);

                console.log('Edit Asesor B found:', response.data);
            } else {
                $('#edit-info-asesor-b').hide();
                $('#edit_kd_asesor_b').val('');
            }
        },
        error: function() {
            $('#edit-info-asesor-b').hide();
            $('#edit_kd_asesor_b').val('');
        }
    });
}

function submitEditAsesorPerubahan() {
    console.log('Submitting edit asesor perubahan');

    // Validasi dual asesor tidak boleh sama (jika keduanya diisi)
    var niaA = $('#edit_nia_asesor_a').val().trim();
    var niaB = $('#edit_nia_asesor_b').val().trim();

    if (niaA && niaB && niaA === niaB) {
        showAlert('error', 'NIA Asesor A dan Asesor B tidak boleh sama');
        return;
    }

    const formData = new FormData($('#form-edit-asesor-perubahan')[0]);

    $.ajax({
        url: 'ajax/update_asesor_perubahan.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        beforeSend: function() {
            // Disable submit button
            $('#form-edit-asesor-perubahan button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
        },
        success: function(response) {
            if (response.success) {
                // Close edit modal
                $('#modal-edit-asesor-perubahan').modal('hide');

                // Update fields di parent modal (real-time update)
                updateDataAsesorFields(response.data);

                // Update stored data di detail modal
                const currentData = $('#modal-detail-mapping-visitasi').data('mapping-data');
                if (currentData) {
                    // Update asesor data
                    currentData.kd_asesor1 = response.data.kd_asesor1;
                    currentData.kd_asesor2 = response.data.kd_asesor2;
                    currentData.nia1 = response.data.nia1;
                    currentData.nia2 = response.data.nia2;
                    currentData.nm_asesor1 = response.data.nm_asesor1;
                    currentData.nm_asesor2 = response.data.nm_asesor2;
                    currentData.no_hp_asesor1 = response.data.no_hp_asesor1;
                    currentData.no_hp_asesor2 = response.data.no_hp_asesor2;
                    currentData.nm_kota_asesor1 = response.data.nm_kota_asesor1;
                    currentData.nm_kota_asesor2 = response.data.nm_kota_asesor2;
                    $('#modal-detail-mapping-visitasi').data('mapping-data', currentData);
                }

                // Reload DataTable tabel utama tanpa refresh browser untuk reflect perubahan asesor
                $('#table-mapping-visitasi').DataTable().ajax.reload(null, false);

                // Silent success - no notification
                console.log('Asesor perubahan berhasil diupdate:', response.data);
                console.log('Tabel utama mapping visitasi telah di-reload untuk reflect perubahan asesor');
            } else {
                showAlert('error', response.message || 'Gagal mengupdate asesor perubahan');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error submitting edit asesor perubahan:', error);
            showAlert('error', 'Terjadi kesalahan saat mengupdate asesor perubahan');
        },
        complete: function() {
            // Re-enable submit button
            $('#form-edit-asesor-perubahan button[type="submit"]').prop('disabled', false).html('<i class="fas fa-save"></i> Update Perubahan');
        }
    });
}

function updateDataAsesorFields(data) {
    console.log('Updating data asesor fields with new data:', data);

    // Update Asesor A fields
    $('#detail-nia-asesor-a').text(data.nia1 || '-');
    $('#detail-nama-asesor-a').text(data.nm_asesor1 || '-');
    $('#detail-hp-asesor-a').text(data.no_hp_asesor1 || '-');
    $('#detail-kab-kota-asesor-a').text(data.nm_kota_asesor1 || '-');

    // Update Asesor B fields
    $('#detail-nia-asesor-b').text(data.nia2 || '-');
    $('#detail-nama-asesor-b').text(data.nm_asesor2 || '-');
    $('#detail-hp-asesor-b').text(data.no_hp_asesor2 || '-');
    $('#detail-kab-kota-asesor-b').text(data.nm_kota_asesor2 || '-');

    console.log('Data asesor fields updated successfully');
}

function downloadSuratTugasVisitasi() {
    console.log('Downloading surat tugas visitasi');

    // Get current mapping data from detail modal
    const mappingData = $('#modal-detail-mapping-visitasi').data('mapping-data');

    if (!mappingData) {
        console.error('No mapping data found');
        showAlert('error', 'Data mapping tidak ditemukan');
        return;
    }

    // Construct URL untuk surat tugas PDF
    const pdfUrl = `mapping_paud_st_visitasi.php?id_mapping=${mappingData.id_mapping}`;

    console.log('Opening surat tugas PDF:', pdfUrl);

    // Open PDF in new tab
    window.open(pdfUrl, '_blank');
}

function openKonfirmasiHapusMappingVisitasiModal() {
    console.log('Opening konfirmasi hapus mapping visitasi modal');

    // Ambil data mapping dari modal detail yang sedang terbuka
    const mappingData = $('#modal-detail-mapping-visitasi').data('mapping-data');

    if (!mappingData) {
        showAlert('error', 'Data mapping tidak ditemukan. Silakan tutup dan buka kembali modal detail.');
        return;
    }

    // Populate data di modal konfirmasi
    $('#nama-sekolah-hapus-visitasi').text(mappingData.nama_sekolah || '-');
    $('#hapus-npsn-visitasi').text(mappingData.npsn || '-');
    $('#hapus-sekolah-visitasi').text(mappingData.nama_sekolah || '-');
    $('#hapus-asesor1-visitasi').text(mappingData.nm_asesor1 || '-');
    $('#hapus-asesor2-visitasi').text(mappingData.nm_asesor2 || '-');
    $('#hapus-tahun-visitasi').text(mappingData.tahun_akreditasi || '-');
    $('#hapus-tahap-visitasi').text(mappingData.tahap || '-');

    // Simpan ID mapping yang akan dihapus
    $('#id-mapping-visitasi-akan-dihapus').val(mappingData.id_mapping);

    // Show modal dengan z-index yang lebih tinggi untuk nested modal
    $('#modal-konfirmasi-hapus-visitasi').css('z-index', 1080).modal('show');

    console.log('Konfirmasi hapus modal opened with ID:', mappingData.id_mapping, 'Sekolah:', mappingData.nama_sekolah);
}

function submitHapusMappingVisitasi() {
    console.log('Submitting hapus mapping visitasi');

    const idMapping = $('#id-mapping-visitasi-akan-dihapus').val();

    if (!idMapping) {
        showAlert('error', 'ID mapping tidak valid');
        return;
    }

    $.ajax({
        url: 'ajax/delete_mapping.php',
        type: 'POST',
        data: {
            id_mapping: idMapping
        },
        dataType: 'json',
        beforeSend: function() {
            // Disable konfirmasi button
            $('#btn-konfirmasi-hapus-visitasi').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menghapus...');
        },
        success: function(response) {
            if (response.success) {
                console.log('Mapping visitasi berhasil dihapus:', response.data);

                // Tutup modal konfirmasi hapus
                $('#modal-konfirmasi-hapus-visitasi').modal('hide');

                // Tutup modal detail
                $('#modal-detail-mapping-visitasi').modal('hide');

                // Reload DataTable tanpa refresh halaman
                $('#table-mapping-visitasi').DataTable().ajax.reload(null, false);

                // Silent success - no notification (sesuai preferensi user)
                console.log('Data mapping visitasi berhasil dihapus:', response.data);
                console.log('Tabel utama mapping visitasi telah di-reload setelah penghapusan');

            } else {
                showAlert('error', response.message || 'Gagal menghapus data mapping visitasi');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error submitting hapus mapping visitasi:', error);
            showAlert('error', 'Terjadi kesalahan saat menghapus data mapping visitasi');
        },
        complete: function() {
            // Re-enable konfirmasi button
            $('#btn-konfirmasi-hapus-visitasi').prop('disabled', false).html('<i class="fas fa-trash"></i> Ya, Hapus');
        }
    });
}

function exportMappingVisitasiToExcel() {
    console.log('Exporting mapping visitasi to Excel');

    // Show loading
    showAlert('info', 'Sedang memproses export Excel...');

    $.ajax({
        url: 'ajax/get_export_mapping_visitasi.php',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success && response.data) {
                console.log('Export data received:', response.data.length, 'records');

                // Prepare Excel data dengan 27 kolom
                var excelData = [];

                // Header row
                excelData.push([
                    'NO',
                    'NPSN',
                    'NAMA SEKOLAH',
                    'JENJANG',
                    'KABUPATEN / KOTA',
                    'NIA ASESOR A',
                    'NAMA ASESOR A',
                    'KABUPATEN KOTA ASESOR A',
                    'NIA ASESOR B',
                    'NAMA ASESOR B',
                    'KABUPATEN KOTA ASESOR B',
                    'TAHUN AKREDITASI',
                    'TAHAP',
                    'TANGGAL VISITASI',
                    'NOMOR SURAT TUGAS',
                    'TANGGAL SURAT TUGAS',
                    'FILE PAKTA INTEGRITAS ASESOR A',
                    'FILE PAKTA INTEGRITAS ASESOR B',
                    'FILE BERITA ACARA VISITASI',
                    'FILE TEMUAN HASIL VISITASI',
                    'FILE ABSEN PEMBUKA',
                    'FILE ABSEN PENUTUP',
                    'FILE FOTO VISITASI',
                    'FILE LAPORAN INDIVIDU ASESOR A',
                    'FILE LAPORAN INDIVIDU ASESOR B',
                    'FILE LAPORAN KELOMPOK',
                    'FILE PENJELASAN HASIL AKREDITASI (PHA)'
                ]);

                // Data rows
                response.data.forEach(function(row, index) {
                    excelData.push([
                        index + 1, // NO (autoincrement)
                        row.npsn || '-',
                        row.nama_sekolah || '-',
                        row.nm_jenjang || '-',
                        row.nm_kota_sekolah || '-',
                        row.nia1 || '-',
                        row.nm_asesor1 || '-',
                        row.nm_kota_asesor1 || '-',
                        row.nia2 || '-',
                        row.nm_asesor2 || '-',
                        row.nm_kota_asesor2 || '-',
                        row.tahun_akreditasi || '-',
                        row.tahap || '-',
                        row.tgl_mulai_visitasi || '-',
                        row.no_surat || '-',
                        row.tgl_surat || '-',
                        // File status dengan conditional logic
                        (row.file_pakta_integritas_1 && row.file_pakta_integritas_1.trim() !== '') ? 'Sudah Unggah' : 'Belum Unggah',
                        (row.file_pakta_integritas_2 && row.file_pakta_integritas_2.trim() !== '') ? 'Sudah Unggah' : 'Belum Unggah',
                        (row.file_berita_acara_visitasi && row.file_berita_acara_visitasi.trim() !== '') ? 'Sudah Unggah' : 'Belum Unggah',
                        (row.file_temuan_hasil_visitasi && row.file_temuan_hasil_visitasi.trim() !== '') ? 'Sudah Unggah' : 'Belum Unggah',
                        (row.file_absen_pembuka && row.file_absen_pembuka.trim() !== '') ? 'Sudah Unggah' : 'Belum Unggah',
                        (row.file_absen_penutup && row.file_absen_penutup.trim() !== '') ? 'Sudah Unggah' : 'Belum Unggah',
                        (row.file_foto_visitasi && row.file_foto_visitasi.trim() !== '') ? 'Sudah Unggah' : 'Belum Unggah',
                        (row.file_laporan_individu_1 && row.file_laporan_individu_1.trim() !== '') ? 'Sudah Unggah' : 'Belum Unggah',
                        (row.file_laporan_individu_2 && row.file_laporan_individu_2.trim() !== '') ? 'Sudah Unggah' : 'Belum Unggah',
                        (row.file_laporan_kelompok && row.file_laporan_kelompok.trim() !== '') ? 'Sudah Unggah' : 'Belum Unggah',
                        (row.file_penjelasan_hasil_akreditasi && row.file_penjelasan_hasil_akreditasi.trim() !== '') ? 'Sudah Unggah' : 'Belum Unggah'
                    ]);
                });

                // Create workbook and worksheet
                var wb = XLSX.utils.book_new();
                var ws = XLSX.utils.aoa_to_sheet(excelData);

                // Set column widths untuk 27 kolom
                ws['!cols'] = [
                    {wch: 5},   // NO
                    {wch: 12},  // NPSN
                    {wch: 30},  // NAMA SEKOLAH
                    {wch: 10},  // JENJANG
                    {wch: 20},  // KABUPATEN / KOTA
                    {wch: 15},  // NIA ASESOR A
                    {wch: 25},  // NAMA ASESOR A
                    {wch: 20},  // KABUPATEN KOTA ASESOR A
                    {wch: 15},  // NIA ASESOR B
                    {wch: 25},  // NAMA ASESOR B
                    {wch: 20},  // KABUPATEN KOTA ASESOR B
                    {wch: 15},  // TAHUN AKREDITASI
                    {wch: 8},   // TAHAP
                    {wch: 15},  // TANGGAL VISITASI
                    {wch: 20},  // NOMOR SURAT TUGAS
                    {wch: 18},  // TANGGAL SURAT TUGAS
                    {wch: 20},  // FILE PAKTA INTEGRITAS ASESOR A
                    {wch: 20},  // FILE PAKTA INTEGRITAS ASESOR B
                    {wch: 20},  // FILE BERITA ACARA VISITASI
                    {wch: 20},  // FILE TEMUAN HASIL VISITASI
                    {wch: 15},  // FILE ABSEN PEMBUKA
                    {wch: 15},  // FILE ABSEN PENUTUP
                    {wch: 15},  // FILE FOTO VISITASI
                    {wch: 25},  // FILE LAPORAN INDIVIDU ASESOR A
                    {wch: 25},  // FILE LAPORAN INDIVIDU ASESOR B
                    {wch: 20},  // FILE LAPORAN KELOMPOK
                    {wch: 30}   // FILE PENJELASAN HASIL AKREDITASI (PHA)
                ];

                // Apply conditional formatting untuk file status
                applyConditionalFormattingVisitasi(ws, excelData, response.data);

                // Add worksheet to workbook
                XLSX.utils.book_append_sheet(wb, ws, 'export visitasi');

                // Generate filename dengan current date
                var now = new Date();
                var dateStr = now.getFullYear() +
                             String(now.getMonth() + 1).padStart(2, '0') +
                             String(now.getDate()).padStart(2, '0');
                var filename = 'Export_Mapping_Visitasi_PAUD_' + dateStr + '.xlsx';

                // Save file
                XLSX.writeFile(wb, filename);

                // Show success message
                showAlert('success', 'File Excel berhasil didownload: ' + filename);

            } else {
                showAlert('error', response.message || 'Gagal mengambil data untuk export');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error exporting to Excel:', error);
            showAlert('error', 'Terjadi kesalahan saat export Excel');
        }
    });
}

function applyConditionalFormattingVisitasi(ws, excelData, data) {
    console.log('Applying conditional formatting for visitasi export');

    // File status columns (kolom 17-27, index 16-26)
    const fileStatusColumns = [16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26];

    // Apply formatting untuk setiap baris data (skip header row)
    for (let rowIndex = 1; rowIndex < excelData.length; rowIndex++) {
        const dataIndex = rowIndex - 1; // Index untuk data array
        const row = data[dataIndex];

        if (row) {
            // File fields untuk conditional formatting
            const fileFields = [
                row.file_pakta_integritas_1,
                row.file_pakta_integritas_2,
                row.file_berita_acara_visitasi,
                row.file_temuan_hasil_visitasi,
                row.file_absen_pembuka,
                row.file_absen_penutup,
                row.file_foto_visitasi,
                row.file_laporan_individu_1,
                row.file_laporan_individu_2,
                row.file_laporan_kelompok,
                row.file_penjelasan_hasil_akreditasi
            ];

            // Apply formatting untuk setiap file status column
            fileStatusColumns.forEach((colIndex, fileIndex) => {
                const cellAddress = XLSX.utils.encode_cell({r: rowIndex, c: colIndex});
                const fileValue = fileFields[fileIndex];

                if (!ws[cellAddress]) {
                    ws[cellAddress] = {};
                }

                // Conditional formatting berdasarkan file status
                if (fileValue && fileValue.trim() !== '') {
                    // File ada - green background dengan black text
                    ws[cellAddress].s = {
                        fill: {
                            fgColor: { rgb: "98FB98" } // Light green
                        },
                        font: {
                            color: { rgb: "000000" } // Black text
                        }
                    };
                } else {
                    // File kosong - red background dengan white text
                    ws[cellAddress].s = {
                        fill: {
                            fgColor: { rgb: "FF6347" } // Tomato red
                        },
                        font: {
                            color: { rgb: "FFFFFF" } // White text
                        }
                    };
                }
            });
        }
    }

    // Apply header styling
    for (let colIndex = 0; colIndex < excelData[0].length; colIndex++) {
        const cellAddress = XLSX.utils.encode_cell({r: 0, c: colIndex});
        if (!ws[cellAddress]) {
            ws[cellAddress] = {};
        }

        // Gray header styling
        ws[cellAddress].s = {
            fill: {
                fgColor: { rgb: "D3D3D3" } // Light gray
            },
            font: {
                bold: true,
                color: { rgb: "000000" } // Black text
            },
            alignment: {
                horizontal: "center",
                vertical: "center"
            }
        };
    }

    console.log('Conditional formatting applied successfully');
}
