<?php
/**
 * AJAX handler untuk validasi NPSN sekolah
 */

require_once '../../../koneksi.php';
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $npsn = trim($_POST['npsn'] ?? '');
    
    if (empty($npsn)) {
        echo json_encode([
            'success' => false,
            'message' => 'NPSN tidak boleh kosong'
        ]);
        exit;
    }
    
    // Ambil provinsi_id dari session
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Query untuk validasi NPSN dengan filter provinsi dan rumpun dasmen
    $query = "SELECT 
                s.sekolah_id,
                s.npsn,
                s.nama_sekolah,
                j.nm_jenjang,
                k.nm_kota
              FROM sekolah s
              LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
              LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
              WHERE s.npsn = ? 
                AND s.provinsi_id = ?
                AND s.rumpun = 'dasmen'
                AND s.soft_delete = '1'";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("si", $npsn, $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode([
            'success' => false,
            'message' => 'NPSN tidak ditemukan atau bukan sekolah dasmen di provinsi ini'
        ]);
        exit;
    }
    
    $sekolah = $result->fetch_assoc();
    
    // Cek apakah sekolah sudah ada di mapping untuk tahun yang sama
    $check_query = "SELECT id_mapping FROM mapping_2024 
                   WHERE sekolah_id = ? AND provinsi_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("ii", $sekolah['sekolah_id'], $provinsi_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows > 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Sekolah ini sudah ada dalam mapping visitasi'
        ]);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'data' => [
            'sekolah_id' => $sekolah['sekolah_id'],
            'npsn' => $sekolah['npsn'],
            'nama_sekolah' => $sekolah['nama_sekolah'],
            'jenjang' => $sekolah['nm_jenjang'],
            'kota' => $sekolah['nm_kota']
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Validate NPSN Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan saat validasi NPSN'
    ]);
}

$conn->close();
?>
