<?php
/**
 * AJAX handler untuk menampilkan form edit sekretariat
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    if (!isset($_POST['id_sekretariat']) || empty($_POST['id_sekretariat'])) {
        throw new Exception('ID Sekretariat tidak valid');
    }
    
    $id_sekretariat = intval($_POST['id_sekretariat']);
    
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Query untuk mendapatkan data sekretariat dengan filter session
    $sql = "SELECT * FROM sekretariat WHERE id_sekretariat = ? AND provinsi_id = ? AND soft_delete = '1'";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $id_sekretariat, $provinsi_id_session);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        throw new Exception('Data sekretariat tidak ditemukan atau Anda tidak memiliki akses');
    }
    
    $row = $result->fetch_assoc();
    
    // Query untuk mendapatkan dropdown kab/kota sesuai provinsi session
    $kota_query = "SELECT kota_id, nm_kota FROM kab_kota WHERE provinsi_id = ? ORDER BY nm_kota ASC";
    $stmt_kota = $conn->prepare($kota_query);
    $stmt_kota->bind_param("i", $provinsi_id_session);
    $stmt_kota->execute();
    $kota_result = $stmt_kota->get_result();
    
    ?>
    
    <!-- Hidden field untuk ID -->
    <input type="hidden" name="id_sekretariat" value="<?php echo $row['id_sekretariat']; ?>">
    <input type="hidden" name="provinsi_id" value="<?php echo $row['provinsi_id']; ?>">
    <input type="hidden" name="kd_sekretariat" value="<?php echo htmlspecialchars($row['kd_sekretariat']); ?>">
    
    <div class="row">
        <!-- Kolom Kiri -->
        <div class="col-md-6">
            <div class="form-group">
                <label for="edit_nm_staff">Nama Staff <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="edit_nm_staff" name="nm_staff" 
                       value="<?php echo htmlspecialchars($row['nm_staff']); ?>" required maxlength="100">
            </div>
            
            <div class="form-group">
                <label for="edit_jk">Jenis Kelamin <span class="text-danger">*</span></label>
                <select class="form-control" id="edit_jk" name="jk" required>
                    <option value="">-- Pilih Jenis Kelamin --</option>
                    <option value="Pria" <?php echo ($row['jk'] == 'Pria') ? 'selected' : ''; ?>>Pria</option>
                    <option value="Wanita" <?php echo ($row['jk'] == 'Wanita') ? 'selected' : ''; ?>>Wanita</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="edit_ktp">Nomor KTP</label>
                <input type="text" class="form-control" id="edit_ktp" name="ktp" 
                       value="<?php echo htmlspecialchars($row['ktp']); ?>" maxlength="20">
            </div>
            
            <div class="form-group">
                <label for="edit_tempat_lahir">Tempat Lahir</label>
                <input type="text" class="form-control" id="edit_tempat_lahir" name="tempat_lahir" 
                       value="<?php echo htmlspecialchars($row['tempat_lahir']); ?>" maxlength="30">
            </div>
            
            <div class="form-group">
                <label for="edit_tgl_lahir">Tanggal Lahir</label>
                <input type="date" class="form-control" id="edit_tgl_lahir" name="tgl_lahir" 
                       value="<?php echo ($row['tgl_lahir'] != '0000-00-00' && !empty($row['tgl_lahir'])) ? $row['tgl_lahir'] : ''; ?>">
            </div>
            
            <div class="form-group">
                <label for="edit_pendidikan">Pendidikan</label>
                <input type="text" class="form-control" id="edit_pendidikan" name="pendidikan" 
                       value="<?php echo htmlspecialchars($row['pendidikan']); ?>" maxlength="15">
            </div>
            
            <div class="form-group">
                <label for="edit_kota_id">Kabupaten/Kota <span class="text-danger">*</span></label>
                <select class="form-control" id="edit_kota_id" name="kota_id" required>
                    <option value="">-- Pilih Kabupaten/Kota --</option>
                    <?php
                    while ($kota_row = $kota_result->fetch_assoc()) {
                        $selected = ($kota_row['kota_id'] == $row['kota_id']) ? 'selected' : '';
                        echo '<option value="' . $kota_row['kota_id'] . '" ' . $selected . '>' . 
                             htmlspecialchars($kota_row['nm_kota']) . '</option>';
                    }
                    ?>
                </select>
            </div>
        </div>
        
        <!-- Kolom Kanan -->
        <div class="col-md-6">
            <div class="form-group">
                <label for="edit_jabatan">Jabatan di BAN SM Provinsi</label>
                <select name="jabatan" id="edit_jabatan" class="form-control" required>
                    <option value="">...</option>
                    <option value="Staff IT" <?php echo ($row['jabatan'] == 'Staff IT') ? 'selected' : ''; ?>>Staff IT</option>
                    <option value="Tim PADA" <?php echo ($row['jabatan'] == 'Tim PADA') ? 'selected' : ''; ?>>Tim PADA</option>
                    <option value="Staff Keuangan" <?php echo ($row['jabatan'] == 'Staff Keuangan') ? 'selected' : ''; ?>>Staff Keuangan</option>
                    <option value="Staff Sekretariat Umum" <?php echo ($row['jabatan'] == 'Staff Sekretariat Umum') ? 'selected' : ''; ?>>Staff Sekretariat Umum</option>
                    <option value="Asesor" <?php echo ($row['jabatan'] == 'Asesor') ? 'selected' : ''; ?>>Asesor</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="edit_unit_kerja">Unit Kerja</label>
                <textarea class="form-control" id="edit_unit_kerja" name="unit_kerja" rows="2" maxlength="300"><?php echo htmlspecialchars($row['unit_kerja']); ?></textarea>
            </div>
            
            <div class="form-group">
                <label for="edit_no_hp">Nomor HP</label>
                <input type="text" class="form-control" id="edit_no_hp" name="no_hp" 
                       value="<?php echo htmlspecialchars($row['no_hp']); ?>" maxlength="20">
            </div>
            
            <div class="form-group">
                <label for="edit_no_wa">Nomor WhatsApp</label>
                <input type="text" class="form-control" id="edit_no_wa" name="no_wa" 
                       value="<?php echo htmlspecialchars($row['no_wa']); ?>" maxlength="15">
            </div>
            
            <div class="form-group">
                <label for="edit_email">Email</label>
                <input type="text" class="form-control" id="edit_email" name="email" 
                       value="<?php echo htmlspecialchars($row['email']); ?>" maxlength="50">
            </div>
            
            <div class="form-group">
                <label for="edit_status_keaktifan_id">Status Keaktifan <span class="text-danger">*</span></label>
                <select class="form-control" id="edit_status_keaktifan_id" name="status_keaktifan_id" required>
                    <option value="">-- Pilih Status --</option>
                    <option value="1" <?php echo ($row['status_keaktifan_id'] == '1') ? 'selected' : ''; ?>>Aktif</option>
                    <option value="0" <?php echo ($row['status_keaktifan_id'] == '0') ? 'selected' : ''; ?>>Tidak Aktif</option>
                    <option value="2" <?php echo ($row['status_keaktifan_id'] == '2') ? 'selected' : ''; ?>>Tidak Diketahui</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="edit_no_urut">Nomor Urut</label>
                <input type="number" class="form-control" id="edit_no_urut" name="no_urut" 
                       value="<?php echo $row['no_urut']; ?>">
            </div>
        </div>
    </div>
    
    <!-- Alamat (Full Width) -->
    <div class="row">
        <div class="col-12">
            <div class="form-group">
                <label for="edit_alamat_kantor">Alamat Kantor</label>
                <textarea class="form-control" id="edit_alamat_kantor" name="alamat_kantor" rows="2" maxlength="300"><?php echo htmlspecialchars($row['alamat_kantor']); ?></textarea>
            </div>
            
            <div class="form-group">
                <label for="edit_alamat_rumah">Alamat Rumah</label>
                <textarea class="form-control" id="edit_alamat_rumah" name="alamat_rumah" rows="2" maxlength="300"><?php echo htmlspecialchars($row['alamat_rumah']); ?></textarea>
            </div>
            
            <div class="form-group">
                <label for="edit_sebab">Sebab/Keterangan</label>
                <textarea class="form-control" id="edit_sebab" name="sebab" rows="2"><?php echo htmlspecialchars($row['sebab']); ?></textarea>
            </div>
        </div>
    </div>
    
    <?php
    
} catch (Exception $e) {
    echo '<div class="alert alert-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
