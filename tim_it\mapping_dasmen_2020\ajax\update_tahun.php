<?php
session_start();
require_once '../../../koneksi.php';

// Cek session dan level
if (!isset($_SESSION['kd_user']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Ambil provinsi_id dari session
$provinsi_id = $_SESSION['provinsi_id'];

if ($_POST) {
    // Baca Variabel Data Form dengan real_escape_string
    $nama_tahun = $conn->real_escape_string($_POST['nama_tahun']);
    
    // Validasi input
    if (empty($nama_tahun) || !is_numeric($nama_tahun)) {
        echo json_encode(['success' => false, 'message' => 'Tahun akreditasi harus berupa angka']);
        exit;
    }
    
    $tahun_int = intval($nama_tahun);
    if ($tahun_int < 2000 || $tahun_int > 2030) {
        echo json_encode(['success' => false, 'message' => 'Tahun akreditasi harus antara 2000-2030']);
        exit;
    }

    try {
        // Cek apakah sudah ada record untuk provinsi ini
        $check_query = "SELECT id_mapping_tahun FROM mapping_tahun WHERE provinsi_id = $provinsi_id";
        $check_result = $conn->query($check_query);
        
        if (!$check_result) {
            throw new Exception("Check query failed: " . $conn->error);
        }
        
        if ($check_result->num_rows > 0) {
            // Update existing record
            $update_query = "UPDATE mapping_tahun 
                             SET nama_tahun = $tahun_int
                             WHERE provinsi_id = $provinsi_id";
            
            if ($conn->query($update_query)) {
                echo json_encode(['success' => true, 'message' => 'Tahun akreditasi berhasil diupdate']);
            } else {
                throw new Exception("Update failed: " . $conn->error);
            }
        } else {
            // Insert new record
            $insert_query = "INSERT INTO mapping_tahun (nama_tahun, provinsi_id) 
                             VALUES ($tahun_int, $provinsi_id)";
            
            if ($conn->query($insert_query)) {
                echo json_encode(['success' => true, 'message' => 'Tahun akreditasi berhasil disimpan']);
            } else {
                throw new Exception("Insert failed: " . $conn->error);
            }
        }

    } catch (Exception $e) {
        error_log("Update Tahun Error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}

$conn->close();
?>
