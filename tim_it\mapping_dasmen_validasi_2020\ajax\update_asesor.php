<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi required fields
    $required_fields = ['id_mapping_validasi', 'kd_asesor1', 'kd_asesor2'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            $field_names = [
                'id_mapping_validasi' => 'ID Mapping Validasi',
                'kd_asesor1' => 'Kode Asesor 1',
                'kd_asesor2' => 'Kode Asesor 2'
            ];
            throw new Exception("Field " . $field_names[$field] . " harus diisi");
        }
    }
    
    // Sanitasi input
    $id_mapping_validasi = intval($_POST['id_mapping_validasi']);
    $kd_asesor1 = $conn->real_escape_string(trim($_POST['kd_asesor1']));
    $kd_asesor2 = $conn->real_escape_string(trim($_POST['kd_asesor2']));
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Validasi ID mapping
    if ($id_mapping_validasi <= 0) {
        throw new Exception('ID mapping validasi tidak valid');
    }
    
    // Validasi: kd_asesor1 dan kd_asesor2 harus berbeda
    if ($kd_asesor1 === $kd_asesor2) {
        throw new Exception('Kode Asesor 1 dan Asesor 2 tidak boleh sama');
    }
    
    // Debug: Log values
    error_log("Update Asesor - ID: $id_mapping_validasi, Asesor1: $kd_asesor1, Asesor2: $kd_asesor2");
    
    // Cek apakah mapping validasi exists dan milik provinsi yang benar
    $check_query = "SELECT id_mapping_validasi FROM mapping_validasi 
                    WHERE id_mapping_validasi = ? AND provinsi_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("ii", $id_mapping_validasi, $provinsi_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows === 0) {
        throw new Exception('Data mapping validasi tidak ditemukan atau Anda tidak memiliki akses');
    }
    
    // Validasi apakah asesor 1 exists dan aktif
    $asesor1_query = "SELECT kd_asesor1, nia1, nm_asesor1, no_hp, kota_id1 
                      FROM asesor_1 
                      WHERE kd_asesor1 = ? AND status_keaktifan_id = '1' AND soft_delete = '1'";
    $asesor1_stmt = $conn->prepare($asesor1_query);
    $asesor1_stmt->bind_param("s", $kd_asesor1);
    $asesor1_stmt->execute();
    $asesor1_result = $asesor1_stmt->get_result();
    
    if ($asesor1_result->num_rows === 0) {
        throw new Exception('Data Asesor 1 tidak ditemukan atau tidak aktif');
    }
    $asesor1_data = $asesor1_result->fetch_assoc();
    
    // Validasi apakah asesor 2 exists dan aktif
    $asesor2_query = "SELECT kd_asesor2, nia2, nm_asesor2, no_hp, kota_id2 
                      FROM asesor_2 
                      WHERE kd_asesor2 = ? AND status_keaktifan_id = '1' AND soft_delete = '1'";
    $asesor2_stmt = $conn->prepare($asesor2_query);
    $asesor2_stmt->bind_param("s", $kd_asesor2);
    $asesor2_stmt->execute();
    $asesor2_result = $asesor2_stmt->get_result();
    
    if ($asesor2_result->num_rows === 0) {
        throw new Exception('Data Asesor 2 tidak ditemukan atau tidak aktif');
    }
    $asesor2_data = $asesor2_result->fetch_assoc();
    
    // Get nama kota untuk asesor 1
    $kota1_query = "SELECT nm_kota FROM kab_kota WHERE kota_id = ?";
    $kota1_stmt = $conn->prepare($kota1_query);
    $kota1_stmt->bind_param("s", $asesor1_data['kota_id1']);
    $kota1_stmt->execute();
    $kota1_result = $kota1_stmt->get_result();
    $kota_asesor1 = $kota1_result->num_rows > 0 ? $kota1_result->fetch_assoc()['nm_kota'] : '-';
    
    // Get nama kota untuk asesor 2
    $kota2_query = "SELECT nm_kota FROM kab_kota WHERE kota_id = ?";
    $kota2_stmt = $conn->prepare($kota2_query);
    $kota2_stmt->bind_param("s", $asesor2_data['kota_id2']);
    $kota2_stmt->execute();
    $kota2_result = $kota2_stmt->get_result();
    $kota_asesor2 = $kota2_result->num_rows > 0 ? $kota2_result->fetch_assoc()['nm_kota'] : '-';
    
    // Begin transaction
    $conn->autocommit(false);
    
    // Update mapping validasi
    $update_query = "UPDATE mapping_validasi 
                     SET kd_asesor1 = ?, kd_asesor2 = ?
                     WHERE id_mapping_validasi = ? AND provinsi_id = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("ssii", $kd_asesor1, $kd_asesor2, $id_mapping_validasi, $provinsi_id);
    
    if (!$update_stmt->execute()) {
        throw new Exception('Gagal mengupdate data asesor: ' . $conn->error);
    }
    
    // Cek apakah ada row yang terupdate
    if ($update_stmt->affected_rows === 0) {
        throw new Exception('Tidak ada data yang diupdate. Data mungkin sudah sama atau tidak ditemukan');
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Log successful update
    error_log("Update Asesor Success - ID: $id_mapping_validasi, Asesor1: " . $asesor1_data['nm_asesor1'] . ", Asesor2: " . $asesor2_data['nm_asesor2'] . ", User: " . $_SESSION['nm_user']);
    
    echo json_encode([
        'success' => true,
        'message' => 'Data asesor berhasil diupdate',
        'data' => [
            'id_mapping_validasi' => $id_mapping_validasi,
            'kd_asesor1' => $kd_asesor1,
            'kd_asesor2' => $kd_asesor2,
            'nia1' => $asesor1_data['nia1'],
            'nia2' => $asesor2_data['nia2'],
            'nm_asesor1' => $asesor1_data['nm_asesor1'],
            'nm_asesor2' => $asesor2_data['nm_asesor2'],
            'hp_asesor1' => $asesor1_data['no_hp'],
            'hp_asesor2' => $asesor2_data['no_hp'],
            'kota_asesor1' => $kota_asesor1,
            'kota_asesor2' => $kota_asesor2
        ]
    ]);
    
} catch (Exception $e) {
    // Rollback transaction
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Update Asesor Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
