/**
 * JavaScript untuk modul Data Pengurus/Anggota
 */

$(document).ready(function() {
    // Inisialisasi DataTable
    initDataTable();
    
    // Event handlers
    initEventHandlers();
});

/**
 * Inisialisasi DataTable
 */
function initDataTable() {
    $('#table-pengurus').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: 'ajax/get_pengurus.php',
            type: 'POST',
            error: function(xhr, error, thrown) {
                console.error('DataTables error:', error);
                showAlert('error', 'Gagal memuat data pengurus/anggota');
            }
        },
        columns: [
            { 
                data: null,
                orderable: false,
                searchable: false,
                render: function(data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { 
                data: 'nm_pengurus',
                name: 'nm_pengurus',
                render: function(data, type, row) {
                    return '<strong>' + data + '</strong>';
                }
            },
            { 
                data: 'jk',
                name: 'jk',
                className: 'text-center'
            },
            { 
                data: 'jabatan',
                name: 'jabatan',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { 
                data: 'nm_kota',
                name: 'nm_kota',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { 
                data: 'unit_kerja',
                name: 'unit_kerja',
                render: function(data, type, row) {
                    if (data && data.length > 30) {
                        return data.substring(0, 30) + '...';
                    }
                    return data || '-';
                }
            },
            {
                data: 'status_keaktifan_id',
                name: 'status_keaktifan_id',
                className: 'text-center',
                render: function(data, type, row) {
                    if (data == '1') {
                        return '<span class="badge badge-success">Aktif</span>';
                    } else if (data == '0') {
                        return '<span class="badge badge-danger">Tidak Aktif</span>';
                    } else if (data == '2') {
                        return '<span class="badge badge-warning">Tidak Diketahui</span>';
                    } else {
                        return '<span class="badge badge-secondary">-</span>';
                    }
                }
            },
            {
                data: null,
                orderable: false,
                searchable: false,
                className: 'text-center',
                render: function(data, type, row) {
                    return '<button class="btn btn-info btn-sm mr-1" onclick="showDetail(' + 
                           row.id_pengurus + ')" title="Detail">' +
                           '<i class="fas fa-eye"></i></button>' +
                           '<button class="btn btn-warning btn-sm mr-1" onclick="showEditModal(' + 
                           row.id_pengurus + ')" title="Edit">' +
                           '<i class="fas fa-edit"></i></button>' +
                           '<button class="btn btn-danger btn-sm" onclick="confirmDelete(' + 
                           row.id_pengurus + ', \'' + row.nm_pengurus.replace(/'/g, "\\'") + '\', \'' + 
                           row.kd_pengurus + '\')" title="Hapus">' +
                           '<i class="fas fa-trash"></i></button>';
                }
            }
        ],
        order: [[1, 'asc']],
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: "Memuat data...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(difilter dari _MAX_ total data)",
            loadingRecords: "Memuat data...",
            zeroRecords: "Tidak ada data yang ditemukan",
            emptyTable: "Tidak ada data tersedia",
            paginate: {
                first: "Pertama",
                last: "Terakhir",
                next: "Selanjutnya",
                previous: "Sebelumnya"
            }
        },
        responsive: true,
        autoWidth: false,
        columnDefs: [
            {
                targets: 0, // Kolom NO
                width: '50px'
            },
            {
                targets: 2, // Kolom JK
                width: '80px'
            },
            {
                targets: 6, // Kolom STATUS
                width: '120px'
            },
            {
                targets: 7, // Kolom AKSI
                width: '150px'
            }
        ]
    });
}

/**
 * Inisialisasi event handlers
 */
function initEventHandlers() {
    // Event handler untuk tombol tambah
    $('#btn-add').on('click', function() {
        showTambahModal();
    });

    // Event handler untuk form tambah
    $('#form-tambah-pengurus').on('submit', function(e) {
        e.preventDefault();
        simpanPengurus();
    });

    // Event handler untuk form edit
    $('#form-edit-pengurus').on('submit', function(e) {
        e.preventDefault();
        updatePengurus();
    });

    // Event handler untuk konfirmasi hapus
    $('#btn-konfirmasi-hapus').on('click', function() {
        var pengurusId = $(this).data('pengurus-id');
        var namaPengurus = $(this).data('nama-pengurus');
        var kodePengurus = $(this).data('kode-pengurus');

        // Tutup modal konfirmasi
        $('#modal-konfirmasi-hapus').modal('hide');

        // Proses hapus
        deletePengurus(pengurusId, namaPengurus, kodePengurus);
    });
}

/**
 * Fungsi untuk menampilkan modal tambah
 */
function showTambahModal() {
    // Load form tambah
    loadTambahForm();
    
    // Show modal
    $('#modal-tambah').modal('show');
}

/**
 * Fungsi untuk load form tambah
 */
function loadTambahForm() {
    $.ajax({
        url: 'ajax/get_dropdown_options.php',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var formHtml = generateTambahForm(response.data);
                $('#modal-tambah .modal-body').html(formHtml);

                // Auto-generate kode pengurus setelah form dimuat
                generateKodePengurus();
            } else {
                $('#modal-tambah .modal-body').html('<div class="alert alert-danger">Gagal memuat form</div>');
            }
        },
        error: function() {
            $('#modal-tambah .modal-body').html('<div class="alert alert-danger">Gagal memuat form</div>');
        }
    });
}

/**
 * Fungsi untuk generate form tambah
 */
function generateTambahForm(dropdownData) {
    var kotaOptions = '<option value="">-- Pilih Kabupaten/Kota --</option>';
    if (dropdownData.kota) {
        dropdownData.kota.forEach(function(item) {
            kotaOptions += '<option value="' + item.kota_id + '">' + item.nm_kota + '</option>';
        });
    }

    return `
        <!-- Hidden field untuk kode pengurus auto-generate -->
        <input type="hidden" id="kd_pengurus" name="kd_pengurus" value="">

        <div class="row">
            <!-- Kolom Kiri -->
            <div class="col-md-6">
                <div class="form-group">
                    <label for="nm_pengurus">Nama Pengurus <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="nm_pengurus" name="nm_pengurus" required maxlength="100">
                </div>
                
                <div class="form-group">
                    <label for="jk">Jenis Kelamin <span class="text-danger">*</span></label>
                    <select class="form-control" id="jk" name="jk" required>
                        <option value="">-- Pilih Jenis Kelamin --</option>
                        <option value="Pria">Pria</option>
                        <option value="Wanita">Wanita</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="ktp">Nomor KTP</label>
                    <input type="text" class="form-control" id="ktp" name="ktp" maxlength="30">
                </div>
                
                <div class="form-group">
                    <label for="tempat_lahir">Tempat Lahir</label>
                    <input type="text" class="form-control" id="tempat_lahir" name="tempat_lahir" maxlength="30">
                </div>
                
                <div class="form-group">
                    <label for="tgl_lahir">Tanggal Lahir</label>
                    <input type="date" class="form-control" id="tgl_lahir" name="tgl_lahir">
                </div>
                
                <div class="form-group">
                    <label for="pendidikan">Pendidikan</label>
                    <input type="text" class="form-control" id="pendidikan" name="pendidikan" maxlength="15">
                </div>
                
                <div class="form-group">
                    <label for="kota_id">Kabupaten/Kota <span class="text-danger">*</span></label>
                    <select class="form-control" id="kota_id" name="kota_id" required>
                        ${kotaOptions}
                    </select>
                </div>
            </div>
            
            <!-- Kolom Kanan -->
            <div class="col-md-6">
                <div class="form-group">
                    <label for="jabatan">Jabatan</label>
                    <input type="text" class="form-control" id="jabatan" name="jabatan" maxlength="50">
                </div>
                
                <div class="form-group">
                    <label for="jabatan_kantor_asal">Jabatan Kantor Asal</label>
                    <input type="text" class="form-control" id="jabatan_kantor_asal" name="jabatan_kantor_asal" maxlength="20">
                </div>
                
                <div class="form-group">
                    <label for="unit_kerja">Unit Kerja</label>
                    <textarea class="form-control" id="unit_kerja" name="unit_kerja" rows="2" maxlength="300"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="no_hp">Nomor HP</label>
                    <input type="text" class="form-control" id="no_hp" name="no_hp" maxlength="20">
                </div>
                
                <div class="form-group">
                    <label for="no_wa">Nomor WhatsApp</label>
                    <input type="text" class="form-control" id="no_wa" name="no_wa" maxlength="20">
                </div>
                
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="text" class="form-control" id="email" name="email" maxlength="50">
                </div>
                
                <div class="form-group">
                    <label for="status_keaktifan_id">Status Keaktifan <span class="text-danger">*</span></label>
                    <select class="form-control" id="status_keaktifan_id" name="status_keaktifan_id" required>
                        <option value="">-- Pilih Status --</option>
                        <option value="1">Aktif</option>
                        <option value="0">Tidak Aktif</option>
                        <option value="2">Tidak Diketahui</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="no_urut">Nomor Urut</label>
                    <input type="number" class="form-control" id="no_urut" name="no_urut">
                </div>
            </div>
        </div>
        
        <!-- Alamat (Full Width) -->
        <div class="row">
            <div class="col-12">
                <div class="form-group">
                    <label for="alamat_kantor">Alamat Kantor</label>
                    <textarea class="form-control" id="alamat_kantor" name="alamat_kantor" rows="2" maxlength="300"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="alamat_rumah">Alamat Rumah</label>
                    <textarea class="form-control" id="alamat_rumah" name="alamat_rumah" rows="2" maxlength="300"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="sebab">Sebab/Keterangan</label>
                    <textarea class="form-control" id="sebab" name="sebab" rows="2"></textarea>
                </div>
            </div>
        </div>
    `;
}

/**
 * Fungsi untuk menampilkan modal edit
 */
function showEditModal(pengurusId) {
    // Show loading
    $('#modal-edit-content').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Memuat data...</div>');
    $('#modal-edit').modal('show');

    // Load form edit via AJAX
    $.ajax({
        url: 'ajax/get_edit_form.php',
        type: 'POST',
        data: { id_pengurus: pengurusId },
        dataType: 'html',
        success: function(response) {
            $('#modal-edit-content').html(response);
        },
        error: function(xhr, status, error) {
            $('#modal-edit-content').html('<div class="alert alert-danger">Gagal memuat form edit</div>');
        }
    });
}

/**
 * Fungsi untuk menampilkan detail
 */
function showDetail(pengurusId) {
    // Show loading
    $('#modal-detail-content').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Memuat data...</div>');
    $('#modal-detail').modal('show');

    // Load detail via AJAX
    $.ajax({
        url: 'ajax/get_detail_pengurus.php',
        type: 'POST',
        data: { id_pengurus: pengurusId },
        dataType: 'html',
        success: function(response) {
            $('#modal-detail-content').html(response);
        },
        error: function(xhr, status, error) {
            $('#modal-detail-content').html('<div class="alert alert-danger">Gagal memuat detail data</div>');
        }
    });
}

/**
 * Fungsi untuk konfirmasi hapus
 */
function confirmDelete(pengurusId, namaPengurus, kodePengurus) {
    // Set data ke modal
    $('#hapus-nama-pengurus').text(namaPengurus);
    $('#hapus-kode-pengurus').text(kodePengurus);

    // Set data ke tombol konfirmasi
    $('#btn-konfirmasi-hapus').data('pengurus-id', pengurusId);
    $('#btn-konfirmasi-hapus').data('nama-pengurus', namaPengurus);
    $('#btn-konfirmasi-hapus').data('kode-pengurus', kodePengurus);

    // Tampilkan modal konfirmasi
    $('#modal-konfirmasi-hapus').modal('show');
}

/**
 * Fungsi untuk simpan data pengurus
 */
function simpanPengurus() {
    // Disable tombol submit
    $('#btn-simpan').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    // Serialize form data
    var formData = $('#form-tambah-pengurus').serialize();

    $.ajax({
        url: 'ajax/simpan_pengurus.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Tutup modal
                $('#modal-tambah').modal('hide');

                // Reload DataTable
                $('#table-pengurus').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat menyimpan data');
        },
        complete: function() {
            // Enable tombol submit
            $('#btn-simpan').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Data');
        }
    });
}

/**
 * Fungsi untuk update data pengurus
 */
function updatePengurus() {
    // Disable tombol submit
    $('#btn-update').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    // Serialize form data
    var formData = $('#form-edit-pengurus').serialize();

    $.ajax({
        url: 'ajax/update_pengurus.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Tutup modal
                $('#modal-edit').modal('hide');

                // Reload DataTable
                $('#table-pengurus').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat menyimpan data');
        },
        complete: function() {
            // Enable tombol submit
            $('#btn-update').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Perubahan');
        }
    });
}

/**
 * Fungsi untuk hapus data pengurus
 */
function deletePengurus(pengurusId, namaPengurus, kodePengurus) {
    $.ajax({
        url: 'ajax/hapus_pengurus.php',
        type: 'POST',
        data: {
            id_pengurus: pengurusId,
            nm_pengurus: namaPengurus,
            kd_pengurus: kodePengurus
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Reload DataTable
                $('#table-pengurus').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat menghapus data');
        }
    });
}

/**
 * Fungsi untuk menampilkan alert
 */
function showAlert(type, message) {
    var alertClass = 'alert-info';
    var icon = 'fas fa-info-circle';
    var title = 'Informasi';

    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            icon = 'fas fa-check-circle';
            title = 'Berhasil';
            break;
        case 'error':
            alertClass = 'alert-danger';
            icon = 'fas fa-exclamation-circle';
            title = 'Error';
            break;
        case 'warning':
            alertClass = 'alert-warning';
            icon = 'fas fa-exclamation-triangle';
            title = 'Peringatan';
            break;
    }

    // Update modal notification
    $('#modal-notification-header').removeClass().addClass('modal-header ' + alertClass);
    $('#modal-notification-icon').removeClass().addClass(icon);
    $('#modal-notification-text').text(title);
    $('#modal-notification-message').text(message);

    // Show modal
    $('#modal-notification').modal('show');

    // Auto hide after 3 seconds for success messages
    if (type === 'success') {
        setTimeout(function() {
            $('#modal-notification').modal('hide');
        }, 3000);
    }
}

/**
 * Fungsi untuk generate kode pengurus otomatis
 */
function generateKodePengurus() {
    // Generate unique ID menggunakan timestamp dan random
    var timestamp = Date.now();
    var random = Math.floor(Math.random() * 1000);
    var kodePengurus = 'PGR' + timestamp + random;

    // Set value ke hidden input
    $('#kd_pengurus').val(kodePengurus);
}
