<?php
/**
 * AJAX handler untuk menyimpan data jenjang baru
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    $required_fields = ['jenjang_id', 'nm_jenjang'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            throw new Exception("Field {$field} harus diisi");
        }
    }
    
    // Sanitasi input
    $jenjang_id = trim($_POST['jenjang_id']);
    $nm_jenjang = trim($_POST['nm_jenjang']);
    
    // Validasi panjang karakter
    if (strlen($jenjang_id) > 2) {
        throw new Exception('Kode jenjang maksimal 2 karakter');
    }
    
    if (strlen($nm_jenjang) > 15) {
        throw new Exception('Nama jenjang maksimal 15 karakter');
    }
    
    // Cek duplikasi jenjang_id
    $check_sql = "SELECT COUNT(*) as count FROM jenjang WHERE jenjang_id = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("s", $jenjang_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    $count = $check_result->fetch_assoc()['count'];
    
    if ($count > 0) {
        throw new Exception('Kode jenjang sudah ada, gunakan kode yang berbeda');
    }
    
    // Cek duplikasi nm_jenjang
    $check_name_sql = "SELECT COUNT(*) as count FROM jenjang WHERE nm_jenjang = ?";
    $check_name_stmt = $conn->prepare($check_name_sql);
    $check_name_stmt->bind_param("s", $nm_jenjang);
    $check_name_stmt->execute();
    $check_name_result = $check_name_stmt->get_result();
    $name_count = $check_name_result->fetch_assoc()['count'];
    
    if ($name_count > 0) {
        throw new Exception('Nama jenjang sudah ada, gunakan nama yang berbeda');
    }
    
    // Begin transaction
    $conn->autocommit(false);
    
    // Query insert
    $sql = "INSERT INTO jenjang (jenjang_id, nm_jenjang) VALUES (?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ss", $jenjang_id, $nm_jenjang);
    
    if (!$stmt->execute()) {
        throw new Exception('Gagal menyimpan data jenjang: ' . $stmt->error);
    }
    
    $id_jenjang = $conn->insert_id;
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data jenjang berhasil disimpan',
        'data' => [
            'id_jenjang' => $id_jenjang,
            'jenjang_id' => $jenjang_id,
            'nm_jenjang' => $nm_jenjang
        ]
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Rollback transaction jika ada error
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Simpan Jenjang Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
