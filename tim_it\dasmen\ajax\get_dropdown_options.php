<?php
/**
 * AJAX handler untuk mengambil options dropdown
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi input
    if (!isset($_POST['type']) || empty($_POST['type'])) {
        throw new Exception('Tipe dropdown tidak valid');
    }

    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];

    $type = $_POST['type'];
    $data = [];
    
    switch ($type) {
        case 'jenjang':
            // Ambil semua data jenjang
            $query = "SELECT jenjang_id, nm_jenjang
                      FROM jenjang
                      ORDER BY jenjang_id";
            
            $result = $conn->query($query);
            while ($row = $result->fetch_assoc()) {
                $data[] = [
                    'jenjang_id' => $row['jenjang_id'],
                    'nm_jenjang' => $row['nm_jenjang']
                ];
            }
            break;
            
        case 'kota':
            // Ambil data kab/kota berdasarkan provinsi_id dari session
            $query = "SELECT kota_id, nm_kota
                      FROM kab_kota
                      WHERE provinsi_id = {$provinsi_id_session}
                      ORDER BY nm_kota";
            
            $result = $conn->query($query);
            while ($row = $result->fetch_assoc()) {
                $data[] = [
                    'kota_id' => $row['kota_id'],
                    'nm_kota' => $row['nm_kota']
                ];
            }
            break;
            
        case 'tipe_sekolah':
            // Ambil semua data tipe sekolah
            $query = "SELECT tipe_sekolah_id, nm_tipe_sekolah 
                      FROM tipe_sekolah 
                      ORDER BY nm_tipe_sekolah";
            
            $result = $conn->query($query);
            while ($row = $result->fetch_assoc()) {
                $data[] = [
                    'tipe_sekolah_id' => $row['tipe_sekolah_id'],
                    'nm_tipe_sekolah' => $row['nm_tipe_sekolah']
                ];
            }
            break;
            
        case 'status_sekolah':
            // Ambil semua data status sekolah
            $query = "SELECT status_sekolah_id, nm_status_sekolah 
                      FROM status_sekolah 
                      ORDER BY nm_status_sekolah";
            
            $result = $conn->query($query);
            while ($row = $result->fetch_assoc()) {
                $data[] = [
                    'status_sekolah_id' => $row['status_sekolah_id'],
                    'nm_status_sekolah' => $row['nm_status_sekolah']
                ];
            }
            break;
            
        default:
            throw new Exception('Tipe dropdown tidak dikenali');
    }
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data berhasil diambil',
        'data' => $data
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Log error
    error_log("Get Dropdown Options Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => []
    ];
    
    echo json_encode($response);
}
?>
