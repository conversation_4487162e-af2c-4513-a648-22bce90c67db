$(document).ready(function() {
    // Initialize DataTable
    initDataTable();
    
    // Event handlers untuk action buttons
    $('#btn-input-mapping').on('click', function() {
        $('#modalInputMapping').modal('show');
        resetFormInputMapping();
    });
    
    $('#btn-export-excel').on('click', function() {
        showAlert('info', 'Fitur export Excel akan segera tersedia');
    });
    
    $('#btn-import-excel').on('click', function() {
        showAlert('info', 'Fitur import Excel akan segera tersedia');
    });
    
    $('#btn-tahun-akreditasi').on('click', function() {
        showAlert('info', 'Fitur tahun akreditasi akan segera tersedia');
    });
    
    // Event handler untuk tombol detail
    $(document).on('click', '.btn-detail-visitasi', function(e) {
        e.preventDefault();
        var idMapping = $(this).data('id');
        showAlert('info', 'Fitur detail visitasi akan segera tersedia');
    });

    // Event handlers untuk form input mapping
    $('#btnSimpanMapping').on('click', function() {
        simpanDataMapping();
    });

    // Real-time validation untuk NPSN
    $('#npsn_sekolah').on('blur', function() {
        var npsn = $(this).val().trim();
        if (npsn) {
            validateNPSN(npsn);
        }
    });

    // Real-time validation untuk NIA Asesor 1
    $('#nia_asesor1').on('blur', function() {
        var nia = $(this).val().trim();
        if (nia) {
            validateAsesor(nia, 1);
        }
    });

    // Real-time validation untuk NIA Asesor 2
    $('#nia_asesor2').on('blur', function() {
        var nia = $(this).val().trim();
        if (nia) {
            validateAsesor(nia, 2);
        }
    });
});

function initDataTable() {
    $('#table-mapping-visitasi').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: 'ajax/get_mapping_visitasi.php',
            type: 'POST',
            error: function(xhr, error, code) {
                console.error('DataTable AJAX Error:', error);
                showAlert('error', 'Gagal memuat data mapping visitasi');
            }
        },
        columns: [
            {
                data: null,
                name: 'no',
                orderable: false,
                searchable: false,
                render: function(data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { data: 'npsn', name: 'npsn' },
            { data: 'nama_sekolah', name: 'nama_sekolah' },
            {
                data: null,
                name: 'jenjang_rumpun',
                render: function(data, type, row) {
                    var jenjang = row.nm_jenjang || '-';
                    var rumpun = row.rumpun ? row.rumpun.toUpperCase() : '-';
                    return jenjang + '<br><small class="text-muted">' + rumpun + '</small>';
                }
            },
            { data: 'nm_kota', name: 'nm_kota' },
            {
                data: 'nia1',
                name: 'nia1',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            {
                data: 'nm_asesor1',
                name: 'nm_asesor1',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            {
                data: 'nia2',
                name: 'nia2',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            {
                data: 'nm_asesor2',
                name: 'nm_asesor2',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { data: 'tahun_akreditasi', name: 'tahun_akreditasi' },
            { data: 'tahap', name: 'tahap' },
            {
                data: null,
                name: 'aksi',
                orderable: false,
                searchable: false,
                render: function(data, type, row) {
                    return '<button type="button" class="btn btn-info btn-sm btn-detail-visitasi" data-id="' + row.id_mapping + '" title="Detail Visitasi">' +
                           '<i class="fas fa-eye"></i>' +
                           '</button>';
                }
            }
        ],
        order: [[1, 'asc']], // Order by NPSN
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]],
        language: {
            processing: "Memproses...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(disaring dari _MAX_ total data)",
            loadingRecords: "Memuat...",
            zeroRecords: "Tidak ada data yang ditemukan",
            emptyTable: "Tidak ada data mapping visitasi",
            paginate: {
                first: "Pertama",
                previous: "Sebelumnya",
                next: "Selanjutnya",
                last: "Terakhir"
            }
        },
        responsive: true,
        scrollX: true,
        dom: '<"row"<"col-sm-6"l><"col-sm-6"f>>' +
             '<"row"<"col-sm-12"tr>>' +
             '<"row"<"col-sm-5"i><"col-sm-7"p>>',
        drawCallback: function(settings) {
            // DataTable draw completed
        }
    });
}

// Fungsi untuk reset form input mapping
function resetFormInputMapping() {
    $('#formInputMapping')[0].reset();

    // Reset validation states
    $('#npsn_sekolah, #nia_asesor1, #nia_asesor2').removeClass('is-valid is-invalid');

    // Clear hidden fields
    $('#sekolah_id, #kd_asesor1, #kd_asesor2').val('');

    // Clear feedback messages
    $('#npsn_feedback, #asesor1_feedback, #asesor2_feedback').text('').hide();
    $('#sekolah_info, #asesor1_info, #asesor2_info').text('');
}

// Fungsi untuk validasi NPSN
function validateNPSN(npsn) {
    $.ajax({
        url: 'ajax/validate_npsn.php',
        type: 'POST',
        data: { npsn: npsn },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $('#npsn_sekolah').removeClass('is-invalid').addClass('is-valid');
                $('#sekolah_id').val(response.data.sekolah_id);
                $('#sekolah_info').text(response.data.nama_sekolah + ' - ' + response.data.jenjang + ' - ' + response.data.kota);
                $('#npsn_feedback').hide();
            } else {
                $('#npsn_sekolah').removeClass('is-valid').addClass('is-invalid');
                $('#npsn_feedback').text(response.message).show();
                $('#sekolah_id').val('');
                $('#sekolah_info').text('');
            }
        },
        error: function() {
            $('#npsn_sekolah').removeClass('is-valid').addClass('is-invalid');
            $('#npsn_feedback').text('Terjadi kesalahan saat validasi NPSN').show();
            $('#sekolah_id').val('');
            $('#sekolah_info').text('');
        }
    });
}

// Fungsi untuk validasi asesor
function validateAsesor(nia, asesorType) {
    var table = (asesorType === 1) ? 'asesor_1' : 'asesor_2';

    $.ajax({
        url: 'ajax/validate_asesor.php',
        type: 'POST',
        data: {
            nia: nia,
            asesor_type: asesorType
        },
        dataType: 'json',
        success: function(response) {
            var inputField = '#nia_asesor' + asesorType;
            var hiddenField = '#kd_asesor' + asesorType;
            var feedbackField = '#asesor' + asesorType + '_feedback';
            var infoField = '#asesor' + asesorType + '_info';

            if (response.success) {
                $(inputField).removeClass('is-invalid').addClass('is-valid');
                $(hiddenField).val(response.data.kd_asesor);
                $(infoField).text(response.data.nama + ' - ' + response.data.kota);
                $(feedbackField).hide();
            } else {
                $(inputField).removeClass('is-valid').addClass('is-invalid');
                $(feedbackField).text(response.message).show();
                $(hiddenField).val('');
                $(infoField).text('');
            }
        },
        error: function() {
            var inputField = '#nia_asesor' + asesorType;
            var hiddenField = '#kd_asesor' + asesorType;
            var feedbackField = '#asesor' + asesorType + '_feedback';
            var infoField = '#asesor' + asesorType + '_info';

            $(inputField).removeClass('is-valid').addClass('is-invalid');
            $(feedbackField).text('Terjadi kesalahan saat validasi NIA').show();
            $(hiddenField).val('');
            $(infoField).text('');
        }
    });
}

// Fungsi untuk simpan data mapping
function simpanDataMapping() {
    // Validasi form
    if (!$('#formInputMapping')[0].checkValidity()) {
        $('#formInputMapping')[0].reportValidity();
        return;
    }

    // Validasi bahwa lookup sudah berhasil
    if (!$('#sekolah_id').val()) {
        showAlert('error', 'NPSN belum valid atau belum divalidasi');
        return;
    }

    if (!$('#kd_asesor1').val()) {
        showAlert('error', 'NIA Asesor 1 belum valid atau belum divalidasi');
        return;
    }

    if (!$('#kd_asesor2').val()) {
        showAlert('error', 'NIA Asesor 2 belum valid atau belum divalidasi');
        return;
    }

    // Validasi asesor tidak boleh sama
    if ($('#kd_asesor1').val() === $('#kd_asesor2').val()) {
        showAlert('error', 'Asesor 1 dan Asesor 2 tidak boleh sama');
        return;
    }

    // Disable button saat proses simpan
    $('#btnSimpanMapping').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    // Ambil data form
    var formData = $('#formInputMapping').serialize();

    $.ajax({
        url: 'ajax/simpan_mapping.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showAlert('success', 'Data mapping berhasil disimpan');
                $('#modalInputMapping').modal('hide');

                // Refresh DataTable
                if ($.fn.DataTable.isDataTable('#table-mapping-visitasi')) {
                    $('#table-mapping-visitasi').DataTable().ajax.reload(null, false);
                }
            } else {
                showAlert('error', response.message);
            }
        },
        error: function() {
            showAlert('error', 'Terjadi kesalahan saat menyimpan data');
        },
        complete: function() {
            // Re-enable button
            $('#btnSimpanMapping').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Data');
        }
    });
}

function showAlert(type, message) {
    let icon = 'info';
    let title = 'Informasi';

    switch(type) {
        case 'success':
            icon = 'success';
            title = 'Berhasil';
            break;
        case 'error':
            icon = 'error';
            title = 'Error';
            break;
        case 'warning':
            icon = 'warning';
            title = 'Peringatan';
            break;
    }

    Swal.fire({
        icon: icon,
        title: title,
        text: message,
        confirmButtonText: 'OK'
    });
}
