$(document).ready(function() {
    // Initialize DataTable
    initDataTable();
    
    // Event handlers untuk action buttons
    $('#btn-input-mapping').on('click', function() {
        showAlert('info', 'Fitur input mapping akan segera tersedia');
    });
    
    $('#btn-export-excel').on('click', function() {
        showAlert('info', 'Fitur export Excel akan segera tersedia');
    });
    
    $('#btn-import-excel').on('click', function() {
        showAlert('info', 'Fitur import Excel akan segera tersedia');
    });
    
    $('#btn-tahun-akreditasi').on('click', function() {
        showAlert('info', 'Fitur tahun akreditasi akan segera tersedia');
    });
    
    // Event handler untuk tombol detail
    $(document).on('click', '.btn-detail-visitasi', function(e) {
        e.preventDefault();
        var idMapping = $(this).data('id');
        showAlert('info', 'Fitur detail visitasi akan segera tersedia');
    });
});

function initDataTable() {
    $('#table-mapping-visitasi').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: 'ajax/get_mapping_visitasi.php',
            type: 'POST',
            error: function(xhr, error, code) {
                console.error('DataTable AJAX Error:', error);
                showAlert('error', 'Gagal memuat data mapping visitasi');
            }
        },
        columns: [
            { 
                data: null,
                name: 'no',
                orderable: false,
                searchable: false,
                render: function(data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { data: 'npsn', name: 'npsn' },
            { data: 'nama_sekolah', name: 'nama_sekolah' },
            { data: 'nm_jenjang', name: 'nm_jenjang' },
            { data: 'rumpun', name: 'rumpun' },
            { data: 'nm_kota', name: 'nm_kota' },
            { 
                data: 'nia1', 
                name: 'nia1',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { 
                data: 'nm_asesor1', 
                name: 'nm_asesor1',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { 
                data: 'nia2', 
                name: 'nia2',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { 
                data: 'nm_asesor2', 
                name: 'nm_asesor2',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { data: 'tahun_akreditasi', name: 'tahun_akreditasi' },
            { data: 'tahap', name: 'tahap' },
            {
                data: null,
                name: 'aksi',
                orderable: false,
                searchable: false,
                render: function(data, type, row) {
                    return '<button type="button" class="btn btn-info btn-sm btn-detail-visitasi" data-id="' + row.id_mapping + '" title="Detail Visitasi">' +
                           '<i class="fas fa-eye"></i>' +
                           '</button>';
                }
            }
        ],
        order: [[1, 'asc']], // Order by NPSN
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]],
        language: {
            processing: "Memproses...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(disaring dari _MAX_ total data)",
            loadingRecords: "Memuat...",
            zeroRecords: "Tidak ada data yang ditemukan",
            emptyTable: "Tidak ada data mapping visitasi",
            paginate: {
                first: "Pertama",
                previous: "Sebelumnya",
                next: "Selanjutnya",
                last: "Terakhir"
            }
        },
        responsive: true,
        scrollX: true,
        dom: '<"row"<"col-sm-6"l><"col-sm-6"f>>' +
             '<"row"<"col-sm-12"tr>>' +
             '<"row"<"col-sm-5"i><"col-sm-7"p>>',
        drawCallback: function(settings) {
            // DataTable draw completed
        }
    });
}

function showAlert(type, message) {
    let icon = 'info';
    let title = 'Informasi';
    
    switch(type) {
        case 'success':
            icon = 'success';
            title = 'Berhasil';
            break;
        case 'error':
            icon = 'error';
            title = 'Error';
            break;
        case 'warning':
            icon = 'warning';
            title = 'Peringatan';
            break;
    }
    
    Swal.fire({
        icon: icon,
        title: title,
        text: message,
        confirmButtonText: 'OK'
    });
}
