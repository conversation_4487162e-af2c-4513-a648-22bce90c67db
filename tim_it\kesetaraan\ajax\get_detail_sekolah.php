<?php
/**
 * AJAX handler untuk mengambil detail sekolah
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi input
    if (!isset($_POST['sekolah_id']) || empty($_POST['sekolah_id'])) {
        throw new Exception('ID Sekolah tidak valid');
    }
    
    $sekolah_id = intval($_POST['sekolah_id']);
    
    // Query untuk mengambil detail sekolah dengan JOIN
    $query = "SELECT s.*, 
                     j.nm_jenjang,
                     k.nm_kota,
                     ts.nm_tipe_sekolah,
                     ss.nm_status_sekolah,
                     sk.nm_status as nm_status_keaktifan
              FROM sekolah s
              LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
              LEFT JOIN kab_kota k ON s.kota_id = k.kota_id  
              LEFT JOIN tipe_sekolah ts ON s.tipe_sekolah_id = ts.tipe_sekolah_id
              LEFT JOIN status_sekolah ss ON s.status_sekolah_id = ss.status_sekolah_id
              LEFT JOIN status_keaktifan sk ON s.status_keaktifan_id = sk.status_keaktifan_id
              WHERE s.sekolah_id = ? AND s.soft_delete = 1 AND s.rumpun = 'kesetaraan'";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $sekolah_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        throw new Exception('Data sekolah tidak ditemukan');
    }
    
    $data = $result->fetch_assoc();
    
    // Format data untuk response
    $response_data = [
        'sekolah_id' => $data['sekolah_id'],
        'npsn' => $data['npsn'],
        'nama_sekolah' => $data['nama_sekolah'],
        'nm_jenjang' => $data['nm_jenjang'],
        'rumpun' => $data['rumpun'],
        'alamat' => $data['alamat'],
        'nm_tipe_sekolah' => $data['nm_tipe_sekolah'],
        'nm_status_sekolah' => $data['nm_status_sekolah'],
        'nm_kota' => $data['nm_kota'],
        'desa_kelurahan' => $data['desa_kelurahan'],
        'kecamatan' => $data['kecamatan'],
        'nama_kepsek' => $data['nama_kepsek'],
        'no_hp_kepsek' => $data['no_hp_kepsek'],
        'no_wa_kepsek' => $data['no_wa_kepsek'],
        'nama_operator' => $data['nama_operator'],
        'no_hp_operator' => $data['no_hp_operator'],
        'no_wa_operator' => $data['no_wa_operator'],
        'email' => $data['email'],
        'nama_yayasan' => $data['nama_yayasan'],
        'no_akte' => $data['no_akte'],
        'tahun_berdiri' => $data['tahun_berdiri'],
        'nm_status_keaktifan' => $data['nm_status_keaktifan'],
        'status_keaktifan_id' => $data['status_keaktifan_id']
    ];
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data berhasil diambil',
        'data' => $response_data
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Log error
    error_log("Get Detail Sekolah Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
}
?>
