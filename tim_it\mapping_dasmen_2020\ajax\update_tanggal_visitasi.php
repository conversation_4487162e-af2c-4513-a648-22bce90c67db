<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$id_mapping = isset($_POST['id_mapping']) ? intval($_POST['id_mapping']) : 0;
$tgl_mulai_visitasi = isset($_POST['tgl_mulai_visitasi']) ? $_POST['tgl_mulai_visitasi'] : '';
$tgl_akhir_visitasi = isset($_POST['tgl_akhir_visitasi']) ? $_POST['tgl_akhir_visitasi'] : '';

// Validasi input
if ($id_mapping <= 0) {
    echo json_encode(['success' => false, 'message' => 'ID mapping tidak valid']);
    exit;
}

// Validasi tanggal
if (!empty($tgl_mulai_visitasi) && !empty($tgl_akhir_visitasi)) {
    if (strtotime($tgl_mulai_visitasi) > strtotime($tgl_akhir_visitasi)) {
        echo json_encode(['success' => false, 'message' => 'Tanggal mulai visitasi tidak boleh lebih besar dari tanggal akhir visitasi']);
        exit;
    }
}

try {
    // Cek apakah mapping exists dan milik provinsi yang sama
    $checkQuery = "SELECT id_mapping FROM mapping WHERE id_mapping = ? AND provinsi_id = ?";
    $checkStmt = $conn->prepare($checkQuery);
    $checkStmt->bind_param("ii", $id_mapping, $_SESSION['provinsi_id']);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();
    
    if ($checkResult->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Data mapping tidak ditemukan atau tidak memiliki akses']);
        exit;
    }
    
    // Prepare update query
    $updateFields = [];
    $updateValues = [];
    $types = '';
    
    if (!empty($tgl_mulai_visitasi)) {
        $updateFields[] = "tgl_mulai_visitasi = ?";
        $updateValues[] = $tgl_mulai_visitasi;
        $types .= 's';
    } else {
        $updateFields[] = "tgl_mulai_visitasi = NULL";
    }
    
    if (!empty($tgl_akhir_visitasi)) {
        $updateFields[] = "tgl_akhir_visitasi = ?";
        $updateValues[] = $tgl_akhir_visitasi;
        $types .= 's';
    } else {
        $updateFields[] = "tgl_akhir_visitasi = NULL";
    }
    
    // Add id_mapping untuk WHERE clause
    $updateValues[] = $id_mapping;
    $updateValues[] = $_SESSION['provinsi_id'];
    $types .= 'ii';
    
    $updateQuery = "UPDATE mapping SET " . implode(', ', $updateFields) . " WHERE id_mapping = ? AND provinsi_id = ?";
    
    $updateStmt = $conn->prepare($updateQuery);
    $updateStmt->bind_param($types, ...$updateValues);
    
    if ($updateStmt->execute()) {
        if ($updateStmt->affected_rows > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Tanggal visitasi berhasil diupdate',
                'data' => [
                    'tgl_mulai_visitasi' => $tgl_mulai_visitasi,
                    'tgl_akhir_visitasi' => $tgl_akhir_visitasi
                ]
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Tidak ada perubahan data']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Gagal mengupdate tanggal visitasi']);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
