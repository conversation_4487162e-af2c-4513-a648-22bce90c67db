<?php
/**
 * AJAX handler untuk mendapatkan form edit sekolah dengan data yang sudah ter-populate
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input
    if (!isset($_POST['sekolah_id']) || empty($_POST['sekolah_id'])) {
        throw new Exception('ID sekolah harus diisi');
    }
    
    $sekolah_id = intval($_POST['sekolah_id']);
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Query untuk mendapatkan data sekolah
    $sql = "SELECT s.*, j.nm_jenjang, ts.nm_tipe_sekolah, ss.nm_status_sekolah, k.nm_kota, sk.nm_status
            FROM sekolah s
            LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
            LEFT JOIN tipe_sekolah ts ON s.tipe_sekolah_id = ts.tipe_sekolah_id
            LEFT JOIN status_sekolah ss ON s.status_sekolah_id = ss.status_sekolah_id
            LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
            LEFT JOIN status_keaktifan sk ON s.status_keaktifan_id = sk.status_keaktifan_id
            WHERE s.sekolah_id = ? AND s.provinsi_id = ? AND s.soft_delete = 1";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $sekolah_id, $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        throw new Exception('Data sekolah tidak ditemukan');
    }
    
    $row = $result->fetch_assoc();
    
    // Generate form HTML dengan data yang sudah ter-populate
    ob_start();
?>
<div class="modal-header bg-success">
    <h5 class="modal-title">
        <i class="fas fa-edit"></i> Edit Data Sekolah paud
    </h5>
    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<form id="form-edit-sekolah" novalidate>
    <div class="modal-body">
        <div class="row">
            <!-- Kolom Kiri -->
            <div class="col-md-6">
                <h6 class="text-primary mb-3"><i class="fas fa-school"></i> Informasi Dasar Sekolah</h6>
                
                <div class="form-group">
                    <label for="edit_npsn">NPSN <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="edit_npsn" name="npsn" 
                           value="<?php echo htmlspecialchars($row['npsn']); ?>" required>
                    <small class="form-text text-muted">Nomor Pokok Sekolah Nasional</small>
                </div>
                
                <div class="form-group">
                    <label for="edit_nama_sekolah">Nama Sekolah <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="edit_nama_sekolah" name="nama_sekolah" 
                           value="<?php echo htmlspecialchars($row['nama_sekolah']); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="edit_jenjang_id">Jenjang <span class="text-danger">*</span></label>
                    <select class="form-control" id="edit_jenjang_id" name="jenjang_id" required>
                        <option value="">Pilih Jenjang</option>
                        <?php
                        $jenjang_sql = "SELECT * FROM jenjang ORDER BY nm_jenjang ASC";
                        $jenjang_result = $conn->query($jenjang_sql);
                        while($jenjang_row = $jenjang_result->fetch_assoc()) {
                        ?>
                            <option value="<?php echo $jenjang_row['jenjang_id']; ?>" 
                                <?php if (!(strcmp($row['jenjang_id'], $jenjang_row['jenjang_id']))) {echo "SELECTED";} ?>>
                                <?php echo htmlspecialchars($jenjang_row['nm_jenjang']); ?>
                            </option>
                        <?php
                        }
                        ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="edit_tipe_sekolah_id">Tipe Sekolah <span class="text-danger">*</span></label>
                    <select class="form-control" id="edit_tipe_sekolah_id" name="tipe_sekolah_id" required>
                        <option value="">Pilih Tipe Sekolah</option>
                        <?php
                        $tipe_sql = "SELECT * FROM tipe_sekolah ORDER BY nm_tipe_sekolah ASC";
                        $tipe_result = $conn->query($tipe_sql);
                        while($tipe_row = $tipe_result->fetch_assoc()) {
                        ?>
                            <option value="<?php echo $tipe_row['tipe_sekolah_id']; ?>" 
                                <?php if (!(strcmp($row['tipe_sekolah_id'], $tipe_row['tipe_sekolah_id']))) {echo "SELECTED";} ?>>
                                <?php echo htmlspecialchars($tipe_row['nm_tipe_sekolah']); ?>
                            </option>
                        <?php
                        }
                        ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="edit_status_sekolah_id">Status Sekolah <span class="text-danger">*</span></label>
                    <select class="form-control" id="edit_status_sekolah_id" name="status_sekolah_id" required>
                        <option value="">Pilih Status Sekolah</option>
                        <?php
                        $status_sql = "SELECT * FROM status_sekolah ORDER BY nm_status_sekolah ASC";
                        $status_result = $conn->query($status_sql);
                        while($status_row = $status_result->fetch_assoc()) {
                        ?>
                            <option value="<?php echo $status_row['status_sekolah_id']; ?>" 
                                <?php if (!(strcmp($row['status_sekolah_id'], $status_row['status_sekolah_id']))) {echo "SELECTED";} ?>>
                                <?php echo htmlspecialchars($status_row['nm_status_sekolah']); ?>
                            </option>
                        <?php
                        }
                        ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="edit_tahun_berdiri">Tahun Berdiri</label>
                    <input type="text" class="form-control" id="edit_tahun_berdiri" name="tahun_berdiri"
                           value="<?php echo htmlspecialchars($row['tahun_berdiri']); ?>">
                </div>
            </div>
            
            <!-- Kolom Kanan -->
            <div class="col-md-6">
                <h6 class="text-primary mb-3"><i class="fas fa-map-marker-alt"></i> Informasi Lokasi</h6>
                
                <div class="form-group">
                    <label for="edit_kota_id">Kabupaten/Kota <span class="text-danger">*</span></label>
                    <select class="form-control" id="edit_kota_id" name="kota_id" required>
                        <option value="">Pilih Kab/Kota</option>
                        <?php
                        $kota_sql = "SELECT * FROM kab_kota WHERE provinsi_id = ? ORDER BY nm_kota ASC";
                        $kota_stmt = $conn->prepare($kota_sql);
                        $kota_stmt->bind_param("i", $provinsi_id);
                        $kota_stmt->execute();
                        $kota_result = $kota_stmt->get_result();
                        while($kota_row = $kota_result->fetch_assoc()) {
                        ?>
                            <option value="<?php echo $kota_row['kota_id']; ?>" 
                                <?php if (!(strcmp($row['kota_id'], $kota_row['kota_id']))) {echo "SELECTED";} ?>>
                                <?php echo htmlspecialchars($kota_row['nm_kota']); ?>
                            </option>
                        <?php
                        }
                        ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="edit_kecamatan">Kecamatan</label>
                    <input type="text" class="form-control" id="edit_kecamatan" name="kecamatan" 
                           value="<?php echo htmlspecialchars($row['kecamatan']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="edit_desa_kelurahan">Desa/Kelurahan</label>
                    <input type="text" class="form-control" id="edit_desa_kelurahan" name="desa_kelurahan" 
                           value="<?php echo htmlspecialchars($row['desa_kelurahan']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="edit_alamat">Alamat Lengkap</label>
                    <textarea class="form-control" id="edit_alamat" name="alamat" rows="3"><?php echo htmlspecialchars($row['alamat']); ?></textarea>
                    <small class="form-text text-muted">Khusus untuk sekolah swasta</small>
                </div>
                
                <div class="form-group">
                    <label for="edit_nama_yayasan">Nama Yayasan</label>
                    <input type="text" class="form-control" id="edit_nama_yayasan" name="nama_yayasan" 
                           value="<?php echo htmlspecialchars($row['nama_yayasan']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="edit_no_akte">No. Akte</label>
                    <input type="text" class="form-control" id="edit_no_akte" name="no_akte"
                           value="<?php echo htmlspecialchars($row['no_akte']); ?>">
                    <small class="form-text text-muted">Nomor akte pendirian yayasan</small>
                </div>
            </div>
        </div>

        <!-- Informasi Kontak -->
        <div class="row mt-4">
            <div class="col-md-6">
                <h6 class="text-primary mb-3"><i class="fas fa-user-tie"></i> Informasi Kepala Sekolah</h6>

                <div class="form-group">
                    <label for="edit_nama_kepsek">Nama Kepala Sekolah</label>
                    <input type="text" class="form-control" id="edit_nama_kepsek" name="nama_kepsek"
                           value="<?php echo htmlspecialchars($row['nama_kepsek']); ?>">
                </div>

                <div class="form-group">
                    <label for="edit_no_hp_kepsek">No. HP Kepala Sekolah</label>
                    <input type="text" class="form-control" id="edit_no_hp_kepsek" name="no_hp_kepsek"
                           value="<?php echo htmlspecialchars($row['no_hp_kepsek']); ?>">
                </div>

                <div class="form-group">
                    <label for="edit_no_wa_kepsek">No. WhatsApp Kepala Sekolah</label>
                    <input type="text" class="form-control" id="edit_no_wa_kepsek" name="no_wa_kepsek"
                           value="<?php echo htmlspecialchars($row['no_wa_kepsek']); ?>">
                </div>
            </div>

            <div class="col-md-6">
                <h6 class="text-primary mb-3"><i class="fas fa-user-cog"></i> Informasi Operator</h6>

                <div class="form-group">
                    <label for="edit_nama_operator">Nama Operator</label>
                    <input type="text" class="form-control" id="edit_nama_operator" name="nama_operator"
                           value="<?php echo htmlspecialchars($row['nama_operator']); ?>">
                </div>

                <div class="form-group">
                    <label for="edit_no_hp_operator">No. HP Operator</label>
                    <input type="text" class="form-control" id="edit_no_hp_operator" name="no_hp_operator"
                           value="<?php echo htmlspecialchars($row['no_hp_operator']); ?>">
                </div>

                <div class="form-group">
                    <label for="edit_no_wa_operator">No. WhatsApp Operator</label>
                    <input type="text" class="form-control" id="edit_no_wa_operator" name="no_wa_operator"
                           value="<?php echo htmlspecialchars($row['no_wa_operator']); ?>">
                </div>

                <div class="form-group">
                    <label for="edit_email">Email</label>
                    <input type="email" class="form-control" id="edit_email" name="email"
                           value="<?php echo htmlspecialchars($row['email']); ?>">
                </div>

                <div class="form-group">
                    <label for="edit_status_keaktifan_id">Status Keaktifan <span class="text-danger">*</span></label>
                    <select class="form-control" id="edit_status_keaktifan_id" name="status_keaktifan_id" required>
                        <option value="">Pilih Status Keaktifan</option>
                        <?php
                        $status_keaktifan_sql = "SELECT * FROM status_keaktifan ORDER BY status_keaktifan_id ASC";
                        $status_keaktifan_result = $conn->query($status_keaktifan_sql);
                        if ($status_keaktifan_result) {
                            while($status_keaktifan_row = $status_keaktifan_result->fetch_assoc()) {
                        ?>
                            <option value="<?php echo $status_keaktifan_row['status_keaktifan_id']; ?>"
                                <?php if (!(strcmp($row['status_keaktifan_id'], $status_keaktifan_row['status_keaktifan_id']))) {echo "SELECTED";} ?>>
                                <?php echo htmlspecialchars($status_keaktifan_row['nm_status']); ?>
                            </option>
                        <?php
                            }
                        } else {
                            // Fallback jika query gagal
                        ?>
                            <option value="0" <?php if ($row['status_keaktifan_id'] == '0') echo "SELECTED"; ?>>Tidak Aktif</option>
                            <option value="1" <?php if ($row['status_keaktifan_id'] == '1') echo "SELECTED"; ?>>Aktif</option>
                            <option value="2" <?php if ($row['status_keaktifan_id'] == '2') echo "SELECTED"; ?>>Tidak Diketahui</option>
                        <?php
                        }
                        ?>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- Hidden fields -->
        <input type="hidden" name="sekolah_id" value="<?php echo $row['sekolah_id']; ?>">
        <input type="hidden" name="rumpun" value="paud">
        <input type="hidden" name="provinsi_id" value="<?php echo $_SESSION['provinsi_id']; ?>">
        <input type="hidden" name="soft_delete" value="1">
    </div>
    
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">
            <i class="fas fa-times"></i> Batal
        </button>
        <button type="button" class="btn btn-warning" id="btn-update">
            <i class="fas fa-save"></i> Update Data
        </button>
    </div>
</form>
<?php
    $form_html = ob_get_clean();
    
    // Return response
    echo json_encode([
        'success' => true,
        'html' => $form_html,
        'data' => $row
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
