<?php
// Set timezone untuk Kalimantan Timur
date_default_timezone_set('Asia/Makassar');

// Konfigurasi database
$host = "localhost";
$username = "root";
$password = "8@n90N3!";
$database = "sim4k";

// Membuat koneksi ke database MariaDB menggunakan MySQLi
$conn = new mysqli($host, $username, $password, $database);

// Cek koneksi
if ($conn->connect_error) {
    // Log error untuk debugging (jangan tampilkan ke user di production)
    error_log("Database connection failed: " . $conn->connect_error);
    die("Koneksi database gagal. Silakan hubungi administrator.");
}

// Set charset ke utf8mb4 untuk mendukung karakter Unicode penuh
$conn->set_charset("utf8mb4");

// Fungsi untuk escape string (mencegah SQL injection)
function escape_string($data) {
    global $conn;
    return $conn->real_escape_string($data);
}

// Fungsi untuk menutup koneksi
function closeConnection() {
    global $conn;
    $conn->close();
}
?>
