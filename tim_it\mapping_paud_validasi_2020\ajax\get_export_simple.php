<?php
// Simple export untuk testing
ob_clean();
error_reporting(0);
ini_set('display_errors', 0);

require '../../../koneksi.php';
require '../../../check_session.php';

header('Content-Type: application/json');

// Check if user is Staff IT
if (!isset($_SESSION['level']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['success' => false, 'message' => '<PERSON><PERSON><PERSON> ditolak']);
    exit;
}

try {
    // Simple query first
    $provinsi_id = $_SESSION['provinsi_id'];
    
    $query = "SELECT 
                mp.id_mapping,
                mp.tahun_akreditasi,
                mp.tahap,
                s.nama_sekolah,
                s.npsn,
                j.nm_jenjang,
                k.nm_kota
              FROM mapping_paud_validasi mp
              LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
              LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
              LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
              WHERE mp.provinsi_id = ?
                AND s.rumpun = 'paud'
                AND s.soft_delete = '1'
              LIMIT 10";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = [
            'id_mapping' => $row['id_mapping'],
            'npsn' => $row['npsn'],
            'nama_sekolah' => $row['nama_sekolah'],
            'nm_jenjang' => $row['nm_jenjang'],
            'nm_kota' => $row['nm_kota'],
            'tahun_akreditasi' => $row['tahun_akreditasi'],
            'tahap' => $row['tahap']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Data berhasil diambil',
        'data' => $data,
        'total_records' => count($data)
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
