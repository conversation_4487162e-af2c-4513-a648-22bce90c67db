<?php
// Start session first
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include files
require_once '../../../koneksi.php';

// Simple session check
if (!isset($_SESSION['kd_user']) || empty($_SESSION['kd_user'])) {
    echo json_encode(['success' => false, 'message' => 'Session tidak valid']);
    exit;
}

// Simple level check
if (!isset($_SESSION['level']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['success' => false, 'message' => 'Aks<PERSON> ditolak']);
    exit;
}

// Check provinsi_id
if (!isset($_SESSION['provinsi_id']) || empty($_SESSION['provinsi_id'])) {
    echo json_encode(['success' => false, 'message' => 'Data provinsi tidak ditemukan']);
    exit;
}

// Cek parameter
$required_fields = ['id_hasil_akreditasi', 'id_prog_ahli', 'peringkat', 'status', 'tahun_akreditasi', 'tahun_berakhir'];
foreach ($required_fields as $field) {
    if (!isset($_POST[$field]) || trim($_POST[$field]) === '') {
        echo json_encode(['success' => false, 'message' => 'Field ' . $field . ' harus diisi']);
        exit;
    }
}

$id_hasil_akreditasi = intval($_POST['id_hasil_akreditasi']);
$id_prog_ahli = intval($_POST['id_prog_ahli']);
$nilai_akhir = isset($_POST['nilai_akhir']) && $_POST['nilai_akhir'] !== '' ? intval($_POST['nilai_akhir']) : 0;
$peringkat = trim($_POST['peringkat']);
$status = trim($_POST['status']);
$tahun_akreditasi = intval($_POST['tahun_akreditasi']);
$tahun_berakhir = intval($_POST['tahun_berakhir']);
$tgl_sk_penetapan = isset($_POST['tgl_sk_penetapan']) && $_POST['tgl_sk_penetapan'] !== '' ? $_POST['tgl_sk_penetapan'] : null;
$no_sk = isset($_POST['no_sk']) ? trim($_POST['no_sk']) : '';

// Get provinsi_id from session
$provinsi_id = $_SESSION['provinsi_id'];

try {
    // Cek apakah data exists dan milik provinsi yang sama
    $check_query = "SELECT ha.sekolah_id, s.provinsi_id
                    FROM hasil_akreditasi ha
                    LEFT JOIN sekolah s ON ha.sekolah_id = s.sekolah_id
                    WHERE ha.id_hasil_akreditasi = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("i", $id_hasil_akreditasi);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();

    if ($check_result->num_rows == 0) {
        echo json_encode(['success' => false, 'message' => 'Data tidak ditemukan']);
        exit;
    }

    $check_data = $check_result->fetch_assoc();
    if ($check_data['provinsi_id'] != $provinsi_id) {
        echo json_encode(['success' => false, 'message' => 'Anda tidak memiliki akses untuk mengedit data ini']);
        exit;
    }
    
    // Validasi prog_ahli
    $valid_prog_ahli = [77, 79, 80, 82, 83, 84];
    if (!in_array($id_prog_ahli, $valid_prog_ahli)) {
        echo json_encode(['success' => false, 'message' => 'Program keahlian tidak valid']);
        exit;
    }
    
    // Validasi peringkat
    $valid_peringkat = ['A', 'B', 'C', 'TT'];
    if (!in_array($peringkat, $valid_peringkat)) {
        echo json_encode(['success' => false, 'message' => 'Peringkat tidak valid']);
        exit;
    }
    
    // Validasi status
    $valid_status = ['Terakreditasi', 'Tidak Terakreditasi'];
    if (!in_array($status, $valid_status)) {
        echo json_encode(['success' => false, 'message' => 'Status tidak valid']);
        exit;
    }
    
    // Validasi tahun
    $current_year = date('Y');
    if ($tahun_akreditasi < 2000 || $tahun_akreditasi > ($current_year + 10)) {
        echo json_encode(['success' => false, 'message' => 'Tahun akreditasi tidak valid']);
        exit;
    }
    
    if ($tahun_berakhir < 2000 || $tahun_berakhir > ($current_year + 20)) {
        echo json_encode(['success' => false, 'message' => 'Tahun berakhir tidak valid']);
        exit;
    }
    
    if ($tahun_berakhir <= $tahun_akreditasi) {
        echo json_encode(['success' => false, 'message' => 'Tahun berakhir harus lebih besar dari tahun akreditasi']);
        exit;
    }
    
    // Cek duplikasi data (sekolah_id + id_prog_ahli + tahun_akreditasi) kecuali data yang sedang diedit
    $dup_query = "SELECT id_hasil_akreditasi FROM hasil_akreditasi 
                  WHERE sekolah_id = ? AND id_prog_ahli = ? AND tahun_akreditasi = ? 
                  AND id_hasil_akreditasi != ?";
    $dup_stmt = $conn->prepare($dup_query);
    $dup_stmt->bind_param("iiii", $check_data['sekolah_id'], $id_prog_ahli, $tahun_akreditasi, $id_hasil_akreditasi);
    $dup_stmt->execute();
    $dup_result = $dup_stmt->get_result();
    
    if ($dup_result->num_rows > 0) {
        echo json_encode(['success' => false, 'message' => 'Data dengan program keahlian dan tahun yang sama sudah ada']);
        exit;
    }
    
    // Update data
    $update_query = "UPDATE hasil_akreditasi SET 
                     id_prog_ahli = ?, 
                     nilai_akhir = ?, 
                     peringkat = ?, 
                     status = ?, 
                     tahun_akreditasi = ?, 
                     tahun_berakhir = ?, 
                     tgl_sk_penetapan = ?, 
                     no_sk = ?
                     WHERE id_hasil_akreditasi = ?";
    
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("iissiissi", 
        $id_prog_ahli, 
        $nilai_akhir, 
        $peringkat, 
        $status, 
        $tahun_akreditasi, 
        $tahun_berakhir, 
        $tgl_sk_penetapan, 
        $no_sk, 
        $id_hasil_akreditasi
    );
    
    if ($update_stmt->execute()) {
        echo json_encode([
            'success' => true, 
            'message' => 'Data nilai akreditasi berhasil diperbarui'
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Gagal memperbarui data']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
}

$conn->close();
?>
