<?php
/**
 * AJAX handler untuk menampilkan detail pengurus
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    if (!isset($_POST['id_pengurus']) || empty($_POST['id_pengurus'])) {
        throw new Exception('ID Pengurus tidak valid');
    }
    
    $id_pengurus = intval($_POST['id_pengurus']);
    
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Query untuk mendapatkan data pengurus dengan filter session
    $sql = "SELECT p.*, k.nm_kota, pr.nama_provinsi
            FROM pengurus p
            LEFT JOIN kab_kota k ON p.kota_id = k.kota_id
            LEFT JOIN provinsi pr ON p.provinsi_id = pr.provinsi_id
            WHERE p.id_pengurus = ? AND p.provinsi_id = ? AND p.soft_delete = '1'";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $id_pengurus, $provinsi_id_session);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        throw new Exception('Data pengurus tidak ditemukan atau Anda tidak memiliki akses');
    }
    
    $row = $result->fetch_assoc();
    
    // Format tanggal lahir
    $tgl_lahir_formatted = '';
    if (!empty($row['tgl_lahir']) && $row['tgl_lahir'] != '0000-00-00' && $row['tgl_lahir'] != null) {
        $tgl_lahir_formatted = date('d-m-Y', strtotime($row['tgl_lahir']));
    }
    
    // Format status keaktifan
    $status_badge = '';
    switch($row['status_keaktifan_id']) {
        case '1':
            $status_badge = '<span class="badge badge-success">Aktif</span>';
            break;
        case '0':
            $status_badge = '<span class="badge badge-danger">Tidak Aktif</span>';
            break;
        case '2':
            $status_badge = '<span class="badge badge-warning">Tidak Diketahui</span>';
            break;
        default:
            $status_badge = '<span class="badge badge-secondary">-</span>';
    }
    
    ?>
    
    <div class="row">
        <!-- Kolom Kiri -->
        <div class="col-md-6">
            <table class="table table-borderless">
                <tr>
                    <td width="40%"><strong>Kode Pengurus</strong></td>
                    <td width="5%">:</td>
                    <td><?php echo htmlspecialchars($row['kd_pengurus'] ?: '-'); ?></td>
                </tr>
                <tr>
                    <td><strong>Nama Pengurus</strong></td>
                    <td>:</td>
                    <td><?php echo htmlspecialchars($row['nm_pengurus']); ?></td>
                </tr>
                <tr>
                    <td><strong>Jenis Kelamin</strong></td>
                    <td>:</td>
                    <td><?php echo htmlspecialchars($row['jk']); ?></td>
                </tr>
                <tr>
                    <td><strong>Nomor KTP</strong></td>
                    <td>:</td>
                    <td><?php echo htmlspecialchars($row['ktp'] ?: '-'); ?></td>
                </tr>
                <tr>
                    <td><strong>Tempat Lahir</strong></td>
                    <td>:</td>
                    <td><?php echo htmlspecialchars($row['tempat_lahir'] ?: '-'); ?></td>
                </tr>
                <tr>
                    <td><strong>Tanggal Lahir</strong></td>
                    <td>:</td>
                    <td><?php echo $tgl_lahir_formatted ?: '-'; ?></td>
                </tr>
                <tr>
                    <td><strong>Pendidikan</strong></td>
                    <td>:</td>
                    <td><?php echo htmlspecialchars($row['pendidikan'] ?: '-'); ?></td>
                </tr>
                <tr>
                    <td><strong>Jabatan</strong></td>
                    <td>:</td>
                    <td><?php echo htmlspecialchars($row['jabatan'] ?: '-'); ?></td>
                </tr>
                <tr>
                    <td><strong>Jabatan Kantor Asal</strong></td>
                    <td>:</td>
                    <td><?php echo htmlspecialchars($row['jabatan_kantor_asal'] ?: '-'); ?></td>
                </tr>
                <tr>
                    <td><strong>Unit Kerja</strong></td>
                    <td>:</td>
                    <td><?php echo htmlspecialchars($row['unit_kerja'] ?: '-'); ?></td>
                </tr>
                <tr>
                    <td><strong>Provinsi</strong></td>
                    <td>:</td>
                    <td><?php echo htmlspecialchars($row['nama_provinsi'] ?: '-'); ?></td>
                </tr>
                <tr>
                    <td><strong>Kabupaten/Kota</strong></td>
                    <td>:</td>
                    <td><?php echo htmlspecialchars($row['nm_kota'] ?: '-'); ?></td>
                </tr>
            </table>
        </div>
        
        <!-- Kolom Kanan -->
        <div class="col-md-6">
            <table class="table table-borderless">
                <tr>
                    <td width="40%"><strong>Nomor HP</strong></td>
                    <td width="5%">:</td>
                    <td><?php echo htmlspecialchars($row['no_hp'] ?: '-'); ?></td>
                </tr>
                <tr>
                    <td><strong>Nomor WhatsApp</strong></td>
                    <td>:</td>
                    <td><?php echo htmlspecialchars($row['no_wa'] ?: '-'); ?></td>
                </tr>
                <tr>
                    <td><strong>Email</strong></td>
                    <td>:</td>
                    <td><?php echo htmlspecialchars($row['email'] ?: '-'); ?></td>
                </tr>
                <tr>
                    <td><strong>Status Keaktifan</strong></td>
                    <td>:</td>
                    <td><?php echo $status_badge; ?></td>
                </tr>
                <tr>
                    <td><strong>Nomor Urut</strong></td>
                    <td>:</td>
                    <td><?php echo $row['no_urut'] ?: '-'; ?></td>
                </tr>
                <tr>
                    <td><strong>Kode User</strong></td>
                    <td>:</td>
                    <td><?php echo htmlspecialchars($row['kd_user'] ?: '-'); ?></td>
                </tr>
                <tr>
                    <td colspan="3"><strong>Alamat Kantor</strong></td>
                </tr>
                <tr>
                    <td colspan="3" class="pl-3"><?php echo htmlspecialchars($row['alamat_kantor'] ?: '-'); ?></td>
                </tr>
                <tr>
                    <td colspan="3"><strong>Alamat Rumah</strong></td>
                </tr>
                <tr>
                    <td colspan="3" class="pl-3"><?php echo htmlspecialchars($row['alamat_rumah'] ?: '-'); ?></td>
                </tr>
                <tr>
                    <td colspan="3"><strong>Sebab/Keterangan</strong></td>
                </tr>
                <tr>
                    <td colspan="3" class="pl-3"><?php echo htmlspecialchars($row['sebab'] ?: '-'); ?></td>
                </tr>
            </table>
        </div>
    </div>
    
    <?php
    
} catch (Exception $e) {
    echo '<div class="alert alert-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
