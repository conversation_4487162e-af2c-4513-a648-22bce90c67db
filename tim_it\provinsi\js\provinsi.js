/**
 * JavaScript untuk modul Data Provinsi
 */

$(document).ready(function() {
    // Inisialisasi DataTable
    initDataTable();
    
    // Event handlers
    initEventHandlers();
});

/**
 * Inisialisasi DataTable
 */
function initDataTable() {
    $('#table-provinsi').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: 'ajax/get_provinsi.php',
            type: 'POST',
            error: function(xhr, error, thrown) {
                console.error('DataTables error:', error);
                showAlert('error', 'Gagal memuat data provinsi');
            }
        },
        columns: [
            { 
                data: 'nama_provinsi',
                name: 'nama_provinsi'
            },
            {
                data: 'alamat_provinsi',
                name: 'alamat_provinsi',
                render: function(data, type, row) {
                    // <PERSON><PERSON><PERSON>an alamat secara lengkap tanpa dipotong
                    return data || '-';
                }
            },
            { 
                data: 'nm_kota',
                name: 'nm_kota',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            { 
                data: 'nama_ketua_banp',
                name: 'nama_ketua_banp',
                render: function(data, type, row) {
                    return data || '-';
                }
            },
            {
                data: null,
                orderable: false,
                searchable: false,
                render: function(data, type, row) {
                    return '<button class="btn btn-warning btn-sm" onclick="showEditModal(' + 
                           row.id_provinsi + ')" title="Edit">' +
                           '<i class="fas fa-edit"></i></button>';
                }
            }
        ],
        order: [[0, 'asc']],
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: "Memuat data...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(difilter dari _MAX_ total data)",
            loadingRecords: "Memuat data...",
            zeroRecords: "Tidak ada data yang ditemukan",
            emptyTable: "Tidak ada data tersedia",
            paginate: {
                first: "Pertama",
                last: "Terakhir",
                next: "Selanjutnya",
                previous: "Sebelumnya"
            }
        },
        responsive: true,
        autoWidth: false,
        columnDefs: [
            {
                targets: 1, // Kolom alamat_provinsi (index 1)
                className: 'text-wrap',
                width: '30%'
            },
            {
                targets: 4, // Kolom aksi (index 4)
                width: '80px'
            }
        ]
    });
}

/**
 * Inisialisasi event handlers
 */
function initEventHandlers() {
    // Event handler untuk form edit
    $('#form-edit-provinsi').on('submit', function(e) {
        e.preventDefault();
        updateProvinsi();
    });
}

/**
 * Fungsi untuk menampilkan modal edit
 */
function showEditModal(provinsiId) {
    // Show loading
    $('#modal-edit-content').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Memuat data...</div>');
    $('#modal-edit').modal('show');

    // Load form edit via AJAX
    $.ajax({
        url: 'ajax/get_edit_form.php',
        type: 'POST',
        data: { id_provinsi: provinsiId },
        dataType: 'html',
        success: function(response) {
            $('#modal-edit-content').html(response);
        },
        error: function(xhr, status, error) {
            $('#modal-edit-content').html('<div class="alert alert-danger">Gagal memuat form edit</div>');
        }
    });
}

/**
 * Fungsi untuk update data provinsi
 */
function updateProvinsi() {
    // Disable tombol submit
    $('#btn-update').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    // Serialize form data
    var formData = $('#form-edit-provinsi').serialize();

    $.ajax({
        url: 'ajax/update_provinsi.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Tutup modal
                $('#modal-edit').modal('hide');

                // Reload DataTable
                $('#table-provinsi').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat menyimpan data');
        },
        complete: function() {
            // Enable tombol submit
            $('#btn-update').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Perubahan');
        }
    });
}

/**
 * Fungsi untuk menampilkan alert
 */
function showAlert(type, message) {
    var alertClass = 'alert-info';
    var icon = 'fas fa-info-circle';
    var title = 'Informasi';

    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            icon = 'fas fa-check-circle';
            title = 'Berhasil';
            break;
        case 'error':
            alertClass = 'alert-danger';
            icon = 'fas fa-exclamation-circle';
            title = 'Error';
            break;
        case 'warning':
            alertClass = 'alert-warning';
            icon = 'fas fa-exclamation-triangle';
            title = 'Peringatan';
            break;
    }

    // Update modal notification
    $('#modal-notification-header').removeClass().addClass('modal-header ' + alertClass);
    $('#modal-notification-icon').removeClass().addClass(icon);
    $('#modal-notification-text').text(title);
    $('#modal-notification-message').text(message);

    // Show modal
    $('#modal-notification').modal('show');

    // Auto hide after 3 seconds for success messages
    if (type === 'success') {
        setTimeout(function() {
            $('#modal-notification').modal('hide');
        }, 3000);
    }
}
