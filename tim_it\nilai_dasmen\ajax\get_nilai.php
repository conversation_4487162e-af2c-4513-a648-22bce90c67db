<?php
/**
 * AJAX handler untuk DataTables server-side processing Data Nilai Akreditasi
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }

    // Ambil parameter DataTables
    $draw = isset($_POST['draw']) ? intval($_POST['draw']) : 1;
    $start = isset($_POST['start']) ? intval($_POST['start']) : 0;
    $length = isset($_POST['length']) ? intval($_POST['length']) : 10;
    $search_value = isset($_POST['search']['value']) ? trim($_POST['search']['value']) : '';
    $order_column = isset($_POST['order'][0]['column']) ? intval($_POST['order'][0]['column']) : 8;
    $order_dir = isset($_POST['order'][0]['dir']) ? $_POST['order'][0]['dir'] : 'desc';

    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];

    // Mapping kolom untuk ordering
    $columns = [
        0 => 'ha.id_hasil_akreditasi', // NO (tidak bisa di-sort)
        1 => 's.npsn',
        2 => 's.nama_sekolah',
        3 => 'j.nm_jenjang',
        4 => 'k.nm_kota',
        5 => 'ha.nilai_akhir',
        6 => 'ha.peringkat',
        7 => 'ha.status',
        8 => 'ha.tahun_akreditasi',
        9 => 'ha.tahun_berakhir',
        10 => 'ha.id_hasil_akreditasi'  // AKSI (tidak bisa di-sort)
    ];

    $order_by = isset($columns[$order_column]) ? $columns[$order_column] : 'ha.tahun_akreditasi';

    // Base query dengan JOIN dan filter untuk nilai akreditasi terakhir per sekolah
    $base_query = "FROM hasil_akreditasi ha
                   JOIN sekolah s ON ha.sekolah_id = s.sekolah_id
                   LEFT JOIN jenjang j ON s.jenjang_id = j.id_jenjang
                   LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
                   WHERE s.rumpun = 'dasmen'
                     AND ha.provinsi_id = {$provinsi_id_session}
                     AND s.status_keaktifan_id = '1'
                     AND ha.tahun_akreditasi = (
                         SELECT MAX(ha2.tahun_akreditasi)
                         FROM hasil_akreditasi ha2
                         WHERE ha2.sekolah_id = ha.sekolah_id
                     )";

    // WHERE clause untuk search
    // Kolom yang bisa dicari: NPSN, Nama Sekolah, Jenjang, Kab/Kota, Peringkat, Status, Tahun Akreditasi, Tahun Berakhir, Nilai Akhir
    $where_clause = "";
    $search_params = [];
    $param_types = "";

    if (!empty($search_value)) {
        $search_value_escaped = $conn->real_escape_string($search_value);
        $where_clause = " AND (s.npsn LIKE '%{$search_value_escaped}%' OR
                               s.nama_sekolah LIKE '%{$search_value_escaped}%' OR
                               j.nm_jenjang LIKE '%{$search_value_escaped}%' OR
                               k.nm_kota LIKE '%{$search_value_escaped}%' OR
                               ha.peringkat LIKE '%{$search_value_escaped}%' OR
                               ha.status LIKE '%{$search_value_escaped}%' OR
                               ha.tahun_akreditasi LIKE '%{$search_value_escaped}%' OR
                               ha.tahun_berakhir LIKE '%{$search_value_escaped}%' OR
                               ha.nilai_akhir LIKE '%{$search_value_escaped}%')";
    }

    // Query untuk total records (tanpa filter search)
    $total_query = "SELECT COUNT(*) as total " . $base_query;
    $result_total = $conn->query($total_query);
    $total_records = $result_total->fetch_assoc()['total'];

    // Query untuk filtered records (dengan filter search)
    $filtered_query = "SELECT COUNT(*) as total " . $base_query . $where_clause;
    $result_filtered = $conn->query($filtered_query);
    $filtered_records = $result_filtered->fetch_assoc()['total'];

    // Query untuk data dengan pagination
    $data_query = "SELECT ha.id_hasil_akreditasi, ha.sekolah_id, ha.nilai_akhir, ha.peringkat,
                          ha.status, ha.tahun_akreditasi, ha.tahun_berakhir,
                          s.npsn, s.nama_sekolah, j.nm_jenjang, k.nm_kota
                   " . $base_query . $where_clause . "
                   ORDER BY {$order_by} {$order_dir}
                   LIMIT {$start}, {$length}";

    $result_data = $conn->query($data_query);

    // Siapkan data untuk response
    $data = [];
    while ($row = $result_data->fetch_assoc()) {
        $data[] = [
            'id_hasil_akreditasi' => $row['id_hasil_akreditasi'],
            'sekolah_id' => $row['sekolah_id'],
            'npsn' => $row['npsn'],
            'nama_sekolah' => $row['nama_sekolah'],
            'nm_jenjang' => $row['nm_jenjang'],
            'nm_kota' => $row['nm_kota'],
            'nilai_akhir' => $row['nilai_akhir'],
            'peringkat' => $row['peringkat'],
            'status' => $row['status'],
            'tahun_akreditasi' => $row['tahun_akreditasi'],
            'tahun_berakhir' => $row['tahun_berakhir']
        ];
    }

    // Response DataTables
    $response = [
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $filtered_records,
        'data' => $data
    ];

    echo json_encode($response);

} catch (Exception $e) {
    // Log error
    error_log("Get Nilai Akreditasi Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'draw' => isset($draw) ? $draw : 1,
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => $e->getMessage()
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
