<?php
// Clean output buffer
ob_clean();
error_reporting(0);
ini_set('display_errors', 0);

require '../../../koneksi.php';
require '../../../check_session.php';

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Validasi level akses
try {
    requireLevel('Staff IT');
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Akses ditolak']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Ambil dan validasi input
$id_mapping = isset($_POST['id_mapping']) ? intval($_POST['id_mapping']) : 0;
$nia_validator = isset($_POST['kd_asesor1']) ? trim($_POST['kd_asesor1']) : '';
$nia_verifikator = isset($_POST['kd_asesor2']) ? trim($_POST['kd_asesor2']) : '';

if ($id_mapping <= 0) {
    echo json_encode(['success' => false, 'message' => 'ID mapping tidak valid']);
    exit;
}

if (empty($nia_validator) || empty($nia_verifikator)) {
    echo json_encode(['success' => false, 'message' => 'NIA Validator dan Verifikator wajib diisi']);
    exit;
}

if ($nia_validator === $nia_verifikator) {
    echo json_encode(['success' => false, 'message' => 'Validator dan Verifikator tidak boleh sama']);
    exit;
}

try {
    // Validasi koneksi database
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Validasi session provinsi_id
    if (!isset($_SESSION['provinsi_id'])) {
        throw new Exception("Session provinsi_id not found");
    }
    
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Validasi mapping exists dan belongs to user's province
    $check_query = "SELECT id_mapping FROM mapping_paud_validasi 
                   WHERE id_mapping = ? AND provinsi_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("ii", $id_mapping, $provinsi_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Data mapping tidak ditemukan']);
        exit;
    }
    
    // Lookup asesor 1 (validator) berdasarkan NIA
    $asesor1_query = "SELECT kd_asesor1, nia1, nm_asesor1, no_hp, kota_id1
                     FROM asesor_1
                     WHERE nia1 = ? AND provinsi_id = ? AND status_keaktifan_id = '1' AND soft_delete = '1'";
    $asesor1_stmt = $conn->prepare($asesor1_query);
    $asesor1_stmt->bind_param("si", $nia_validator, $provinsi_id);
    $asesor1_stmt->execute();
    $asesor1_result = $asesor1_stmt->get_result();

    if ($asesor1_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Validator dengan NIA ' . $nia_validator . ' tidak ditemukan atau tidak aktif']);
        exit;
    }

    $asesor1_data = $asesor1_result->fetch_assoc();
    $kd_asesor1 = $asesor1_data['kd_asesor1']; // Get kd_asesor1 dari hasil lookup

    // Lookup asesor 2 (verifikator) berdasarkan NIA
    $asesor2_query = "SELECT kd_asesor2, nia2, nm_asesor2, no_hp, kota_id2
                     FROM asesor_2
                     WHERE nia2 = ? AND provinsi_id = ? AND status_keaktifan_id = '1' AND soft_delete = '1'";
    $asesor2_stmt = $conn->prepare($asesor2_query);
    $asesor2_stmt->bind_param("si", $nia_verifikator, $provinsi_id);
    $asesor2_stmt->execute();
    $asesor2_result = $asesor2_stmt->get_result();

    if ($asesor2_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Verifikator dengan NIA ' . $nia_verifikator . ' tidak ditemukan atau tidak aktif']);
        exit;
    }

    $asesor2_data = $asesor2_result->fetch_assoc();
    $kd_asesor2 = $asesor2_data['kd_asesor2']; // Get kd_asesor2 dari hasil lookup
    
    // Update mapping_paud_validasi
    $update_query = "UPDATE mapping_paud_validasi 
                    SET kd_asesor1 = ?, kd_asesor2 = ? 
                    WHERE id_mapping = ? AND provinsi_id = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("ssii", $kd_asesor1, $kd_asesor2, $id_mapping, $provinsi_id);
    
    if (!$update_stmt->execute()) {
        throw new Exception("Update failed: " . $update_stmt->error);
    }
    
    if ($update_stmt->affected_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Tidak ada data yang diupdate']);
        exit;
    }
    
    // Get kab/kota data untuk asesor
    $kota1_query = "SELECT nm_kota FROM kab_kota WHERE kota_id = ?";
    $kota1_stmt = $conn->prepare($kota1_query);
    $kota1_stmt->bind_param("s", $asesor1_data['kota_id1']);
    $kota1_stmt->execute();
    $kota1_result = $kota1_stmt->get_result();
    $kota1_data = $kota1_result->fetch_assoc();
    
    $kota2_query = "SELECT nm_kota FROM kab_kota WHERE kota_id = ?";
    $kota2_stmt = $conn->prepare($kota2_query);
    $kota2_stmt->bind_param("s", $asesor2_data['kota_id2']);
    $kota2_stmt->execute();
    $kota2_result = $kota2_stmt->get_result();
    $kota2_data = $kota2_result->fetch_assoc();
    
    // Prepare response data
    $response_data = [
        'id_mapping' => $id_mapping,
        'nia1' => $asesor1_data['nia1'],
        'nm_asesor1' => $asesor1_data['nm_asesor1'],
        'no_hp_asesor1' => $asesor1_data['no_hp'],
        'nm_kota_asesor1' => $kota1_data['nm_kota'] ?? '-',
        'nia2' => $asesor2_data['nia2'],
        'nm_asesor2' => $asesor2_data['nm_asesor2'],
        'no_hp_asesor2' => $asesor2_data['no_hp'],
        'nm_kota_asesor2' => $kota2_data['nm_kota'] ?? '-'
    ];
    
    // Log successful update
    error_log("Validator/Verifikator updated - ID: $id_mapping, User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => true,
        'message' => 'Validator dan Verifikator berhasil diupdate',
        'data' => $response_data
    ]);
    
} catch (Exception $e) {
    error_log("Update Validator/Verifikator Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan saat mengupdate validator/verifikator'
    ]);
}

if (isset($conn)) {
    $conn->close();
}
?>
