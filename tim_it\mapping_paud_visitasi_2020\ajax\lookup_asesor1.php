<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input NIA
    if (!isset($_POST['nia']) || empty(trim($_POST['nia']))) {
        throw new Exception('NIA harus diisi');
    }
    
    $nia = $conn->real_escape_string(trim($_POST['nia']));
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Query lookup asesor_1 dengan rumpun yang sesuai
    $query = "SELECT a1.id_asesor1, a1.kd_asesor1, a1.nia1, a1.nm_asesor1,
                     a1.ktp, a1.unit_kerja, a1.kota_id1, a1.provinsi_id,
                     a1.no_sertifikat, a1.no_hp, a1.no_wa, a1.tempat_lahir,
                     a1.tgl_lahir, a1.jabatan, a1.jabatan_struktural, a1.pendidikan,
                     a1.jenjang_id, a1.rumpun, a1.grade, a1.jk,
                     a1.alamat_kantor, a1.alamat_rumah, a1.email,
                     a1.thn_terbit_sertifikat, a1.kegiatan, a1.status_keaktifan_id,
                     kk.nm_kota
              FROM asesor_1 a1
              LEFT JOIN kab_kota kk ON a1.kota_id1 = kk.kota_id
              WHERE a1.nia1 = ?
                AND a1.provinsi_id = ?
                AND a1.soft_delete = '1'
                AND a1.status_keaktifan_id = '1'
              LIMIT 1";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("si", $nia, $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $asesor = $result->fetch_assoc();
        
        echo json_encode([
            'success' => true,
            'data' => $asesor,
            'message' => 'Asesor A ditemukan'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'data' => null,
            'message' => 'Asesor dengan NIA tersebut tidak ditemukan atau tidak aktif'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'data' => null,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
