<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$id_mapping = isset($_POST['id_mapping']) ? intval($_POST['id_mapping']) : 0;

if ($id_mapping <= 0) {
    echo json_encode(['success' => false, 'message' => 'ID mapping tidak valid']);
    exit;
}

try {
    // Query untuk mengambil detail mapping KPA dengan join ke tabel terkait
    $query = "SELECT 
                mp.id_mapping,
                mp.sekolah_id,
                mp.kd_asesor,
                mp.tgl_penetapan_kpa,
                mp.tahap,
                mp.tahun_akreditasi,
                mp.file_laporan_hasil_kpa,
                mp.tgl_file_hasil_kpa,
                mp.jam_file_hasil_kpa,
                mp.provinsi_id,
                
                -- Data Sekolah
                s.nama_sekolah,
                s.npsn,
                s.jenjang_id,
                s.rumpun,
                s.alamat,
                s.kota_id,
                s.desa_kelurahan,
                s.kecamatan,
                s.nama_kepsek,
                s.no_hp_kepsek,
                s.no_wa_kepsek,
                s.nama_operator,
                s.no_hp_operator,
                s.no_wa_operator,
                s.email,
                
                -- Data Jenjang
                j.nm_jenjang,
                
                -- Data Kab/Kota Sekolah
                kk_s.nm_kota,
                
                -- Data Asesor
                a.nia,
                a.nm_asesor,
                a.no_hp,
                a.unit_kerja,
                a.rumpun as rumpun_asesor,
                a.kota_id as asesor_kota_id,
                
                -- Data Kab/Kota Asesor
                kk_a.nm_kota as kota_asesor
                
              FROM mapping_paud_kpa mp
              LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
              LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
              LEFT JOIN kab_kota kk_s ON s.kota_id = kk_s.kota_id
              LEFT JOIN asesor a ON mp.kd_asesor = a.kd_asesor
              LEFT JOIN kab_kota kk_a ON a.kota_id = kk_a.kota_id
              WHERE mp.id_mapping = ? AND mp.provinsi_id = ?";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ii", $id_mapping, $_SESSION['provinsi_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Data mapping KPA tidak ditemukan']);
        exit;
    }
    
    $data = $result->fetch_assoc();
    
    echo json_encode([
        'success' => true,
        'data' => $data
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
