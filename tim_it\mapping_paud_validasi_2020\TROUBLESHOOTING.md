# Troubleshooting Modal Detail Mapping Validasi PAUD

## 🐛 Error: JSON.parse: unexpected character at line 1 column 1

### Kemungkinan Penyebab:
1. **PHP Error/Warning** yang muncul sebelum JSON output
2. **HTML/Text output** yang tidak diinginkan
3. **Session/Authentication** issue
4. **Database connection** error
5. **Path/Include** file yang salah

### 🔍 Langkah Debugging:

#### 1. Test File AJAX Langsung
Akses langsung di browser:
```
http://localhost/app-smkpdm/tim_it/mapping_paud_validasi_2020/ajax/test_detail.php
```

#### 2. Test Simple Version
File `get_detail_mapping_simple.php` sudah dibuat untuk testing dengan query yang lebih sederhana.

#### 3. Check Browser Console
Buka Developer Tools → Console dan lihat:
- Raw response dari server
- Error messages yang detail
- Network tab untuk melihat response headers

#### 4. Check Server Error Log
Lihat error log PHP untuk mengetahui error yang terjadi di server side.

### 🛠️ File yang Dibuat untuk Debugging:

1. **`ajax/test_detail.php`** - Test koneksi database dan query dasar
2. **`ajax/get_detail_mapping_simple.php`** - Versi sederhana dari AJAX endpoint
3. **JavaScript dengan enhanced logging** - Menampilkan raw response di console

### 🔧 Perbaikan yang Sudah Dilakukan:

1. **Clean Output Buffer** - `ob_clean()` di awal file PHP
2. **Disable Error Display** - Mencegah PHP error merusak JSON
3. **Enhanced Error Handling** - Try-catch yang lebih comprehensive
4. **Debug Logging** - Console.log untuk melihat response mentah
5. **Simplified Query** - Query yang lebih sederhana untuk testing

### 📝 Langkah Selanjutnya:

1. **Test dengan file simple** terlebih dahulu
2. **Check console log** untuk melihat raw response
3. **Jika simple version bekerja**, upgrade ke query lengkap
4. **Add data kota asesor** setelah basic functionality bekerja

### 🎯 Expected Response Format:
```json
{
    "success": true,
    "data": {
        "id_mapping": 1,
        "npsn": "12345678",
        "nama_sekolah": "TK Example",
        "nm_jenjang": "TK",
        "nm_kota": "Samarinda",
        "nama_kepsek": "John Doe",
        "no_hp_kepsek": "081234567890",
        "no_wa_kepsek": "081234567890",
        "nia1": "123456",
        "nm_asesor1": "Asesor A",
        "no_hp_asesor1": "081234567890",
        "nm_kota_asesor1": "Balikpapan",
        "nia2": "789012",
        "nm_asesor2": "Asesor B",
        "no_hp_asesor2": "081234567890",
        "nm_kota_asesor2": "Kutai Kartanegara",
        "file_penjelasan_hasil_akreditasi": "filename.pdf"
    }
}
```

### 🚨 Common Issues:

1. **Session Timeout** - User perlu login ulang
2. **Wrong Provinsi ID** - Data tidak ditemukan karena filter provinsi
3. **Missing Data** - ID mapping tidak ada di database
4. **Permission Issue** - User bukan Staff IT

### 📞 Support:
Jika masalah masih berlanjut, check:
1. Database connection di `koneksi.php`
2. Session management di `check_session.php`
3. User level dan provinsi_id di session
