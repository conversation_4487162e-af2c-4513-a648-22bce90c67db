<?php
function format_tgl_saja($date){
   /* ARRAY u/ hari dan bulan */
   $Hari = array ("<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>",);
   $Bulan = array ("<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Agus<PERSON>", "September", "Oktober", "November", "Desember");
/* Memisahkan format tanggal bulan dan tahun menggunakan substring */
$tahun     = substr($date, 0, 4);
$bulan     = substr($date, 5, 2);
$tgl    = substr($date, 8, 2);
$waktu    = date ("H:i:s");
$hari    = date("w", strtotime($date));
$result = $tgl." ".$Bulan[(int)$bulan-1]." ".$tahun." ";
return $result;
}
/* by RioBermano.Com */
?>