$(document).ready(function() {
    console.log('Mapping Validasi PAUD module loaded');
    
    // Initialize DataTable
    initDataTable();
    
    // Event handlers untuk action buttons
    $('#btn-input-mapping-validasi').on('click', function() {
        console.log('Input mapping validasi clicked');
        $('#modalInputMapping').modal('show');
        resetFormInputMapping();
    });
    
    $('#btn-export-excel').on('click', function() {
        console.log('Export Excel clicked');
        exportMappingValidasiToExcel();
    });
    
    $('#btn-import-excel').on('click', function() {
        console.log('Import Excel clicked');
        showAlert('info', 'Fitur import Excel akan segera tersedia');
    });
    
    $('#btn-tahun-akreditasi').on('click', function() {
        console.log('Tahun akreditasi clicked');
        filterTahunAkreditasi();
    });
    
    // Event handler untuk tombol detail
    $(document).on('click', '.btn-detail-validasi', function(e) {
        e.preventDefault();
        var idMapping = $(this).data('id');
        console.log('Detail validasi clicked for ID:', idMapping);
        showDetailMappingValidasi(idMapping);
    });
});

function initDataTable() {
    console.log('Initializing DataTable for mapping validasi');
    
    $('#table-mapping-validasi').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: 'ajax/get_mapping_validasi.php',
            type: 'POST',
            error: function(xhr, error, code) {
                console.error('DataTable AJAX error:', error);
                showAlert('error', 'Gagal memuat data mapping validasi');
            }
        },
        columns: [
            { 
                data: null,
                name: 'no',
                orderable: false,
                searchable: false,
                className: 'text-center',
                width: '5%',
                render: function(data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { 
                data: 'npsn',
                name: 'npsn',
                className: 'text-center',
                width: '8%'
            },
            { 
                data: 'nama_sekolah',
                name: 'nama_sekolah',
                width: '20%'
            },
            { 
                data: 'nm_jenjang',
                name: 'nm_jenjang',
                className: 'text-center',
                width: '8%'
            },
            { 
                data: 'nm_kota',
                name: 'nm_kota',
                width: '12%'
            },
            { 
                data: 'nia1',
                name: 'nia1',
                className: 'text-center',
                width: '10%'
            },
            { 
                data: 'nm_asesor1',
                name: 'nm_asesor1',
                width: '15%'
            },
            { 
                data: 'nia2',
                name: 'nia2',
                className: 'text-center',
                width: '10%'
            },
            { 
                data: 'nm_asesor2',
                name: 'nm_asesor2',
                width: '15%'
            },
            { 
                data: 'tahun_akreditasi',
                name: 'tahun_akreditasi',
                className: 'text-center',
                width: '8%'
            },
            { 
                data: 'tahap',
                name: 'tahap',
                className: 'text-center',
                width: '6%'
            },
            { 
                data: null,
                name: 'aksi',
                orderable: false,
                searchable: false,
                className: 'text-center',
                width: '8%',
                render: function(data, type, row) {
                    return '<button type="button" class="btn btn-info btn-sm btn-detail-validasi" ' +
                           'data-id="' + row.id_mapping + '" title="Detail Mapping Validasi">' +
                           '<i class="fas fa-eye"></i>' +
                           '</button>';
                }
            }
        ],
        order: [[2, 'asc']], // Order by nama sekolah
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]],
        language: {
            processing: "Sedang memproses...",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            zeroRecords: "Data tidak ditemukan",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(disaring dari _MAX_ total data)",
            search: "Cari:",
            paginate: {
                first: "Pertama",
                last: "Terakhir",
                next: "Selanjutnya",
                previous: "Sebelumnya"
            }
        },
        responsive: true,
        autoWidth: false,
        scrollX: true,
        dom: '<"row"<"col-sm-6"l><"col-sm-6"f>>' +
             '<"row"<"col-sm-12"tr>>' +
             '<"row"<"col-sm-5"i><"col-sm-7"p>>',
        drawCallback: function(settings) {
            console.log('DataTable draw completed. Rows:', settings.fnRecordsDisplay());
        }
    });
}

function showAlert(type, message) {
    // Simple alert function - bisa diganti dengan library yang lebih sophisticated
    var alertClass = 'alert-info';
    if (type === 'success') alertClass = 'alert-success';
    else if (type === 'error') alertClass = 'alert-danger';
    else if (type === 'warning') alertClass = 'alert-warning';

    var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                    message +
                    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                    '<span aria-hidden="true">&times;</span>' +
                    '</button>' +
                    '</div>';

    // Tampilkan alert di atas card
    $('.card').before(alertHtml);

    // Auto hide after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

// Fungsi untuk reset form input mapping
function resetFormInputMapping() {
    $('#formInputMapping')[0].reset();
    $('#sekolah_id').val('');

    // Reset validation states
    $('#formInputMapping .form-control').removeClass('is-invalid is-valid');
    $('#formInputMapping .invalid-feedback, #formInputMapping .valid-feedback').text('').hide();

    // Reset data attributes
    $('#nia_validator').removeAttr('data-kd-asesor');
    $('#nia_verifikator').removeAttr('data-kd-asesor');

    console.log('Form input mapping direset');
}

// Event handler untuk validasi NPSN real-time
$(document).on('blur', '#npsn_sekolah', function() {
    var npsn = $(this).val().trim();

    if (npsn === '') {
        resetNpsnValidation();
        return;
    }

    validateNpsn(npsn);
});

// Event handler untuk lookup NIA Validator (blur, bukan real-time)
$(document).on('blur', '#nia_validator', function() {
    var nia = $(this).val().trim();

    if (nia === '') {
        resetValidatorValidation();
        return;
    }

    lookupValidator(nia);
});

// Event handler untuk lookup NIA Verifikator (blur, bukan real-time)
$(document).on('blur', '#nia_verifikator', function() {
    var nia = $(this).val().trim();

    if (nia === '') {
        resetVerifikatorValidation();
        return;
    }

    lookupVerifikator(nia);
});

// Event handler untuk form submit tahun akreditasi
$(document).on('submit', '#form-tahun-akreditasi', function(e) {
    e.preventDefault();
    submitTahunAkreditasi();
});

// Event handler untuk tombol edit validator/verifikator di modal detail
$(document).on('click', '#btn-edit-validator-verifikator', function(e) {
    e.preventDefault();
    var idMapping = $(this).attr('data-id-mapping');
    console.log('Edit validator/verifikator clicked for ID:', idMapping);
    showEditValidatorVerifikatorModal(idMapping);
});

// Event handler untuk tombol hapus di modal detail
$(document).on('click', '#btn-hapus-mapping-validasi', function(e) {
    e.preventDefault();
    var idMapping = $(this).attr('data-id-mapping');
    var namaSekolah = $(this).attr('data-nama-sekolah');
    var npsn = $(this).attr('data-npsn');
    console.log('Hapus mapping validasi clicked for ID:', idMapping);
    showAlert('info', 'Fitur hapus mapping validasi akan segera tersedia');
});

// Event handler untuk form edit validator/verifikator
$(document).on('submit', '#form-edit-validator-verifikator', function(e) {
    e.preventDefault();
    submitEditValidatorVerifikator();
});

// Event handler untuk validasi real-time NIA Validator di form edit
$(document).on('blur', '#edit-nia-validator', function() {
    var nia = $(this).val().trim();
    if (nia) {
        validateEditAsesor(nia, 'validator');
    }
});

// Event handler untuk validasi real-time NIA Verifikator di form edit
$(document).on('blur', '#edit-nia-verifikator', function() {
    var nia = $(this).val().trim();
    if (nia) {
        validateEditAsesor(nia, 'verifikator');
    }
});

// Fungsi untuk validasi NPSN
function validateNpsn(npsn) {
    $.ajax({
        url: 'ajax/validate_npsn.php',
        type: 'POST',
        data: { npsn: npsn },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // NPSN valid dan sekolah PAUD
                $('#npsn_sekolah').removeClass('is-invalid').addClass('is-valid');
                $('#npsn_feedback').removeClass('invalid-feedback').addClass('valid-feedback')
                    .text('✓ ' + response.data.nama_sekolah + ' (' + response.data.jenjang + ', ' + response.data.kota + ')').show();
                $('#sekolah_id').val(response.data.sekolah_id);

                console.log('NPSN valid:', response.data);
            } else {
                // NPSN tidak valid
                $('#npsn_sekolah').removeClass('is-valid').addClass('is-invalid');
                $('#npsn_feedback').removeClass('valid-feedback').addClass('invalid-feedback')
                    .text(response.message).show();
                $('#sekolah_id').val('');

                console.log('NPSN tidak valid:', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error validasi NPSN:', error);
            $('#npsn_sekolah').removeClass('is-valid').addClass('is-invalid');
            $('#npsn_feedback').removeClass('valid-feedback').addClass('invalid-feedback')
                .text('Terjadi kesalahan saat validasi NPSN').show();
            $('#sekolah_id').val('');
        }
    });
}

// Fungsi untuk reset validasi NPSN
function resetNpsnValidation() {
    $('#npsn_sekolah').removeClass('is-invalid is-valid');
    $('#npsn_feedback').text('').hide();
    $('#sekolah_id').val('');
}

// Fungsi untuk lookup Validator (asesor_1)
function lookupValidator(nia) {
    $.ajax({
        url: '../mapping_paud_visitasi_2020/ajax/lookup_asesor1.php',
        type: 'POST',
        data: { nia: nia },
        dataType: 'json',
        success: function(response) {
            if (response.success && response.data) {
                // NIA Validator valid
                $('#nia_validator').removeClass('is-invalid').addClass('is-valid');
                $('#validator_feedback').removeClass('invalid-feedback').addClass('valid-feedback')
                    .text('✓ ' + response.data.nm_asesor1 + ' (' + response.data.nm_kota + ')').show();

                // Set hidden field dengan kd_asesor1 untuk database
                $('#nia_validator').attr('data-kd-asesor', response.data.kd_asesor1);

                console.log('Validator valid:', response.data);
            } else {
                // NIA Validator tidak valid
                $('#nia_validator').removeClass('is-valid').addClass('is-invalid');
                $('#validator_feedback').removeClass('valid-feedback').addClass('invalid-feedback')
                    .text(response.message || 'NIA Validator tidak ditemukan atau tidak aktif').show();
                $('#nia_validator').removeAttr('data-kd-asesor');

                console.log('Validator tidak valid:', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error lookup validator:', error);
            $('#nia_validator').removeClass('is-valid').addClass('is-invalid');
            $('#validator_feedback').removeClass('valid-feedback').addClass('invalid-feedback')
                .text('Terjadi kesalahan saat validasi NIA Validator').show();
            $('#nia_validator').removeAttr('data-kd-asesor');
        }
    });
}

// Fungsi untuk lookup Verifikator (asesor_2)
function lookupVerifikator(nia) {
    $.ajax({
        url: '../mapping_paud_visitasi_2020/ajax/lookup_asesor2.php',
        type: 'POST',
        data: { nia: nia },
        dataType: 'json',
        success: function(response) {
            if (response.success && response.data) {
                // NIA Verifikator valid
                $('#nia_verifikator').removeClass('is-invalid').addClass('is-valid');
                $('#verifikator_feedback').removeClass('invalid-feedback').addClass('valid-feedback')
                    .text('✓ ' + response.data.nm_asesor2 + ' (' + response.data.nm_kota + ')').show();

                // Set hidden field dengan kd_asesor2 untuk database
                $('#nia_verifikator').attr('data-kd-asesor', response.data.kd_asesor2);

                console.log('Verifikator valid:', response.data);
            } else {
                // NIA Verifikator tidak valid
                $('#nia_verifikator').removeClass('is-valid').addClass('is-invalid');
                $('#verifikator_feedback').removeClass('valid-feedback').addClass('invalid-feedback')
                    .text(response.message || 'NIA Verifikator tidak ditemukan atau tidak aktif').show();
                $('#nia_verifikator').removeAttr('data-kd-asesor');

                console.log('Verifikator tidak valid:', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error lookup verifikator:', error);
            $('#nia_verifikator').removeClass('is-valid').addClass('is-invalid');
            $('#verifikator_feedback').removeClass('valid-feedback').addClass('invalid-feedback')
                .text('Terjadi kesalahan saat validasi NIA Verifikator').show();
            $('#nia_verifikator').removeAttr('data-kd-asesor');
        }
    });
}

// Fungsi untuk reset validasi Validator
function resetValidatorValidation() {
    $('#nia_validator').removeClass('is-invalid is-valid');
    $('#validator_feedback').text('').hide();
    $('#nia_validator').removeAttr('data-kd-asesor');
}

// Fungsi untuk reset validasi Verifikator
function resetVerifikatorValidation() {
    $('#nia_verifikator').removeClass('is-invalid is-valid');
    $('#verifikator_feedback').text('').hide();
    $('#nia_verifikator').removeAttr('data-kd-asesor');
}

// Event handler untuk form submit
$(document).on('submit', '#formInputMapping', function(e) {
    e.preventDefault();

    // Validasi form sebelum submit
    if (!validateFormInputMapping()) {
        return false;
    }

    // Prepare data dengan kd_asesor yang benar
    var formData = {
        sekolah_id: $('#sekolah_id').val(),
        nia_validator: $('#nia_validator').attr('data-kd-asesor'), // Kirim kd_asesor1
        nia_verifikator: $('#nia_verifikator').attr('data-kd-asesor'), // Kirim kd_asesor2
        tahun_akreditasi: $('#tahun_akreditasi').val(),
        tahap: $('#tahap').val()
    };

    // Disable tombol submit
    $('#btnSimpanMapping').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    $.ajax({
        url: 'ajax/simpan_mapping.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Tutup modal dan refresh tabel
                $('#modalInputMapping').modal('hide');
                $('#table-mapping-validasi').DataTable().ajax.reload(null, false);

                console.log('Data mapping berhasil disimpan');
            } else {
                showAlert('error', response.message);
                console.log('Gagal simpan mapping:', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error simpan mapping:', error);
            showAlert('error', 'Terjadi kesalahan saat menyimpan data mapping');
        },
        complete: function() {
            // Enable kembali tombol submit
            $('#btnSimpanMapping').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Mapping');
        }
    });
});

// Fungsi validasi form input mapping
function validateFormInputMapping() {
    var isValid = true;

    // Validasi NPSN dan sekolah_id
    var sekolah_id = $('#sekolah_id').val();
    if (!sekolah_id) {
        $('#npsn_sekolah').addClass('is-invalid');
        $('#npsn_feedback').removeClass('valid-feedback').addClass('invalid-feedback')
            .text('NPSN harus diisi dan valid').show();
        isValid = false;
    }

    // Validasi NIA Validator - harus ada kd_asesor
    var nia_validator = $('#nia_validator').val().trim();
    var kd_validator = $('#nia_validator').attr('data-kd-asesor');
    if (!nia_validator) {
        $('#nia_validator').addClass('is-invalid');
        $('#validator_feedback').removeClass('valid-feedback').addClass('invalid-feedback')
            .text('NIA Validator harus diisi').show();
        isValid = false;
    } else if (!kd_validator) {
        $('#nia_validator').addClass('is-invalid');
        $('#validator_feedback').removeClass('valid-feedback').addClass('invalid-feedback')
            .text('NIA Validator tidak valid atau belum diverifikasi').show();
        isValid = false;
    }

    // Validasi NIA Verifikator - harus ada kd_asesor
    var nia_verifikator = $('#nia_verifikator').val().trim();
    var kd_verifikator = $('#nia_verifikator').attr('data-kd-asesor');
    if (!nia_verifikator) {
        $('#nia_verifikator').addClass('is-invalid');
        $('#verifikator_feedback').removeClass('valid-feedback').addClass('invalid-feedback')
            .text('NIA Verifikator harus diisi').show();
        isValid = false;
    } else if (!kd_verifikator) {
        $('#nia_verifikator').addClass('is-invalid');
        $('#verifikator_feedback').removeClass('valid-feedback').addClass('invalid-feedback')
            .text('NIA Verifikator tidak valid atau belum diverifikasi').show();
        isValid = false;
    }

    // Validasi asesor tidak boleh sama
    if (kd_validator && kd_verifikator && kd_validator === kd_verifikator) {
        $('#nia_verifikator').addClass('is-invalid');
        $('#verifikator_feedback').removeClass('valid-feedback').addClass('invalid-feedback')
            .text('Validator dan Verifikator tidak boleh sama').show();
        isValid = false;
    }

    // Validasi Tahun Akreditasi
    var tahun = $('#tahun_akreditasi').val().trim();
    if (!tahun) {
        $('#tahun_akreditasi').addClass('is-invalid');
        $('#tahun_feedback').text('Tahun Akreditasi harus diisi').show();
        isValid = false;
    } else if (!/^\d{4}$/.test(tahun)) {
        $('#tahun_akreditasi').addClass('is-invalid');
        $('#tahun_feedback').text('Tahun harus berformat 4 digit angka').show();
        isValid = false;
    } else {
        $('#tahun_akreditasi').removeClass('is-invalid');
        $('#tahun_feedback').hide();
    }

    // Validasi Tahap
    var tahap = $('#tahap').val().trim();
    if (!tahap) {
        $('#tahap').addClass('is-invalid');
        $('#tahap_feedback').text('Tahap Ke harus diisi').show();
        isValid = false;
    } else if (isNaN(tahap) || parseInt(tahap) < 1) {
        $('#tahap').addClass('is-invalid');
        $('#tahap_feedback').text('Tahap harus berupa angka positif').show();
        isValid = false;
    } else {
        $('#tahap').removeClass('is-invalid');
        $('#tahap_feedback').hide();
    }

    return isValid;
}

// Fungsi untuk filter tahun akreditasi
function filterTahunAkreditasi() {
    console.log('Filter tahun akreditasi clicked');

    // Load current tahun akreditasi
    loadCurrentTahunAkreditasi();

    // Show modal
    $('#modal-tahun-akreditasi').modal('show');
}

// Fungsi untuk load tahun akreditasi saat ini
function loadCurrentTahunAkreditasi() {
    console.log('Loading current tahun akreditasi validasi');

    $.ajax({
        url: 'ajax/get_tahun_akreditasi.php',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.success && response.data) {
                // Set current year in form
                $('#nama_tahun').val(response.data.nama_tahun);

                // Show current year info
                $('#current-year-display').text(response.data.nama_tahun);
                $('#info-current-year').show();

                console.log('Current tahun akreditasi validasi loaded:', response.data.nama_tahun);
            } else {
                // No current year, set default
                $('#nama_tahun').val('');
                $('#info-current-year').hide();
                console.log('No current tahun akreditasi validasi found');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading current tahun akreditasi:', error);
            $('#nama_tahun').val('');
            $('#info-current-year').hide();
        }
    });
}

// Fungsi untuk submit tahun akreditasi
function submitTahunAkreditasi() {
    console.log('Submitting tahun akreditasi validasi');

    var namaTahun = $('#nama_tahun').val().trim();

    if (!namaTahun) {
        showAlert('error', 'Tahun Akreditasi harus diisi');
        return;
    }

    // Validasi format tahun
    if (!/^\d{4}$/.test(namaTahun)) {
        showAlert('error', 'Tahun harus berformat 4 digit angka');
        return;
    }

    var formData = new FormData($('#form-tahun-akreditasi')[0]);

    $.ajax({
        url: 'ajax/update_tahun_akreditasi.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        beforeSend: function() {
            // Disable submit button
            $('#form-tahun-akreditasi button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
        },
        success: function(response) {
            if (response.success) {
                // Close modal
                $('#modal-tahun-akreditasi').modal('hide');

                // Reload DataTable tanpa refresh browser untuk reflect perubahan filter
                $('#table-mapping-validasi').DataTable().ajax.reload(null, false);

                // Silent success - no notification sesuai requirement
                console.log('Tahun akreditasi validasi berhasil diupdate:', response.data);
                console.log('Tabel mapping validasi telah di-reload dengan filter tahun baru');
            } else {
                showAlert('error', response.message || 'Gagal mengupdate tahun akreditasi');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error submitting tahun akreditasi:', error);
            showAlert('error', 'Terjadi kesalahan saat mengupdate tahun akreditasi');
        },
        complete: function() {
            // Re-enable submit button
            $('#form-tahun-akreditasi button[type="submit"]').prop('disabled', false).html('<i class="fas fa-save"></i> Update Filter');
        }
    });
}

// Fungsi untuk menampilkan detail mapping validasi
function showDetailMappingValidasi(idMapping) {
    console.log('Loading detail mapping validasi for ID:', idMapping);

    // Show modal dengan loading state
    $('#modal-detail-mapping-validasi').modal('show');

    // Reset semua field ke loading state
    resetDetailFields();

    // AJAX request untuk get detail
    $.ajax({
        url: 'ajax/get_detail_mapping.php',
        type: 'POST',
        data: { id_mapping: idMapping },
        success: function(response, textStatus, xhr) {
            console.log('Raw response:', response);
            console.log('Response type:', typeof response);

            try {
                // Jika response sudah object, gunakan langsung
                var data = (typeof response === 'string') ? JSON.parse(response) : response;

                if (data.success && data.data) {
                    populateDetailFields(data.data);
                    console.log('Detail mapping validasi loaded successfully');
                } else {
                    showAlert('error', data.message || 'Gagal memuat detail mapping validasi');
                    $('#modal-detail-mapping-validasi').modal('hide');
                }
            } catch (parseError) {
                console.error('JSON Parse Error:', parseError);
                console.error('Raw response that failed to parse:', response);
                showAlert('error', 'Response tidak valid dari server');
                $('#modal-detail-mapping-validasi').modal('hide');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', error);
            console.error('Status:', status);
            console.error('Response Text:', xhr.responseText);
            showAlert('error', 'Terjadi kesalahan saat memuat detail mapping validasi');
            $('#modal-detail-mapping-validasi').modal('hide');
        }
    });
}

// Fungsi untuk reset field detail ke loading state
function resetDetailFields() {
    // Data Sekolah
    $('#detail-validasi-npsn').text('Memuat...');
    $('#detail-validasi-nama-sekolah').text('Memuat...');
    $('#detail-validasi-jenjang').text('Memuat...');
    $('#detail-validasi-kab-kota').text('Memuat...');
    $('#detail-validasi-nama-kepsek').text('Memuat...');
    $('#detail-validasi-hp-kepsek').text('Memuat...');
    $('#detail-validasi-wa-kepsek').text('Memuat...');

    // Data Validator dan Verifikator
    $('#detail-validasi-nia-validator').text('Memuat...');
    $('#detail-validasi-nama-validator').text('Memuat...');
    $('#detail-validasi-hp-validator').text('Memuat...');
    $('#detail-validasi-kota-validator').text('Memuat...');
    $('#detail-validasi-nia-verifikator').text('Memuat...');
    $('#detail-validasi-nama-verifikator').text('Memuat...');
    $('#detail-validasi-hp-verifikator').text('Memuat...');
    $('#detail-validasi-kota-verifikator').text('Memuat...');

    // Dokumen Unggahan
    $('#detail-validasi-file-penjelasan').html('Memuat...');
}

// Fungsi untuk populate field detail dengan data
function populateDetailFields(data) {
    console.log('Populating fields with data:', data);

    // Data Sekolah
    $('#detail-validasi-npsn').text(data.npsn || '-');
    $('#detail-validasi-nama-sekolah').text(data.nama_sekolah || '-');
    $('#detail-validasi-jenjang').text(data.nm_jenjang || '-');
    $('#detail-validasi-kab-kota').text(data.nm_kota || '-');
    $('#detail-validasi-nama-kepsek').text(data.nama_kepsek || '-');
    $('#detail-validasi-hp-kepsek').text(data.no_hp_kepsek || '-');
    $('#detail-validasi-wa-kepsek').text(data.no_wa_kepsek || '-');

    // Data Validator (asesor_1)
    $('#detail-validasi-nia-validator').text(data.nia1 || '-');
    $('#detail-validasi-nama-validator').text(data.nm_asesor1 || '-');
    $('#detail-validasi-hp-validator').text(data.no_hp_asesor1 || '-');
    $('#detail-validasi-kota-validator').text(data.nm_kota_asesor1 || '-');

    // Data Verifikator (asesor_2)
    $('#detail-validasi-nia-verifikator').text(data.nia2 || '-');
    $('#detail-validasi-nama-verifikator').text(data.nm_asesor2 || '-');
    $('#detail-validasi-hp-verifikator').text(data.no_hp_asesor2 || '-');
    $('#detail-validasi-kota-verifikator').text(data.nm_kota_asesor2 || '-');

    // Dokumen Unggahan - File Penjelasan Hasil Akreditasi
    var fileStatus = '';
    if (data.file_penjelasan_hasil_akreditasi && data.file_penjelasan_hasil_akreditasi.trim() !== '') {
        var filename = data.file_penjelasan_hasil_akreditasi.trim();
        fileStatus = '<span class="badge badge-success clickable-file" style="cursor: pointer;" ' +
                    'onclick="openValidasiFile(\'' + filename + '\')" title="Klik untuk membuka file">' +
                    'Sudah Upload</span>';
    } else {
        fileStatus = '<span class="badge badge-danger">Belum Upload</span>';
    }
    $('#detail-validasi-file-penjelasan').html(fileStatus);

    // Set data attributes untuk tombol aksi
    $('#btn-edit-validator-verifikator').attr('data-id-mapping', data.id_mapping);
    $('#btn-hapus-mapping-validasi').attr('data-id-mapping', data.id_mapping);
    $('#btn-hapus-mapping-validasi').attr('data-nama-sekolah', data.nama_sekolah);
    $('#btn-hapus-mapping-validasi').attr('data-npsn', data.npsn);
}

// Fungsi untuk membuka file validasi PAUD
function openValidasiFile(filename) {
    console.log('Opening validasi file:', filename);

    if (!filename || filename.trim() === '') {
        showAlert('error', 'Nama file tidak valid');
        return;
    }

    // Path relatif dari lokasi modal ke direktori file
    var filePath = '../../../simak/files/upload_file_hasil_Validasi_paud/' + filename;

    // Buka file di tab baru
    var newWindow = window.open(filePath, '_blank');

    // Check jika window berhasil dibuka (popup blocker detection)
    if (!newWindow || newWindow.closed || typeof newWindow.closed == 'undefined') {
        showAlert('warning', 'Popup diblokir oleh browser. Silakan izinkan popup untuk membuka file.');
        return;
    }

    // Error handling untuk file tidak ditemukan
    newWindow.onerror = function() {
        showAlert('error', 'File tidak ditemukan atau tidak dapat dibuka');
    };

    // Alternatif error handling dengan timeout
    setTimeout(function() {
        try {
            if (newWindow.document.title === '' || newWindow.document.title.indexOf('404') !== -1) {
                showAlert('error', 'File tidak ditemukan di server');
            }
        } catch (e) {
            // Cross-origin error, file mungkin ada tapi tidak bisa diakses title
            console.log('File opened, cross-origin access restricted');
        }
    }, 1000);

    // Log untuk audit
    console.log('File validasi PAUD opened:', filePath);
}

// Fungsi untuk menampilkan modal edit validator/verifikator
function showEditValidatorVerifikatorModal(idMapping) {
    console.log('Opening edit modal for mapping ID:', idMapping);

    // Set ID mapping ke hidden field
    $('#edit-id-mapping').val(idMapping);

    // Reset form
    resetEditForm();

    // Get current data untuk pre-fill form
    getCurrentMappingData(idMapping);

    // Show modal edit
    $('#modal-edit-validator-verifikator').modal('show');
}

// Fungsi untuk reset form edit
function resetEditForm() {
    $('#form-edit-validator-verifikator')[0].reset();

    // Reset validation states
    $('#edit-nia-validator').removeClass('is-valid is-invalid');
    $('#edit-nia-verifikator').removeClass('is-valid is-invalid');

    // Clear feedback messages
    $('#edit-validator-feedback').text('').hide();
    $('#edit-verifikator-feedback').text('').hide();
    $('#edit-validator-info').text('');
    $('#edit-verifikator-info').text('');
}

// Fungsi untuk get current mapping data dan pre-fill form
function getCurrentMappingData(idMapping) {
    // Get data dari modal detail yang sedang terbuka
    var currentValidatorNia = $('#detail-validasi-nia-validator').text();
    var currentVerifikatorNia = $('#detail-validasi-nia-verifikator').text();

    // Pre-fill form dengan data current
    if (currentValidatorNia && currentValidatorNia !== '-') {
        $('#edit-nia-validator').val(currentValidatorNia);
    }

    if (currentVerifikatorNia && currentVerifikatorNia !== '-') {
        $('#edit-nia-verifikator').val(currentVerifikatorNia);
    }
}

// Fungsi untuk validasi asesor di form edit
function validateEditAsesor(nia, type) {
    console.log('Validating edit asesor:', nia, type);

    var inputField = '#edit-nia-' + type;
    var feedbackField = '#edit-' + type + '-feedback';
    var infoField = '#edit-' + type + '-info';

    // Reset state
    $(inputField).removeClass('is-valid is-invalid');
    $(feedbackField).text('').hide();
    $(infoField).text('Memvalidasi...');

    // Determine asesor table
    var asesorTable = (type === 'validator') ? 'asesor_1' : 'asesor_2';
    var niaField = (type === 'validator') ? 'nia1' : 'nia2';
    var namaField = (type === 'validator') ? 'nm_asesor1' : 'nm_asesor2';

    $.ajax({
        url: 'ajax/validate_asesor.php',
        type: 'POST',
        data: {
            nia: nia,
            asesor_table: asesorTable,
            nia_field: niaField,
            nama_field: namaField
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                $(inputField).addClass('is-valid').removeClass('is-invalid');
                $(infoField).text(response.data.nama + ' - ' + response.data.kota);
                $(feedbackField).hide();
            } else {
                $(inputField).addClass('is-invalid').removeClass('is-valid');
                $(feedbackField).addClass('invalid-feedback').removeClass('valid-feedback')
                    .text(response.message).show();
                $(infoField).text('');
            }
        },
        error: function() {
            $(inputField).addClass('is-invalid').removeClass('is-valid');
            $(feedbackField).addClass('invalid-feedback').removeClass('valid-feedback')
                .text('Terjadi kesalahan saat validasi').show();
            $(infoField).text('');
        }
    });
}

// Fungsi untuk submit edit validator/verifikator
function submitEditValidatorVerifikator() {
    console.log('Submitting edit validator/verifikator');

    var idMapping = $('#edit-id-mapping').val();
    var niaValidator = $('#edit-nia-validator').val().trim();
    var niaVerifikator = $('#edit-nia-verifikator').val().trim();

    // Validasi form
    var isValid = true;

    if (!niaValidator) {
        $('#edit-nia-validator').addClass('is-invalid');
        $('#edit-validator-feedback').addClass('invalid-feedback').text('NIA Validator wajib diisi').show();
        isValid = false;
    }

    if (!niaVerifikator) {
        $('#edit-nia-verifikator').addClass('is-invalid');
        $('#edit-verifikator-feedback').addClass('invalid-feedback').text('NIA Verifikator wajib diisi').show();
        isValid = false;
    }

    // Validasi asesor tidak boleh sama
    if (niaValidator && niaVerifikator && niaValidator === niaVerifikator) {
        $('#edit-nia-verifikator').addClass('is-invalid');
        $('#edit-verifikator-feedback').addClass('invalid-feedback').text('Validator dan Verifikator tidak boleh sama').show();
        isValid = false;
    }

    // Validasi bahwa kedua asesor sudah tervalidasi (memiliki class is-valid)
    if (!$('#edit-nia-validator').hasClass('is-valid')) {
        $('#edit-nia-validator').addClass('is-invalid');
        $('#edit-validator-feedback').addClass('invalid-feedback').text('NIA Validator belum valid atau belum divalidasi').show();
        isValid = false;
    }

    if (!$('#edit-nia-verifikator').hasClass('is-valid')) {
        $('#edit-nia-verifikator').addClass('is-invalid');
        $('#edit-verifikator-feedback').addClass('invalid-feedback').text('NIA Verifikator belum valid atau belum divalidasi').show();
        isValid = false;
    }

    if (!isValid) {
        return;
    }

    // Disable button saat submit
    $('#btn-update-asesor').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Updating...');

    $.ajax({
        url: 'ajax/update_validator_verifikator.php',
        type: 'POST',
        data: {
            id_mapping: idMapping,
            kd_asesor1: niaValidator,  // Kirim NIA, akan di-lookup ke kd_asesor di backend
            kd_asesor2: niaVerifikator // Kirim NIA, akan di-lookup ke kd_asesor di backend
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Update modal detail dengan data baru
                updateModalDetailData(response.data);

                // Update tabel utama
                updateTableRow(idMapping, response.data);

                // Tutup modal edit
                $('#modal-edit-validator-verifikator').modal('hide');

                console.log('Validator/Verifikator updated successfully');
            } else {
                showAlert('error', response.message || 'Gagal mengupdate validator/verifikator');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error updating validator/verifikator:', error);
            showAlert('error', 'Terjadi kesalahan saat mengupdate validator/verifikator');
        },
        complete: function() {
            // Re-enable button
            $('#btn-update-asesor').prop('disabled', false).html('<i class="fas fa-save"></i> Update Perubahan');
        }
    });
}

// Fungsi untuk update data di modal detail
function updateModalDetailData(data) {
    console.log('Updating modal detail with new data:', data);

    // Update data validator
    $('#detail-validasi-nia-validator').text(data.nia1 || '-');
    $('#detail-validasi-nama-validator').text(data.nm_asesor1 || '-');
    $('#detail-validasi-hp-validator').text(data.no_hp_asesor1 || '-');
    $('#detail-validasi-kota-validator').text(data.nm_kota_asesor1 || '-');

    // Update data verifikator
    $('#detail-validasi-nia-verifikator').text(data.nia2 || '-');
    $('#detail-validasi-nama-verifikator').text(data.nm_asesor2 || '-');
    $('#detail-validasi-hp-verifikator').text(data.no_hp_asesor2 || '-');
    $('#detail-validasi-kota-verifikator').text(data.nm_kota_asesor2 || '-');
}

// Fungsi untuk update row di tabel utama
function updateTableRow(idMapping, data) {
    console.log('Updating table row for ID:', idMapping, 'with data:', data);

    // Refresh DataTable untuk update data terbaru
    if ($.fn.DataTable.isDataTable('#table-mapping-validasi')) {
        $('#table-mapping-validasi').DataTable().ajax.reload(null, false);
        console.log('DataTable refreshed after validator/verifikator update');
    }
}

// Fungsi untuk export data mapping validasi ke Excel
function exportMappingValidasiToExcel() {
    // Show loading
    showAlert('info', 'Sedang memproses export Excel...');

    // Get all data from server untuk export
    $.ajax({
        url: 'ajax/get_export_mapping_validasi.php',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.success) {

                // Prepare data for Excel
                var excelData = [];

                // Add header row
                excelData.push([
                    'NO',
                    'NPSN',
                    'NAMA SEKOLAH',
                    'JENJANG',
                    'KABUPATEN / KOTA',
                    'NIA VALIDATOR',
                    'NAMA VALIDATOR',
                    'KABUPATEN KOTA',
                    'NIA VERIFIKATOR',
                    'NAMA VERIFIKATOR',
                    'KABUPATEN KOTA',
                    'TAHUN AKREDITASI',
                    'TAHAP',
                    'FILE PHA VALIDASI',
                    'NAMA ASESOR KPA',
                    'CATATAN PENILAIAN VALIDATOR TERHADAP ASESOR KPA',
                    'NAMA ASESOR VISITASI A',
                    'CATATAN PENILAIAN VALIDATOR TERHADAP ASESOR VISITASI A',
                    'NAMA ASESOR VISITASI B',
                    'CATATAN PENILAIAN VALIDATOR TERHADAP ASESOR VISITASI B',
                    'NAMA VALIDATOR',
                    'NILAI VALIDASI',
                    'CATATAN PENILAIAN VERIFIKATOR TERHADAP VALIDATOR',
                    'PHA'
                ]);

                // Add data rows
                response.data.forEach(function(row, index) {
                    excelData.push([
                        index + 1,
                        row.npsn || '-',
                        row.nama_sekolah || '-',
                        row.nm_jenjang || '-',
                        row.nm_kota_sekolah || '-',
                        row.nia1 || '-',
                        row.nm_asesor1 || '-',
                        row.nm_kota_asesor1 || '-',
                        row.nia2 || '-',
                        row.nm_asesor2 || '-',
                        row.nm_kota_asesor2 || '-',
                        row.tahun_akreditasi || '-',
                        row.tahap || '-',
                        row.file_status || '-',
                        row.nama_asesor_kpa || '-',
                        row.catatan_penilaian_asesor_kpa || '-',
                        row.nama_asesor_Validasi_a || '-',
                        row.catatan_penilaian_asesor_Validasi_a || '-',
                        row.nama_asesor_Validasi_b || '-',
                        row.catatan_penilaian_asesor_Validasi_b || '-',
                        row.nama_validator || '-',
                        row.nilai_validasi || '-',
                        row.catatan_penilaian_validator || '-',
                        row.pha || '-'
                    ]);
                });

                // Create workbook and worksheet
                var wb = XLSX.utils.book_new();
                var ws = XLSX.utils.aoa_to_sheet(excelData);

                // Set column widths
                ws['!cols'] = [
                    {wch: 5},   // NO
                    {wch: 12},  // NPSN
                    {wch: 30},  // NAMA SEKOLAH
                    {wch: 10},  // JENJANG
                    {wch: 15},  // KABUPATEN / KOTA
                    {wch: 12},  // NIA VALIDATOR
                    {wch: 25},  // NAMA VALIDATOR
                    {wch: 15},  // KABUPATEN KOTA
                    {wch: 12},  // NIA VERIFIKATOR
                    {wch: 25},  // NAMA VERIFIKATOR
                    {wch: 15},  // KABUPATEN KOTA
                    {wch: 10},  // TAHUN AKREDITASI
                    {wch: 8},   // TAHAP
                    {wch: 15},  // FILE PHA VALIDASI
                    {wch: 20},  // NAMA ASESOR KPA
                    {wch: 35},  // CATATAN PENILAIAN VALIDATOR TERHADAP ASESOR KPA
                    {wch: 20},  // NAMA ASESOR VISITASI A
                    {wch: 35},  // CATATAN PENILAIAN VALIDATOR TERHADAP ASESOR VISITASI A
                    {wch: 20},  // NAMA ASESOR VISITASI B
                    {wch: 35},  // CATATAN PENILAIAN VALIDATOR TERHADAP ASESOR VISITASI B
                    {wch: 20},  // NAMA VALIDATOR
                    {wch: 15},  // NILAI VALIDASI
                    {wch: 35},  // CATATAN PENILAIAN VERIFIKATOR TERHADAP VALIDATOR
                    {wch: 25}   // PHA
                ];

                // Add worksheet to workbook
                XLSX.utils.book_append_sheet(wb, ws, "Mapping Validasi PAUD");

                // Generate filename with current year
                var today = new Date();
                var year = today.getFullYear();
                var filename = 'Export_Mapping_Validasi_PAUD_' + year + '.xlsx';

                // Save file
                XLSX.writeFile(wb, filename);

                // Show success message
                showAlert('success', 'File Excel berhasil didownload: ' + filename);
                console.log('Excel export completed:', filename);

            } else {
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat export data');
        }
    });
}
