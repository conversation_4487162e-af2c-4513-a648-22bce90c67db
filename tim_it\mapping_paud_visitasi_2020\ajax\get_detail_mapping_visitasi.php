<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input ID mapping
    if (!isset($_POST['id_mapping']) || empty(trim($_POST['id_mapping']))) {
        throw new Exception('ID mapping harus diisi');
    }
    
    $id_mapping = intval($_POST['id_mapping']);
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Validasi ID mapping
    if ($id_mapping <= 0) {
        throw new Exception('ID mapping tidak valid');
    }
    
    // Query detail mapping visitasi dengan semua relasi
    $query = "SELECT 
                  mp.id_mapping,
                  mp.sekolah_id,
                  mp.kd_asesor1,
                  mp.kd_asesor2,
                  mp.tgl_mulai_visitasi,
                  mp.tgl_akhir_visitasi,
                  mp.tahap,
                  mp.no_surat,
                  mp.tgl_surat,
                  mp.tahun_akreditasi,
                  mp.file_pakta_integritas_1,
                  mp.file_pakta_integritas_2,
                  mp.file_berita_acara_visitasi,
                  mp.file_temuan_hasil_visitasi,
                  mp.file_absen_pembuka,
                  mp.file_absen_penutup,
                  mp.file_foto_visitasi,
                  mp.tgl_file_foto_visitasi,
                  mp.jam_file_foto_visitasi,
                  mp.file_laporan_individu_1,
                  mp.file_laporan_individu_2,
                  mp.file_laporan_kelompok,
                  mp.file_penjelasan_hasil_akreditasi,
                  mp.provinsi_id,
                  
                  -- Data Sekolah
                  s.nama_sekolah,
                  s.npsn,
                  s.jenjang_id,
                  s.rumpun,
                  s.alamat,
                  s.kota_id,
                  s.desa_kelurahan,
                  s.kecamatan,
                  s.nama_kepsek,
                  s.no_hp_kepsek,
                  s.no_wa_kepsek,
                  s.nama_operator,
                  s.no_hp_operator,
                  s.no_wa_operator,
                  s.email,
                  
                  -- Data Jenjang
                  j.nm_jenjang,
                  
                  -- Data Kab/Kota Sekolah
                  kk_s.nm_kota as nm_kota_sekolah,
                  
                  -- Data Asesor 1
                  a1.nia1,
                  a1.nm_asesor1,
                  a1.no_hp as no_hp_asesor1,
                  a1.unit_kerja as unit_kerja_asesor1,
                  a1.kota_id1,
                  a1.rumpun as rumpun_asesor1,
                  a1.grade as grade_asesor1,
                  a1.jabatan as jabatan_asesor1,
                  a1.pendidikan as pendidikan_asesor1,
                  
                  -- Data Kab/Kota Asesor 1
                  kk_a1.nm_kota as nm_kota_asesor1,
                  
                  -- Data Asesor 2
                  a2.nia2,
                  a2.nm_asesor2,
                  a2.no_hp as no_hp_asesor2,
                  a2.unit_kerja as unit_kerja_asesor2,
                  a2.kota_id2,
                  a2.rumpun as rumpun_asesor2,
                  a2.grade as grade_asesor2,
                  a2.jabatan as jabatan_asesor2,
                  a2.pendidikan as pendidikan_asesor2,
                  
                  -- Data Kab/Kota Asesor 2
                  kk_a2.nm_kota as nm_kota_asesor2
                  
              FROM mapping_paud_visitasi mp
              LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
              LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
              LEFT JOIN kab_kota kk_s ON s.kota_id = kk_s.kota_id
              LEFT JOIN asesor_1 a1 ON mp.kd_asesor1 = a1.kd_asesor1
              LEFT JOIN kab_kota kk_a1 ON a1.kota_id1 = kk_a1.kota_id
              LEFT JOIN asesor_2 a2 ON mp.kd_asesor2 = a2.kd_asesor2
              LEFT JOIN kab_kota kk_a2 ON a2.kota_id2 = kk_a2.kota_id
              WHERE mp.id_mapping = ? AND mp.provinsi_id = ?
              LIMIT 1";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ii", $id_mapping, $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception('Data mapping visitasi tidak ditemukan atau Anda tidak memiliki akses');
    }
    
    $mapping_data = $result->fetch_assoc();
    
    // Log successful access
    error_log("Detail Mapping Visitasi Access - ID: $id_mapping, Sekolah: " . $mapping_data['nama_sekolah'] . ", User: " . $_SESSION['nm_user']);
    
    echo json_encode([
        'success' => true,
        'data' => $mapping_data,
        'message' => 'Detail mapping visitasi berhasil dimuat'
    ]);
    
} catch (Exception $e) {
    // Log error
    error_log("Detail Mapping Visitasi Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'data' => null,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
