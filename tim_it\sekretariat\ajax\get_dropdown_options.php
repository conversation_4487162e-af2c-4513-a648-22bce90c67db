<?php
/**
 * AJAX handler untuk mendapatkan dropdown options
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }

    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];

    $response_data = [];

    // Get dropdown kab/kota sesuai provinsi session
    $kota_query = "SELECT kota_id, nm_kota FROM kab_kota WHERE provinsi_id = ? ORDER BY nm_kota ASC";
    $stmt_kota = $conn->prepare($kota_query);
    $stmt_kota->bind_param("i", $provinsi_id_session);
    $stmt_kota->execute();
    $kota_result = $stmt_kota->get_result();

    $kota_data = [];
    while ($row = $kota_result->fetch_assoc()) {
        $kota_data[] = [
            'kota_id' => $row['kota_id'],
            'nm_kota' => $row['nm_kota']
        ];
    }

    $response_data['kota'] = $kota_data;

    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Dropdown options berhasil dimuat',
        'data' => $response_data
    ];

    echo json_encode($response);

} catch (Exception $e) {
    // Log error
    error_log("Get Dropdown Options Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
