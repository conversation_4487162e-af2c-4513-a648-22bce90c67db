/**
 * JavaScript untuk modul Data Kabupaten/Kota
 */

$(document).ready(function() {
    // Inisialisasi DataTable
    initDataTable();
    
    // Event handlers
    initEventHandlers();
});

/**
 * Inisialisasi DataTable
 */
function initDataTable() {
    $('#table-kab-kota').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: 'ajax/get_kab_kota.php',
            type: 'POST',
            error: function(xhr, error, thrown) {
                console.error('DataTables error:', error);
                showAlert('error', 'Gagal memuat data kabupaten/kota');
            }
        },
        columns: [
            { 
                data: null,
                orderable: false,
                searchable: false,
                render: function(data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { 
                data: 'kota_id',
                name: 'kota_id'
            },
            { 
                data: 'nm_kota',
                name: 'nm_kota'
            },
            {
                data: null,
                orderable: false,
                searchable: false,
                render: function(data, type, row) {
                    return '<button class="btn btn-warning btn-sm mr-1" onclick="showEditModal(' + 
                           row.id_kota + ')" title="Edit">' +
                           '<i class="fas fa-edit"></i></button>' +
                           '<button class="btn btn-danger btn-sm" onclick="confirmDelete(' + 
                           row.id_kota + ', \'' + row.nm_kota + '\', \'' + row.kota_id + '\')" title="Hapus">' +
                           '<i class="fas fa-trash"></i></button>';
                }
            }
        ],
        order: [[1, 'asc']],
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: "Memuat data...",
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data per halaman",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(difilter dari _MAX_ total data)",
            loadingRecords: "Memuat data...",
            zeroRecords: "Tidak ada data yang ditemukan",
            emptyTable: "Tidak ada data tersedia",
            paginate: {
                first: "Pertama",
                last: "Terakhir",
                next: "Selanjutnya",
                previous: "Sebelumnya"
            }
        },
        responsive: true,
        autoWidth: false,
        columnDefs: [
            {
                targets: 0, // Kolom NO
                width: '60px',
                className: 'text-center'
            },
            {
                targets: 1, // Kolom KODE
                width: '150px'
            },
            {
                targets: 3, // Kolom AKSI
                width: '120px',
                className: 'text-center'
            }
        ]
    });
}

/**
 * Inisialisasi event handlers
 */
function initEventHandlers() {
    // Event handler untuk form edit
    $('#form-edit-kab-kota').on('submit', function(e) {
        e.preventDefault();
        updateKabKota();
    });

    // Event handler untuk konfirmasi hapus
    $('#btn-konfirmasi-hapus').on('click', function() {
        var kabKotaId = $(this).data('kab-kota-id');
        var namaKabKota = $(this).data('nama-kab-kota');
        var kodeKabKota = $(this).data('kode-kab-kota');

        // Tutup modal konfirmasi
        $('#modal-konfirmasi-hapus').modal('hide');

        // Proses hapus
        deleteKabKota(kabKotaId, namaKabKota, kodeKabKota);
    });
}

/**
 * Fungsi untuk menampilkan modal edit
 */
function showEditModal(kabKotaId) {
    // Show loading
    $('#modal-edit-content').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Memuat data...</div>');
    $('#modal-edit').modal('show');

    // Load form edit via AJAX
    $.ajax({
        url: 'ajax/get_edit_form.php',
        type: 'POST',
        data: { id_kota: kabKotaId },
        dataType: 'html',
        success: function(response) {
            $('#modal-edit-content').html(response);
        },
        error: function(xhr, status, error) {
            $('#modal-edit-content').html('<div class="alert alert-danger">Gagal memuat form edit</div>');
        }
    });
}

/**
 * Fungsi untuk konfirmasi hapus
 */
function confirmDelete(kabKotaId, namaKabKota, kodeKabKota) {
    // Set data ke modal
    $('#hapus-nama-kab-kota').text(namaKabKota);
    $('#hapus-kode-kab-kota').text(kodeKabKota);
    
    // Set data ke tombol konfirmasi
    $('#btn-konfirmasi-hapus').data('kab-kota-id', kabKotaId);
    $('#btn-konfirmasi-hapus').data('nama-kab-kota', namaKabKota);
    $('#btn-konfirmasi-hapus').data('kode-kab-kota', kodeKabKota);
    
    // Tampilkan modal konfirmasi
    $('#modal-konfirmasi-hapus').modal('show');
}

/**
 * Fungsi untuk update data kabupaten/kota
 */
function updateKabKota() {
    // Disable tombol submit
    $('#btn-update').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    // Serialize form data
    var formData = $('#form-edit-kab-kota').serialize();

    $.ajax({
        url: 'ajax/update_kab_kota.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Tutup modal
                $('#modal-edit').modal('hide');

                // Reload DataTable
                $('#table-kab-kota').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat menyimpan data');
        },
        complete: function() {
            // Enable tombol submit
            $('#btn-update').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Perubahan');
        }
    });
}

/**
 * Fungsi untuk hapus data kabupaten/kota
 */
function deleteKabKota(kabKotaId, namaKabKota, kodeKabKota) {
    $.ajax({
        url: 'ajax/hapus_kab_kota.php',
        type: 'POST',
        data: {
            id_kota: kabKotaId,
            nm_kota: namaKabKota,
            kota_id: kodeKabKota
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Reload DataTable
                $('#table-kab-kota').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat menghapus data');
        }
    });
}

/**
 * Fungsi untuk menampilkan alert
 */
function showAlert(type, message) {
    var alertClass = 'alert-info';
    var icon = 'fas fa-info-circle';
    var title = 'Informasi';

    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            icon = 'fas fa-check-circle';
            title = 'Berhasil';
            break;
        case 'error':
            alertClass = 'alert-danger';
            icon = 'fas fa-exclamation-circle';
            title = 'Error';
            break;
        case 'warning':
            alertClass = 'alert-warning';
            icon = 'fas fa-exclamation-triangle';
            title = 'Peringatan';
            break;
    }

    // Update modal notification
    $('#modal-notification-header').removeClass().addClass('modal-header ' + alertClass);
    $('#modal-notification-icon').removeClass().addClass(icon);
    $('#modal-notification-text').text(title);
    $('#modal-notification-message').text(message);

    // Show modal
    $('#modal-notification').modal('show');

    // Auto hide after 3 seconds for success messages
    if (type === 'success') {
        setTimeout(function() {
            $('#modal-notification').modal('hide');
        }, 3000);
    }
}
