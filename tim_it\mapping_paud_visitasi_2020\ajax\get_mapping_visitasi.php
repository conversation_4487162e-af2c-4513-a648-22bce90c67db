<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

// Get parameters from DataTables
$draw = isset($_POST['draw']) ? intval($_POST['draw']) : 1;
$start = isset($_POST['start']) ? intval($_POST['start']) : 0;
$length = isset($_POST['length']) ? intval($_POST['length']) : 10;
$search_value = isset($_POST['search']['value']) ? $_POST['search']['value'] : '';
$order_column = isset($_POST['order'][0]['column']) ? intval($_POST['order'][0]['column']) : 0;
$order_dir = isset($_POST['order'][0]['dir']) ? $_POST['order'][0]['dir'] : 'asc';

// Get provinsi_id from session
$provinsi_id = $_SESSION['provinsi_id'];

// Get tahun akreditasi aktif
$tahun_query = "SELECT nama_tahun FROM mapping_paud_visitasi_tahun WHERE provinsi_id = ? ORDER BY id_mapping_tahun DESC LIMIT 1";
$tahun_stmt = $conn->prepare($tahun_query);
$tahun_stmt->bind_param("i", $provinsi_id);
$tahun_stmt->execute();
$tahun_result = $tahun_stmt->get_result();

$tahun_aktif = '';
if ($tahun_result->num_rows > 0) {
    $tahun_row = $tahun_result->fetch_assoc();
    $tahun_aktif = $tahun_row['nama_tahun'];
}

try {
    // Base query dengan JOIN ke semua tabel terkait
    $base_query = "FROM mapping_paud_visitasi mp
                   LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
                   LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                   LEFT JOIN kab_kota kk ON s.kota_id = kk.kota_id
                   LEFT JOIN asesor_1 a1 ON mp.kd_asesor1 = a1.kd_asesor1
                   LEFT JOIN asesor_2 a2 ON mp.kd_asesor2 = a2.kd_asesor2
                   LEFT JOIN mapping_paud_visitasi_tahun mt ON mp.tahun_akreditasi = mt.nama_tahun AND mt.provinsi_id = $provinsi_id
                   WHERE mp.provinsi_id = $provinsi_id
                     AND s.rumpun = 'paud'
                     AND s.soft_delete = '1'
                     AND a1.soft_delete = '1'
                     AND a2.soft_delete = '1'
                     AND mp.tahun_akreditasi = mt.nama_tahun";

    // Add search functionality
    if (!empty($search_value)) {
        $base_query .= " AND (s.npsn LIKE '%$search_value%' 
                            OR s.nama_sekolah LIKE '%$search_value%'
                            OR j.nm_jenjang LIKE '%$search_value%'
                            OR kk.nm_kota LIKE '%$search_value%'
                            OR a1.nia1 LIKE '%$search_value%'
                            OR a1.nm_asesor1 LIKE '%$search_value%'
                            OR a2.nia2 LIKE '%$search_value%'
                            OR a2.nm_asesor2 LIKE '%$search_value%'
                            OR mp.tahun_akreditasi LIKE '%$search_value%'
                            OR mp.tahap LIKE '%$search_value%')";
    }

    // Count total records
    $total_query = "SELECT COUNT(*) as total " . $base_query;
    $total_result = $conn->query($total_query);
    $total_records = $total_result->fetch_assoc()['total'];

    // Column mapping for ordering
    $columns = [
        0 => 'mp.id_mapping', // NO (not orderable, but fallback)
        1 => 's.npsn',
        2 => 's.nama_sekolah',
        3 => 'j.nm_jenjang',
        4 => 'kk.nm_kota',
        5 => 'a1.nia1',
        6 => 'a1.nm_asesor1',
        7 => 'a2.nia2',
        8 => 'a2.nm_asesor2',
        9 => 'mp.tahun_akreditasi',
        10 => 'mp.tahap',
        11 => 'mp.id_mapping' // AKSI (not orderable, but fallback)
    ];

    // Add ordering
    $order_column_name = isset($columns[$order_column]) ? $columns[$order_column] : 'mp.tahun_akreditasi';
    $base_query .= " ORDER BY $order_column_name $order_dir";

    // Add pagination
    $base_query .= " LIMIT $start, $length";

    // Main data query
    $data_query = "SELECT 
                      mp.id_mapping,
                      mp.sekolah_id,
                      mp.kd_asesor1,
                      mp.kd_asesor2,
                      mp.tgl_mulai_visitasi,
                      mp.tgl_akhir_visitasi,
                      mp.tahap,
                      mp.no_surat,
                      mp.tgl_surat,
                      mp.tahun_akreditasi,
                      mp.provinsi_id,
                      
                      -- Data Sekolah
                      s.nama_sekolah,
                      s.npsn,
                      s.jenjang_id,
                      s.rumpun,
                      s.alamat,
                      s.kota_id,
                      s.desa_kelurahan,
                      s.kecamatan,
                      s.nama_kepsek,
                      s.no_hp_kepsek,
                      s.no_wa_kepsek,
                      
                      -- Data Jenjang
                      j.nm_jenjang,
                      
                      -- Data Kab/Kota Sekolah
                      kk.nm_kota,
                      
                      -- Data Asesor 1
                      a1.nia1,
                      a1.nm_asesor1,
                      a1.no_hp as no_hp_asesor1,
                      a1.unit_kerja as unit_kerja_asesor1,
                      a1.kota_id1,
                      
                      -- Data Asesor 2
                      a2.nia2,
                      a2.nm_asesor2,
                      a2.no_hp as no_hp_asesor2,
                      a2.unit_kerja as unit_kerja_asesor2,
                      a2.kota_id2
                      
                   " . $base_query;

    $data_result = $conn->query($data_query);
    
    $data = [];
    if ($data_result && $data_result->num_rows > 0) {
        while ($row = $data_result->fetch_assoc()) {
            $data[] = $row;
        }
    }

    // Response
    echo json_encode([
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $total_records,
        'data' => $data
    ]);

} catch (Exception $e) {
    echo json_encode([
        'draw' => $draw,
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => $e->getMessage()
    ]);
}

$conn->close();
?>
