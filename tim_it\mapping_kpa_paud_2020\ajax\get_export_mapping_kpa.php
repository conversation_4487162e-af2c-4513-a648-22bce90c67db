<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Get provinsi_id from session
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Get tahun akreditasi aktif
    $tahun_query = "SELECT nama_tahun FROM mapping_paud_kpa_tahun WHERE provinsi_id = ? ORDER BY id_mapping_tahun DESC LIMIT 1";
    $tahun_stmt = $conn->prepare($tahun_query);
    $tahun_stmt->bind_param("i", $provinsi_id);
    $tahun_stmt->execute();
    $tahun_result = $tahun_stmt->get_result();
    
    $tahun_aktif = '';
    if ($tahun_result->num_rows > 0) {
        $tahun_row = $tahun_result->fetch_assoc();
        $tahun_aktif = $tahun_row['nama_tahun'];
    }
    
    // Query untuk export data mapping KPA dengan filter tahun aktif
    $export_query = "SELECT 
                        mp.id_mapping,
                        mp.sekolah_id,
                        mp.kd_asesor,
                        mp.tgl_penetapan_kpa,
                        mp.tahap,
                        mp.tahun_akreditasi,
                        mp.file_laporan_hasil_kpa,
                        mp.tgl_file_hasil_kpa,
                        mp.jam_file_hasil_kpa,
                        
                        -- Data Sekolah
                        s.nama_sekolah,
                        s.npsn,
                        s.jenjang_id,
                        s.rumpun,
                        s.alamat,
                        s.kota_id,
                        s.desa_kelurahan,
                        s.kecamatan,
                        s.nama_kepsek,
                        s.no_hp_kepsek,
                        s.no_wa_kepsek,
                        
                        -- Data Jenjang
                        j.nm_jenjang,
                        
                        -- Data Kab/Kota Sekolah
                        kk_s.nm_kota as nm_kota_sekolah,
                        
                        -- Data Asesor
                        a.nia,
                        a.nm_asesor,
                        a.no_hp,
                        a.unit_kerja,
                        a.rumpun as rumpun_asesor,
                        a.kota_id as asesor_kota_id,
                        
                        -- Data Kab/Kota Asesor
                        kk_a.nm_kota as nm_kota_asesor
                        
                     FROM mapping_paud_kpa mp
                     LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
                     LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                     LEFT JOIN kab_kota kk_s ON s.kota_id = kk_s.kota_id
                     LEFT JOIN asesor a ON mp.kd_asesor = a.kd_asesor
                     LEFT JOIN kab_kota kk_a ON a.kota_id = kk_a.kota_id
                     LEFT JOIN mapping_paud_kpa_tahun mt ON mp.tahun_akreditasi = mt.nama_tahun AND mt.provinsi_id = ?
                     WHERE mp.provinsi_id = ?
                       AND s.rumpun = 'paud'
                       AND s.soft_delete = '1'
                       AND a.soft_delete = '1'
                       AND mp.tahun_akreditasi = mt.nama_tahun
                     ORDER BY s.nama_sekolah ASC";
    
    $export_stmt = $conn->prepare($export_query);
    $export_stmt->bind_param("ii", $provinsi_id, $provinsi_id);
    $export_stmt->execute();
    $export_result = $export_stmt->get_result();
    
    $data = [];
    if ($export_result && $export_result->num_rows > 0) {
        while ($row = $export_result->fetch_assoc()) {
            $data[] = $row;
        }
    }
    
    // Log export activity
    error_log("Export Mapping KPA - Provinsi: $provinsi_id, Tahun: $tahun_aktif, Records: " . count($data) . ", User: " . $_SESSION['nm_user']);
    
    // Response
    echo json_encode([
        'success' => true,
        'data' => $data,
        'tahun_aktif' => $tahun_aktif,
        'total_records' => count($data),
        'message' => 'Data export berhasil dimuat'
    ]);
    
} catch (Exception $e) {
    error_log("Export Mapping KPA Error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'data' => [],
        'message' => 'Terjadi kesalahan saat memuat data export: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
