<?php
/**
 * AJAX handler untuk menyimpan data nilai hasil akreditasi
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }


    
    // Validasi input required (kecuali sekolah_id dan nilai_akhir yang akan divalidasi terpisah)
    $required_fields = ['npsn', 'id_prog_ahli', 'peringkat', 'status', 'tahun_akreditasi', 'tahun_berakhir'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            throw new Exception("Field {$field} harus diisi");
        }
    }

    // Validasi khusus untuk nilai_akhir (boleh 0 atau kosong)
    if (!isset($_POST['nilai_akhir'])) {
        throw new Exception("Field nilai_akhir harus ada");
    }

    // Validasi khusus untuk sekolah_id (boleh kosong, akan dicari berdasarkan NPSN)
    if (!isset($_POST['sekolah_id']) || empty($_POST['sekolah_id'])) {
        // Jika sekolah_id kosong, cari berdasarkan NPSN
        $npsn_temp = trim($_POST['npsn']);
        $provinsi_id_temp = $_SESSION['provinsi_id'];
        $npsn_escaped_temp = $conn->real_escape_string($npsn_temp);

        $find_sekolah = "SELECT sekolah_id FROM sekolah
                         WHERE npsn = '$npsn_escaped_temp'
                           AND rumpun = 'kesetaraan'
                           AND provinsi_id = $provinsi_id_temp
                           AND soft_delete = '1'";
        $result_find = $conn->query($find_sekolah);

        if ($result_find->num_rows == 0) {
            throw new Exception("NPSN tidak ditemukan atau bukan sekolah kesetaraan di provinsi Anda");
        }

        $found_sekolah = $result_find->fetch_assoc();
        $_POST['sekolah_id'] = $found_sekolah['sekolah_id'];
    }
    
    // Sanitize dan ambil data dari POST
    $npsn = trim($_POST['npsn']);
    $sekolah_id = intval($_POST['sekolah_id']);
    $id_prog_ahli = intval($_POST['id_prog_ahli']);

    // Nilai akhir boleh kosong atau 0
    $nilai_akhir = 0; // Default value
    if (isset($_POST['nilai_akhir']) && $_POST['nilai_akhir'] !== '') {
        $nilai_akhir = intval($_POST['nilai_akhir']);
    }

    $program = trim($_POST['program']);
    $peringkat = trim($_POST['peringkat']);
    $status = trim($_POST['status']);
    $tahun_akreditasi = intval($_POST['tahun_akreditasi']);
    $tahun_berakhir = intval($_POST['tahun_berakhir']);
    $tgl_sk_penetapan = isset($_POST['tgl_sk_penetapan']) && !empty($_POST['tgl_sk_penetapan']) ? $_POST['tgl_sk_penetapan'] : '';
    $no_sk = isset($_POST['no_sk']) ? trim($_POST['no_sk']) : '';
    
    // Data fixed
    $provinsi_id = $_SESSION['provinsi_id']; // Dari session user
    
    // Validasi nilai akhir (boleh 0, tapi tidak boleh negatif atau > 100)
    if ($nilai_akhir < 0 || $nilai_akhir > 100) {
        throw new Exception('Nilai akhir harus antara 0-100');
    }
    
    // Validasi peringkat
    $valid_peringkat = ['A', 'B', 'C', 'TT'];
    if (!in_array($peringkat, $valid_peringkat)) {
        throw new Exception('Peringkat tidak valid');
    }
    
    // Validasi program paket
    $valid_program = ['Paket A', 'Paket B', 'Paket C', 'Satdik'];
    if (!in_array($program, $valid_program)) {
        throw new Exception('Program paket tidak valid');
    }

    // Validasi status
    $valid_status = ['Terakreditasi', 'Tidak Terakreditasi'];
    if (!in_array($status, $valid_status)) {
        throw new Exception('Status akreditasi tidak valid');
    }
    
    // Validasi tahun
    if ($tahun_berakhir <= $tahun_akreditasi) {
        throw new Exception('Tahun berakhir harus lebih besar dari tahun akreditasi');
    }
    
    // Validasi sekolah_id ada dan sesuai NPSN serta provinsi session
    $npsn_escaped = $conn->real_escape_string($npsn);
    $check_sekolah = "SELECT sekolah_id FROM sekolah 
                      WHERE sekolah_id = $sekolah_id 
                        AND npsn = '$npsn_escaped' 
                        AND rumpun = 'kesetaraan' 
                        AND provinsi_id = $provinsi_id 
                        AND soft_delete = '1'";
    $result_check_sekolah = $conn->query($check_sekolah);
    
    if ($result_check_sekolah->num_rows == 0) {
        throw new Exception('Data sekolah tidak valid atau tidak sesuai dengan provinsi Anda');
    }
    
    // Validasi id_prog_ahli ada dalam daftar yang diizinkan
    $valid_prog_ahli = [77, 79, 80, 82, 83, 84];
    if (!in_array($id_prog_ahli, $valid_prog_ahli)) {
        throw new Exception('Program keahlian tidak valid');
    }
    
    // Escape semua data untuk keamanan
    $program = $conn->real_escape_string($program);
    $peringkat = $conn->real_escape_string($peringkat);
    $status = $conn->real_escape_string($status);
    $no_sk = $conn->real_escape_string($no_sk);
    
    // Format tanggal SK
    $tgl_sk_insert = !empty($tgl_sk_penetapan) ? "'$tgl_sk_penetapan'" : 'NULL';
    
    // Insert ke tabel hasil_akreditasi
    $sql_insert = "INSERT INTO hasil_akreditasi (
                sekolah_id, id_prog_ahli, nilai_akhir, program, peringkat, tahun_akreditasi,
                tahun_berakhir, status, tgl_sk_penetapan, no_sk, provinsi_id
            ) VALUES (
                $sekolah_id, $id_prog_ahli, $nilai_akhir, '$program', '$peringkat', $tahun_akreditasi,
                $tahun_berakhir, '$status', $tgl_sk_insert, '$no_sk', $provinsi_id
            )";
    
    if (!$conn->query($sql_insert)) {
        throw new Exception('Gagal menyimpan data nilai akreditasi: ' . $conn->error);
    }
    
    $hasil_id = $conn->insert_id;
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data nilai akreditasi berhasil disimpan',
        'data' => [
            'id_hasil_akreditasi' => $hasil_id,
            'npsn' => $npsn,
            'nilai_akhir' => $nilai_akhir,
            'peringkat' => $peringkat,
            'tahun_akreditasi' => $tahun_akreditasi
        ]
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Log error
    error_log("Simpan Nilai Akreditasi Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
