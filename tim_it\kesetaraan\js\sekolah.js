/**
 * JavaScript untuk halaman Data kesetaraan (Sekolah)
 * Menggunakan DataTables Server-side Processing
 */

$(document).ready(function() {
    
    // Inisialisasi DataTable
    var table = $('#table-sekolah').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "ajax/get_sekolah.php",
            "type": "POST",
            "error": function(xhr, error, thrown) {
                console.error('DataTables Error:', error);
                showAlert('error', 'Terjadi kesalahan saat memuat data: ' + error);
            }
        },
        "columns": [
            { 
                "data": null,
                "orderable": false,
                "searchable": false,
                "render": function(data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { 
                "data": "npsn",
                "render": function(data, type, row) {
                    return data ? data : '-';
                }
            },
            { 
                "data": "nama_sekolah",
                "render": function(data, type, row) {
                    return '<strong>' + data + '</strong>';
                }
            },
            { 
                "data": "nm_jenjang",
                "className": "text-center"
            },
            { 
                "data": "nm_kota"
            },
            { 
                "data": "kecamatan",
                "render": function(data, type, row) {
                    return data ? data : '-';
                }
            },
            {
                "data": "nm_status",
                "className": "text-center",
                "orderable": true,
                "render": function(data, type, row) {
                    if (row.status_keaktifan_id == '1') {
                        return '<span class="badge badge-success status-badge">Aktif</span>';
                    } else if (row.status_keaktifan_id == '0') {
                        return '<span class="badge badge-danger status-badge">Tidak Aktif</span>';
                    } else if (row.status_keaktifan_id == '2') {
                        return '<span class="badge badge-warning status-badge">Tidak Diketahui</span>';
                    } else {
                        return '<span class="badge badge-secondary status-badge">' + (data || 'Unknown') + '</span>';
                    }
                }
            },
            {
                "data": null,
                "className": "text-center",
                "orderable": false,
                "searchable": false,
                "render": function(data, type, row) {
                    var buttons = '';

                    // Tombol Detail
                    buttons += '<button type="button" class="btn btn-info btn-sm btn-action mr-1" ' +
                              'onclick="showDetail(' + row.sekolah_id + ')" title="Lihat Detail">' +
                              '<i class="fas fa-eye"></i>' +
                              '</button>';

                    // Tombol Edit
                    buttons += '<button type="button" class="btn btn-warning btn-sm btn-action mr-1" ' +
                              'onclick="showModalEdit(' + row.sekolah_id + ')" title="Edit Data">' +
                              '<i class="fas fa-edit"></i>' +
                              '</button>';

                    // Tombol Hapus
                    buttons += '<button type="button" class="btn btn-danger btn-sm btn-action" ' +
                              'onclick="confirmDelete(' + row.sekolah_id + ', \'' + row.nama_sekolah.replace(/'/g, "\\'") + '\')" title="Hapus Data">' +
                              '<i class="fas fa-trash"></i>' +
                              '</button>';

                    return buttons;
                }
            }
        ],
        "order": [[2, "asc"]], // Sort by nama sekolah
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]],
        "language": {
            "processing": "Sedang memproses...",
            "lengthMenu": "Tampilkan _MENU_ entri",
            "zeroRecords": "Tidak ditemukan data yang sesuai",
            "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ entri",
            "infoEmpty": "Menampilkan 0 sampai 0 dari 0 entri",
            "infoFiltered": "(disaring dari _MAX_ entri keseluruhan)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        },
        "responsive": true,
        "autoWidth": false
    });

    // Event handler untuk tombol export excel
    $('#btn-export-excel').click(function() {
        exportToExcel();
    });

    // Event handler untuk tombol tambah data
    $('#btn-add').click(function() {
        showModalTambah();
    });

    // Event handler untuk form submit tambah
    $('#form-tambah-sekolah').submit(function(e) {
        e.preventDefault();
        simpanDataSekolah();
    });

    // Event handler untuk form submit edit akan di-bind ulang di loadEditForm()

    // Event handler untuk konfirmasi hapus
    $('#btn-konfirmasi-hapus').click(function() {
        var sekolahId = $(this).data('sekolah-id');
        var namaSekolah = $(this).data('nama-sekolah');

        // Tutup modal konfirmasi
        $('#modal-konfirmasi-hapus').modal('hide');

        // Proses hapus
        deleteSekolah(sekolahId, namaSekolah);
    });

});

/**
 * Fungsi untuk menampilkan modal tambah data
 */
function showModalTambah() {
    // Reset form
    $('#form-tambah-sekolah')[0].reset();

    // Load dropdown options
    loadDropdownOptions();

    // Show modal
    $('#modal-tambah').modal('show');
}

/**
 * Fungsi untuk menampilkan modal edit data
 */
function showModalEdit(sekolahId) {
    // Show loading di modal body
    $('#modal-edit .modal-content').html(`
        <div class="modal-header bg-warning">
            <h5 class="modal-title text-white">
                <i class="fas fa-edit"></i> Edit Data Sekolah kesetaraan
            </h5>
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body text-center">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p class="mt-2">Memuat form edit...</p>
        </div>
    `);

    // Show modal
    $('#modal-edit').modal('show');

    // Load form edit dengan data yang sudah ter-populate
    loadEditForm(sekolahId);
}

/**
 * Fungsi untuk load form edit dengan data yang sudah ter-populate
 */
function loadEditForm(sekolahId) {
    $.ajax({
        url: 'ajax/get_edit_form.php',
        type: 'POST',
        data: { sekolah_id: sekolahId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Replace modal content dengan form yang sudah ter-populate
                $('#modal-edit .modal-content').html(response.html);

                // Disable HTML5 validation completely
                $('#form-edit-sekolah')[0].setAttribute('novalidate', 'novalidate');

                // Remove all required attributes to prevent HTML5 validation
                $('#form-edit-sekolah').find('[required]').removeAttr('required');

                // Ensure tahun_berdiri field has a value (even if empty string)
                if ($('#edit_tahun_berdiri').val() === null || $('#edit_tahun_berdiri').val() === undefined) {
                    $('#edit_tahun_berdiri').val('');
                }

                // Unbind ALL event handlers from form and button
                $('#form-edit-sekolah').off();
                $('#btn-update').off();

                // Re-bind form submit event with high priority
                $('#form-edit-sekolah').on('submit.custom', function(e) {
                    e.preventDefault();
                    e.stopImmediatePropagation();

                    // Manual validation for truly required fields only
                    var npsn = $('#edit_npsn').val().trim();
                    var nama_sekolah = $('#edit_nama_sekolah').val().trim();
                    var jenjang_id = $('#edit_jenjang_id').val();
                    var tipe_sekolah_id = $('#edit_tipe_sekolah_id').val();
                    var status_sekolah_id = $('#edit_status_sekolah_id').val();
                    var status_keaktifan_id = $('#edit_status_keaktifan_id').val();
                    var kota_id = $('#edit_kota_id').val();

                    if (!npsn) {
                        showAlert('error', 'NPSN harus diisi');
                        return false;
                    }
                    if (!nama_sekolah) {
                        showAlert('error', 'Nama Sekolah harus diisi');
                        return false;
                    }
                    if (!jenjang_id) {
                        showAlert('error', 'Jenjang harus dipilih');
                        return false;
                    }
                    if (!tipe_sekolah_id) {
                        showAlert('error', 'Tipe Sekolah harus dipilih');
                        return false;
                    }
                    if (!status_sekolah_id) {
                        showAlert('error', 'Status Sekolah harus dipilih');
                        return false;
                    }
                    if (status_keaktifan_id === '' || status_keaktifan_id === null || status_keaktifan_id === undefined) {
                        showAlert('error', 'Status Keaktifan harus dipilih');
                        return false;
                    }
                    if (!kota_id) {
                        showAlert('error', 'Kabupaten/Kota harus dipilih');
                        return false;
                    }

                    updateDataSekolah();
                });

                // Also bind click event to button as backup
                $('#btn-update').on('click.custom', function(e) {
                    e.preventDefault();
                    e.stopImmediatePropagation();

                    // Same validation as form submit
                    var npsn = $('#edit_npsn').val().trim();
                    var nama_sekolah = $('#edit_nama_sekolah').val().trim();
                    var jenjang_id = $('#edit_jenjang_id').val();
                    var tipe_sekolah_id = $('#edit_tipe_sekolah_id').val();
                    var status_sekolah_id = $('#edit_status_sekolah_id').val();
                    var status_keaktifan_id = $('#edit_status_keaktifan_id').val();
                    var kota_id = $('#edit_kota_id').val();

                    if (!npsn) {
                        showAlert('error', 'NPSN harus diisi');
                        return false;
                    }
                    if (!nama_sekolah) {
                        showAlert('error', 'Nama Sekolah harus diisi');
                        return false;
                    }
                    if (!jenjang_id) {
                        showAlert('error', 'Jenjang harus dipilih');
                        return false;
                    }
                    if (!tipe_sekolah_id) {
                        showAlert('error', 'Tipe Sekolah harus dipilih');
                        return false;
                    }
                    if (!status_sekolah_id) {
                        showAlert('error', 'Status Sekolah harus dipilih');
                        return false;
                    }
                    if (status_keaktifan_id === '' || status_keaktifan_id === null || status_keaktifan_id === undefined) {
                        showAlert('error', 'Status Keaktifan harus dipilih');
                        return false;
                    }
                    if (!kota_id) {
                        showAlert('error', 'Kabupaten/Kota harus dipilih');
                        return false;
                    }

                    updateDataSekolah();
                });

                // Form edit berhasil dimuat
            } else {
                $('#modal-edit .modal-content').html(`
                    <div class="modal-header bg-danger">
                        <h5 class="modal-title text-white">
                            <i class="fas fa-exclamation-triangle"></i> Error
                        </h5>
                        <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                        <p class="mt-2">${response.message}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    </div>
                `);
            }
        },
        error: function(xhr, status, error) {
            $('#modal-edit .modal-content').html(`
                <div class="modal-header bg-danger">
                    <h5 class="modal-title text-white">
                        <i class="fas fa-exclamation-triangle"></i> Error
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                    <p class="mt-2">Terjadi kesalahan saat memuat form edit</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                </div>
            `);
        }
    });
}


/**
 * Fungsi untuk update data sekolah
 */
function updateDataSekolah() {
    // Disable tombol submit
    $('#btn-update').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Mengupdate...');

    // Collect form data manually to ensure all fields are included
    var formData = {
        sekolah_id: $('input[name="sekolah_id"]').val(),
        npsn: $('#edit_npsn').val(),
        nama_sekolah: $('#edit_nama_sekolah').val(),
        jenjang_id: $('#edit_jenjang_id').val(),
        tipe_sekolah_id: $('#edit_tipe_sekolah_id').val(),
        status_sekolah_id: $('#edit_status_sekolah_id').val(),
        kota_id: $('#edit_kota_id').val(),
        kecamatan: $('#edit_kecamatan').val(),
        desa_kelurahan: $('#edit_desa_kelurahan').val(),
        alamat: $('#edit_alamat').val(),
        nama_yayasan: $('#edit_nama_yayasan').val(),
        no_akte: $('#edit_no_akte').val(),
        tahun_berdiri: $('#edit_tahun_berdiri').val(),
        nama_kepsek: $('#edit_nama_kepsek').val(),
        no_hp_kepsek: $('#edit_no_hp_kepsek').val(),
        no_wa_kepsek: $('#edit_no_wa_kepsek').val(),
        nama_operator: $('#edit_nama_operator').val(),
        no_hp_operator: $('#edit_no_hp_operator').val(),
        no_wa_operator: $('#edit_no_wa_operator').val(),
        email: $('#edit_email').val(),
        status_keaktifan_id: $('#edit_status_keaktifan_id').val(),
        rumpun: $('input[name="rumpun"]').val(),
        provinsi_id: $('input[name="provinsi_id"]').val(),
        soft_delete: $('input[name="soft_delete"]').val()
    };

    // AJAX request
    $.ajax({
        url: 'ajax/update_sekolah.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Tutup modal
                $('#modal-edit').modal('hide');

                // Reload DataTable
                $('#table-sekolah').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat mengupdate data');
        },
        complete: function() {
            // Enable tombol submit
            $('#btn-update').prop('disabled', false).html('<i class="fas fa-save"></i> Update Data');
        }
    });
}

/**
 * Fungsi untuk load dropdown options
 */
function loadDropdownOptions() {
    // Load jenjang options
    $.ajax({
        url: 'ajax/get_dropdown_options.php',
        type: 'POST',
        data: { type: 'jenjang' },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var options = '<option value="">Pilih Jenjang</option>';
                $.each(response.data, function(index, item) {
                    options += '<option value="' + item.jenjang_id + '">' + item.nm_jenjang + '</option>';
                });
                $('#jenjang_id').html(options);
            }
        }
    });

    // Load kota options
    $.ajax({
        url: 'ajax/get_dropdown_options.php',
        type: 'POST',
        data: { type: 'kota' },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var options = '<option value="">Pilih Kab/Kota</option>';
                $.each(response.data, function(index, item) {
                    options += '<option value="' + item.kota_id + '">' + item.nm_kota + '</option>';
                });
                $('#kota_id').html(options);
            }
        }
    });

    // Load tipe sekolah options
    $.ajax({
        url: 'ajax/get_dropdown_options.php',
        type: 'POST',
        data: { type: 'tipe_sekolah' },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var options = '<option value="">Pilih Tipe Sekolah</option>';
                $.each(response.data, function(index, item) {
                    options += '<option value="' + item.tipe_sekolah_id + '">' + item.nm_tipe_sekolah + '</option>';
                });
                $('#tipe_sekolah_id').html(options);
            }
        }
    });

    // Load status sekolah options
    $.ajax({
        url: 'ajax/get_dropdown_options.php',
        type: 'POST',
        data: { type: 'status_sekolah' },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var options = '<option value="">Pilih Status Sekolah</option>';
                $.each(response.data, function(index, item) {
                    options += '<option value="' + item.status_sekolah_id + '">' + item.nm_status_sekolah + '</option>';
                });
                $('#status_sekolah_id').html(options);
            }
        }
    });
}

/**
 * Fungsi untuk menyimpan data sekolah
 */
function simpanDataSekolah() {
    // Disable tombol submit
    $('#btn-simpan').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    // Serialize form data
    var formData = $('#form-tambah-sekolah').serialize();

    // AJAX request
    $.ajax({
        url: 'ajax/simpan_sekolah.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Tutup modal
                $('#modal-tambah').modal('hide');

                // Reload DataTable
                $('#table-sekolah').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat menyimpan data');
        },
        complete: function() {
            // Enable tombol submit
            $('#btn-simpan').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Data');
        }
    });
}

/**
 * Fungsi untuk konfirmasi hapus data menggunakan modal
 */
function confirmDelete(sekolahId, namaSekolah) {
    // Set data ke tombol konfirmasi
    $('#btn-konfirmasi-hapus').data('sekolah-id', sekolahId);
    $('#btn-konfirmasi-hapus').data('nama-sekolah', namaSekolah);

    // Set nama sekolah di modal
    $('#nama-sekolah-hapus').text(namaSekolah);

    // Tampilkan modal konfirmasi
    $('#modal-konfirmasi-hapus').modal('show');
}

/**
 * Fungsi untuk menghapus data sekolah (soft delete)
 */
function deleteSekolah(sekolahId, namaSekolah) {
    // Show loading
    showAlert('info', 'Sedang menghapus data...');

    $.ajax({
        url: 'ajax/hapus_sekolah.php',
        type: 'POST',
        data: {
            sekolah_id: sekolahId,
            nama_sekolah: namaSekolah
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Reload DataTable
                $('#table-sekolah').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat menghapus data');
        }
    });
}

/**
 * Fungsi untuk menampilkan detail sekolah
 */
function showDetail(sekolahId) {
    $('#modal-detail').modal('show');
    
    // Reset content
    $('#modal-detail-content').html(
        '<div class="text-center">' +
        '<i class="fas fa-spinner fa-spin fa-2x"></i>' +
        '<p class="mt-2">Memuat data...</p>' +
        '</div>'
    );

    // Load detail via AJAX
    $.ajax({
        url: 'ajax/get_detail_sekolah.php',
        type: 'POST',
        data: { sekolah_id: sekolahId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                displayDetailContent(response.data);
            } else {
                $('#modal-detail-content').html(
                    '<div class="alert alert-danger">' +
                    '<i class="fas fa-exclamation-triangle"></i> ' +
                    response.message +
                    '</div>'
                );
            }
        },
        error: function(xhr, status, error) {
            $('#modal-detail-content').html(
                '<div class="alert alert-danger">' +
                '<i class="fas fa-exclamation-triangle"></i> ' +
                'Terjadi kesalahan saat memuat detail sekolah' +
                '</div>'
            );
        }
    });
}

/**
 * Fungsi untuk menampilkan konten detail sekolah
 */
function displayDetailContent(data) {
    var html = '<div class="row">';
    
    // Kolom kiri
    html += '<div class="col-md-6">';
    html += createDetailRow('NPSN', data.npsn || '-');
    html += createDetailRow('Nama Sekolah', data.nama_sekolah);
    html += createDetailRow('Jenjang', data.nm_jenjang);
    html += createDetailRow('Rumpun', data.rumpun || '-');
    html += createDetailRow('Tipe Sekolah', data.nm_tipe_sekolah || '-');
    html += createDetailRow('Status Sekolah', data.nm_status_sekolah || '-');
    html += createDetailRow('Tahun Berdiri', data.tahun_berdiri || '-');
    html += createDetailRow('Nama Yayasan', data.nama_yayasan || '-');
    html += createDetailRow('No. Akte', data.no_akte || '-');
    html += '</div>';
    
    // Kolom kanan
    html += '<div class="col-md-6">';
    html += createDetailRow('Alamat', data.alamat || '-');
    html += createDetailRow('Kecamatan', data.kecamatan || '-');
    html += createDetailRow('Desa/Kelurahan', data.desa_kelurahan || '-');
    html += createDetailRow('Kab/Kota', data.nm_kota);
    html += createDetailRow('Nama Kepala Sekolah', data.nama_kepsek || '-');
    html += createDetailRow('No. HP Kepsek', data.no_hp_kepsek || '-');
    html += createDetailRow('No. WA Kepsek', data.no_wa_kepsek || '-');
    html += createDetailRow('Email', data.email || '-');
    html += '</div>';
    
    html += '</div>';
    
    $('#modal-detail-content').html(html);
}

/**
 * Fungsi helper untuk membuat baris detail
 */
function createDetailRow(label, value) {
    return '<div class="modal-detail-row">' +
           '<div class="modal-detail-label">' + label + ':</div>' +
           '<div class="modal-detail-value">' + value + '</div>' +
           '</div>';
}

/**
 * Fungsi untuk export data ke Excel menggunakan SheetJS
 */
function exportToExcel() {
    // Show loading
    showAlert('info', 'Sedang memproses export Excel...');

    // Get DataTable instance
    var table = $('#table-sekolah').DataTable();

    // Get all data from server (not just current page)
    $.ajax({
        url: 'ajax/get_export_data.php',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Prepare data for Excel
                var excelData = [];

                // Add header row
                excelData.push([
                    'NO',
                    'NPSN',
                    'NAMA SEKOLAH',
                    'JENJANG',
                    'KAB/KOTA',
                    'KECAMATAN',
                    'STATUS',
                    'ALAMAT',
                    'NAMA YAYASAN',
                    'NO AKTE',
                    'NAMA KEPALA SEKOLAH',
                    'NO HP KEPSEK',
                    'EMAIL'
                ]);

                // Add data rows
                response.data.forEach(function(row, index) {
                    excelData.push([
                        index + 1,
                        row.npsn || '',
                        row.nama_sekolah || '',
                        row.nm_jenjang || '',
                        row.nm_kota || '',
                        row.kecamatan || '',
                        row.nm_status || '',
                        row.alamat || '',
                        row.nama_yayasan || '',
                        row.no_akte || '',
                        row.nama_kepsek || '',
                        row.no_hp_kepsek || '',
                        row.email || ''
                    ]);
                });

                // Create workbook and worksheet
                var wb = XLSX.utils.book_new();
                var ws = XLSX.utils.aoa_to_sheet(excelData);

                // Set column widths
                ws['!cols'] = [
                    {wch: 5},   // NO
                    {wch: 12},  // NPSN
                    {wch: 30},  // NAMA SEKOLAH
                    {wch: 10},  // JENJANG
                    {wch: 15},  // KAB/KOTA
                    {wch: 15},  // KECAMATAN
                    {wch: 10},  // STATUS
                    {wch: 25},  // ALAMAT
                    {wch: 20},  // NAMA YAYASAN
                    {wch: 15},  // NO AKTE
                    {wch: 20},  // NAMA KEPSEK
                    {wch: 15},  // NO HP
                    {wch: 20}   // EMAIL
                ];

                // Add worksheet to workbook
                XLSX.utils.book_append_sheet(wb, ws, "Data Sekolah kesetaraan");

                // Generate filename with current date
                var today = new Date();
                var dateStr = today.getFullYear() + '-' +
                             String(today.getMonth() + 1).padStart(2, '0') + '-' +
                             String(today.getDate()).padStart(2, '0');
                var filename = 'Data_Sekolah_kesetaraan_' + dateStr + '.xlsx';

                // Save file
                XLSX.writeFile(wb, filename);

                // Show success message
                showAlert('success', 'File Excel berhasil didownload: ' + filename);

            } else {
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat export data');
        }
    });
}

/**
 * Fungsi untuk menampilkan notifikasi modal
 */
function showNotification(type, message) {
    if (type === 'success') {
        $('#notifikasi-sukses-message').text(message);
        $('#modal-notifikasi-sukses').modal('show');
    } else if (type === 'error') {
        $('#notifikasi-error-message').text(message);
        $('#modal-notifikasi-error').modal('show');
    }
}

/**
 * Fungsi untuk menampilkan alert (fallback untuk loading/info)
 */
function showAlert(type, message) {
    // Untuk success dan error, gunakan modal
    if (type === 'success' || type === 'error') {
        showNotification(type, message);
        return;
    }

    // Untuk info/warning, gunakan alert biasa
    var alertClass = 'alert-info';
    var icon = 'fas fa-info-circle';

    switch(type) {
        case 'warning':
            alertClass = 'alert-warning';
            icon = 'fas fa-exclamation-circle';
            break;
    }

    var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                    '<i class="' + icon + '"></i> ' + message +
                    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                    '<span aria-hidden="true">&times;</span>' +
                    '</button>' +
                    '</div>';

    $('#alert-container').html(alertHtml);

    // Auto hide after 3 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 3000);
}