<?php
/**
 * AJAX handler untuk mengupdate data sekolah
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    $required_fields = ['sekolah_id', 'npsn', 'nama_sekolah', 'jenjang_id', 'tipe_sekolah_id', 'status_sekolah_id', 'kota_id'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
            throw new Exception("Field {$field} harus diisi");
        }
    }

    // Validasi khusus untuk status_keaktifan_id (boleh 0, 1, 2)
    if (!isset($_POST['status_keaktifan_id']) || $_POST['status_keaktifan_id'] === '') {
        throw new Exception("Status keaktifan harus dipilih");
    }

    // Validasi nilai status_keaktifan_id harus 0, 1, atau 2
    $valid_status = ['0', '1', '2'];
    if (!in_array($_POST['status_keaktifan_id'], $valid_status)) {
        throw new Exception("Status keaktifan tidak valid. Harus 0, 1, atau 2");
    }
    
    // Sanitize dan ambil data dari POST
    $sekolah_id = intval($_POST['sekolah_id']);
    $npsn = trim($_POST['npsn']);
    $nama_sekolah = trim($_POST['nama_sekolah']);
    $jenjang_id = intval($_POST['jenjang_id']);
    $tipe_sekolah_id = intval($_POST['tipe_sekolah_id']);
    $status_sekolah_id = intval($_POST['status_sekolah_id']);
    $status_keaktifan_id = trim($_POST['status_keaktifan_id']);
    $kota_id = trim($_POST['kota_id']);


    
    // Data opsional
    $kecamatan = isset($_POST['kecamatan']) ? trim($_POST['kecamatan']) : null;
    $desa_kelurahan = isset($_POST['desa_kelurahan']) ? trim($_POST['desa_kelurahan']) : null;
    $alamat = isset($_POST['alamat']) ? trim($_POST['alamat']) : null;
    $nama_yayasan = isset($_POST['nama_yayasan']) ? trim($_POST['nama_yayasan']) : null;
    $no_akte = isset($_POST['no_akte']) ? trim($_POST['no_akte']) : null;
    $tahun_berdiri = isset($_POST['tahun_berdiri']) ? trim($_POST['tahun_berdiri']) : null;
    
    // Data kontak
    $nama_kepsek = isset($_POST['nama_kepsek']) ? trim($_POST['nama_kepsek']) : null;
    $no_hp_kepsek = isset($_POST['no_hp_kepsek']) ? trim($_POST['no_hp_kepsek']) : null;
    $no_wa_kepsek = isset($_POST['no_wa_kepsek']) ? trim($_POST['no_wa_kepsek']) : null;
    $nama_operator = isset($_POST['nama_operator']) ? trim($_POST['nama_operator']) : null;
    $no_hp_operator = isset($_POST['no_hp_operator']) ? trim($_POST['no_hp_operator']) : null;
    $no_wa_operator = isset($_POST['no_wa_operator']) ? trim($_POST['no_wa_operator']) : null;
    $email = isset($_POST['email']) ? trim($_POST['email']) : null;
    
    // Data fixed
    $rumpun = 'paud';
    $provinsi_id = $_SESSION['provinsi_id']; // Dari session user
    $soft_delete = 1; // Aktif
    
    // Ambil provinsi_id dari session untuk validasi
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Validasi apakah sekolah ada dan milik provinsi user
    $check_query = "SELECT sekolah_id, npsn FROM sekolah 
                    WHERE sekolah_id = ? AND provinsi_id = ? AND rumpun = 'paud' AND soft_delete = 1";
    
    $stmt_check = $conn->prepare($check_query);
    $stmt_check->bind_param("ii", $sekolah_id, $provinsi_id_session);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();
    
    if ($result_check->num_rows == 0) {
        throw new Exception('Data sekolah tidak ditemukan atau Anda tidak memiliki akses');
    }
    
    $existing_data = $result_check->fetch_assoc();
    
    // Validasi NPSN unik (kecuali untuk record yang sedang diedit)
    $check_npsn = "SELECT sekolah_id FROM sekolah 
                   WHERE npsn = ? AND sekolah_id != ? AND soft_delete = 1";
    $stmt_check_npsn = $conn->prepare($check_npsn);
    $stmt_check_npsn->bind_param("si", $npsn, $sekolah_id);
    $stmt_check_npsn->execute();
    $result_check_npsn = $stmt_check_npsn->get_result();
    
    if ($result_check_npsn->num_rows > 0) {
        throw new Exception('NPSN sudah terdaftar dalam sistem');
    }
    
    // Validasi email format jika diisi
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Format email tidak valid');
    }

    // Cek duplikasi NPSN di tabel user jika berubah
    if ($npsn !== $existing_data['npsn']) {
        $check_user_npsn = "SELECT kd_user FROM user WHERE nianpsn = ? AND kd_user != ? AND soft_delete = '1'";
        $stmt_check_user_npsn = $conn->prepare($check_user_npsn);
        $stmt_check_user_npsn->bind_param("ss", $npsn, $sekolah_id);
        $stmt_check_user_npsn->execute();
        $result_check_user_npsn = $stmt_check_user_npsn->get_result();

        if ($result_check_user_npsn->num_rows > 0) {
            throw new Exception('NPSN sudah terdaftar sebagai user, gunakan NPSN yang berbeda');
        }
    }

    // Begin transaction
    $conn->autocommit(false);
    
    // Query update
    $sql = "UPDATE sekolah SET
                npsn = ?, nama_sekolah = ?, jenjang_id = ?, rumpun = ?, alamat = ?,
                tipe_sekolah_id = ?, status_sekolah_id = ?, provinsi_id = ?, kota_id = ?,
                desa_kelurahan = ?, kecamatan = ?, nama_kepsek = ?, no_hp_kepsek = ?, no_wa_kepsek = ?,
                nama_operator = ?, no_hp_operator = ?, no_wa_operator = ?, email = ?, nama_yayasan = ?, no_akte = ?, tahun_berdiri = ?,
                status_keaktifan_id = ?, soft_delete = ?
            WHERE sekolah_id = ? AND provinsi_id = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssissiiisssssssssssssssii",
        $npsn, $nama_sekolah, $jenjang_id, $rumpun, $alamat,
        $tipe_sekolah_id, $status_sekolah_id, $provinsi_id, $kota_id,
        $desa_kelurahan, $kecamatan, $nama_kepsek, $no_hp_kepsek, $no_wa_kepsek,
        $nama_operator, $no_hp_operator, $no_wa_operator, $email, $nama_yayasan, $no_akte, $tahun_berdiri,
        $status_keaktifan_id, $soft_delete, $sekolah_id, $provinsi_id_session
    );
    
    if (!$stmt->execute()) {
        throw new Exception('Gagal mengupdate data sekolah: ' . $stmt->error);
    }

    // Sinkronisasi update ke tabel user (kecuali password dan level)
    $sql_user = "UPDATE user SET
                     kd_user = ?,
                     nianpsn = ?,
                     nm_user = ?,
                     jenjang_id = ?,
                     status_keaktifan_id = ?,
                     provinsi_id = ?,
                     kota_id = ?,
                     username = ?
                 WHERE kd_user = ? AND soft_delete = '1'";

    $stmt_user = $conn->prepare($sql_user);
    $stmt_user->bind_param("sssiiisss",
        $sekolah_id, $npsn, $nama_sekolah, $jenjang_id, $status_keaktifan_id,
        $provinsi_id, $kota_id, $npsn, $sekolah_id
    );

    if (!$stmt_user->execute()) {
        throw new Exception('Gagal sinkronisasi update ke tabel user: ' . $stmt_user->error);
    }

    // Commit transaction jika semua berhasil
    $conn->commit();
    $conn->autocommit(true);
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data sekolah "' . $nama_sekolah . '" berhasil diupdate',
        'data' => [
            'sekolah_id' => $sekolah_id,
            'npsn' => $npsn,
            'nama_sekolah' => $nama_sekolah
        ]
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Rollback transaction jika ada error
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Update Sekolah Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
}
?>
