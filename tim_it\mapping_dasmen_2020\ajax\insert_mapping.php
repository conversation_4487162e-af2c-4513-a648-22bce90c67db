<?php
session_start();
require_once '../../../koneksi.php';

// Cek session dan level
if (!isset($_SESSION['kd_user']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Ambil provinsi_id dari session
$provinsi_id = $_SESSION['provinsi_id'];

if ($_POST) {
    // Baca Variabel Data Form dengan real_escape_string
    $npsn_sekolah = $conn->real_escape_string($_POST['npsn_sekolah']);
    $nia_asesor1 = $conn->real_escape_string($_POST['nia_asesor1']);
    $nia_asesor2 = $conn->real_escape_string($_POST['nia_asesor2']);
    $tgl_mulai_visitasi = $conn->real_escape_string($_POST['tgl_mulai_visitasi']);
    $tgl_akhir_visitasi = $conn->real_escape_string($_POST['tgl_akhir_visitasi']);
    $tahun_akreditasi = $conn->real_escape_string($_POST['tahun_akreditasi']);
    $tahap = intval($_POST['tahap']);
    $model_pembelajaran = $conn->real_escape_string($_POST['model_pembelajaran']);
    $cara_visitasi = $conn->real_escape_string($_POST['cara_visitasi']);
    $no_surat = $conn->real_escape_string($_POST['no_surat']);
    $tgl_surat = $conn->real_escape_string($_POST['tgl_surat']);

    try {
        // 1. Validasi dan ambil sekolah_id berdasarkan NPSN
        $sekolah_query = "SELECT sekolah_id, nama_sekolah FROM sekolah 
                          WHERE npsn = '$npsn_sekolah' 
                            AND provinsi_id = $provinsi_id 
                            AND rumpun = 'dasmen' 
                            AND soft_delete = '1'";
        $sekolah_result = $conn->query($sekolah_query);
        
        if (!$sekolah_result || $sekolah_result->num_rows === 0) {
            echo json_encode(['success' => false, 'message' => 'NPSN tidak ditemukan atau bukan sekolah dasmen di provinsi Anda']);
            exit;
        }
        
        $sekolah_data = $sekolah_result->fetch_assoc();
        $sekolah_id = $sekolah_data['sekolah_id'];

        // 2. Validasi NIA Asesor 1
        $asesor1_query = "SELECT kd_asesor1, nm_asesor1 FROM asesor_1 
                          WHERE nia1 = '$nia_asesor1' 
                            AND provinsi_id = $provinsi_id 
                            AND soft_delete = '1'";
        $asesor1_result = $conn->query($asesor1_query);
        
        if (!$asesor1_result || $asesor1_result->num_rows === 0) {
            echo json_encode(['success' => false, 'message' => 'NIA Asesor 1 tidak ditemukan di provinsi Anda']);
            exit;
        }
        
        $asesor1_data = $asesor1_result->fetch_assoc();
        $kd_asesor1 = $asesor1_data['kd_asesor1'];

        // 3. Validasi NIA Asesor 2
        $asesor2_query = "SELECT kd_asesor2, nm_asesor2 FROM asesor_2 
                          WHERE nia2 = '$nia_asesor2' 
                            AND provinsi_id = $provinsi_id 
                            AND soft_delete = '1'";
        $asesor2_result = $conn->query($asesor2_query);
        
        if (!$asesor2_result || $asesor2_result->num_rows === 0) {
            echo json_encode(['success' => false, 'message' => 'NIA Asesor 2 tidak ditemukan di provinsi Anda']);
            exit;
        }
        
        $asesor2_data = $asesor2_result->fetch_assoc();
        $kd_asesor2 = $asesor2_data['kd_asesor2'];

        // 4. Validasi tanggal
        if (!empty($tgl_mulai_visitasi) && !empty($tgl_akhir_visitasi)) {
            if (strtotime($tgl_mulai_visitasi) > strtotime($tgl_akhir_visitasi)) {
                echo json_encode(['success' => false, 'message' => 'Tanggal mulai visitasi tidak boleh lebih besar dari tanggal akhir visitasi']);
                exit;
            }
        }

        // 5. Cek duplikasi mapping (sekolah_id + tahun_akreditasi)
        $dup_query = "SELECT id_mapping FROM mapping 
                      WHERE sekolah_id = $sekolah_id 
                        AND tahun_akreditasi = '$tahun_akreditasi'";
        $dup_result = $conn->query($dup_query);
        
        if ($dup_result && $dup_result->num_rows > 0) {
            echo json_encode(['success' => false, 'message' => 'Mapping untuk sekolah dan tahun akreditasi ini sudah ada']);
            exit;
        }

        // 6. Prepare data untuk insert
        $insert_fields = [
            'sekolah_id' => $sekolah_id,
            'kd_asesor1' => "'$kd_asesor1'",
            'kd_asesor2' => "'$kd_asesor2'",
            'tahun_akreditasi' => "'$tahun_akreditasi'",
            'tahap' => $tahap,
            'provinsi_id' => $provinsi_id
        ];

        // Optional fields
        if (!empty($tgl_mulai_visitasi)) {
            $insert_fields['tgl_mulai_visitasi'] = "'$tgl_mulai_visitasi'";
        }
        if (!empty($tgl_akhir_visitasi)) {
            $insert_fields['tgl_akhir_visitasi'] = "'$tgl_akhir_visitasi'";
        }
        if (!empty($model_pembelajaran)) {
            $insert_fields['model_pembelajaran'] = "'$model_pembelajaran'";
        }
        if (!empty($cara_visitasi)) {
            $insert_fields['cara_visitasi'] = "'$cara_visitasi'";
        }
        if (!empty($no_surat)) {
            $insert_fields['no_surat'] = "'$no_surat'";
        }
        if (!empty($tgl_surat)) {
            $insert_fields['tgl_surat'] = "'$tgl_surat'";
        }

        // 7. Build and execute insert query
        $fields = implode(', ', array_keys($insert_fields));
        $values = implode(', ', array_values($insert_fields));
        
        $insert_query = "INSERT INTO mapping ($fields) VALUES ($values)";
        
        if ($conn->query($insert_query)) {
            echo json_encode([
                'success' => true, 
                'message' => 'Data mapping berhasil disimpan'
            ]);
        } else {
            throw new Exception("Insert failed: " . $conn->error);
        }

    } catch (Exception $e) {
        error_log("Insert Mapping Error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}

$conn->close();
?>
