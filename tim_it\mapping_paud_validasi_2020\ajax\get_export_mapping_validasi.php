<?php
/**
 * AJAX handler untuk mengambil semua data mapping validasi PAUD untuk export Excel
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

// Fungsi helper untuk get data asesor
function getAsesorData($conn, $kd_asesor, $table) {
    if (empty($kd_asesor)) {
        return null;
    }

    if ($table === 'asesor_1') {
        $query = "SELECT nia1 as nia, nm_asesor1 as nama, kota_id1 as kota_id FROM asesor_1 WHERE kd_asesor1 = ?";
    } else {
        $query = "SELECT nia2 as nia, nm_asesor2 as nama, kota_id2 as kota_id FROM asesor_2 WHERE kd_asesor2 = ?";
    }

    $stmt = $conn->prepare($query);
    if (!$stmt) return null;

    $stmt->bind_param("s", $kd_asesor);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $asesor = $result->fetch_assoc();

        // Get kota name
        $kota_query = "SELECT nm_kota FROM kab_kota WHERE kota_id = ?";
        $kota_stmt = $conn->prepare($kota_query);
        if ($kota_stmt) {
            $kota_stmt->bind_param("s", $asesor['kota_id']);
            $kota_stmt->execute();
            $kota_result = $kota_stmt->get_result();
            $kota_data = $kota_result->fetch_assoc();
            $asesor['kota'] = $kota_data['nm_kota'] ?? '-';
        } else {
            $asesor['kota'] = '-';
        }

        return $asesor;
    }

    return null;
}

try {
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Query untuk mengambil semua data mapping validasi PAUD untuk export
    // Menggunakan query step by step untuk menghindari error
    $query = "SELECT
                mp.id_mapping,
                mp.sekolah_id,
                mp.kd_asesor1,
                mp.kd_asesor2,
                mp.tahap,
                mp.tahun_akreditasi,
                mp.file_penjelasan_hasil_akreditasi,
                mp.nama_asesor_kpa,
                mp.catatan_penilaian_asesor_kpa,
                mp.nama_asesor_Validasi_a,
                mp.catatan_penilaian_asesor_Validasi_a,
                mp.nama_asesor_Validasi_b,
                mp.catatan_penilaian_asesor_Validasi_b,
                mp.nama_validator,
                mp.nilai_validasi,
                mp.catatan_penilaian_validator,
                mp.pha,
                s.nama_sekolah,
                s.npsn,
                j.nm_jenjang,
                k.nm_kota as nm_kota_sekolah
              FROM mapping_paud_validasi mp
              LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
              LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
              LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
              WHERE mp.provinsi_id = ?
                AND s.rumpun = 'paud'
                AND s.soft_delete = '1'
              ORDER BY s.nama_sekolah ASC";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $provinsi_id_session);
    $stmt->execute();
    $result = $stmt->get_result();
    
    // Siapkan data untuk response
    $data = [];
    while ($row = $result->fetch_assoc()) {
        // Tentukan status file
        $file_status = 'Belum Unggah';
        if (!empty($row['file_penjelasan_hasil_akreditasi']) && trim($row['file_penjelasan_hasil_akreditasi']) !== '') {
            $file_status = 'Sudah Unggah';
        }

        // Get asesor data separately untuk menghindari JOIN yang kompleks
        $asesor1_data = getAsesorData($conn, $row['kd_asesor1'], 'asesor_1');
        $asesor2_data = getAsesorData($conn, $row['kd_asesor2'], 'asesor_2');

        $data[] = [
            'id_mapping' => $row['id_mapping'],
            'npsn' => $row['npsn'],
            'nama_sekolah' => $row['nama_sekolah'],
            'nm_jenjang' => $row['nm_jenjang'],
            'nm_kota_sekolah' => $row['nm_kota_sekolah'],
            'nia1' => $asesor1_data['nia'] ?? '-',
            'nm_asesor1' => $asesor1_data['nama'] ?? '-',
            'nm_kota_asesor1' => $asesor1_data['kota'] ?? '-',
            'nia2' => $asesor2_data['nia'] ?? '-',
            'nm_asesor2' => $asesor2_data['nama'] ?? '-',
            'nm_kota_asesor2' => $asesor2_data['kota'] ?? '-',
            'tahun_akreditasi' => $row['tahun_akreditasi'],
            'tahap' => $row['tahap'],
            'file_status' => $file_status,
            'nama_asesor_kpa' => $row['nama_asesor_kpa'],
            'catatan_penilaian_asesor_kpa' => $row['catatan_penilaian_asesor_kpa'],
            'nama_asesor_Validasi_a' => $row['nama_asesor_Validasi_a'],
            'catatan_penilaian_asesor_Validasi_a' => $row['catatan_penilaian_asesor_Validasi_a'],
            'nama_asesor_Validasi_b' => $row['nama_asesor_Validasi_b'],
            'catatan_penilaian_asesor_Validasi_b' => $row['catatan_penilaian_asesor_Validasi_b'],
            'nama_validator' => $row['nama_validator'],
            'nilai_validasi' => $row['nilai_validasi'],
            'catatan_penilaian_validator' => $row['catatan_penilaian_validator'],
            'pha' => $row['pha']
        ];
    }
    
    // Log export activity
    error_log("Export Mapping Validasi PAUD - User: " . ($_SESSION['nm_user'] ?? 'Unknown') . ", Records: " . count($data));
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data berhasil diambil untuk export',
        'data' => $data,
        'total_records' => count($data)
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Log error
    error_log("Export Data Error: " . $e->getMessage());

    // Response error
    $response = [
        'success' => false,
        'message' => 'Terjadi kesalahan saat mengambil data: ' . $e->getMessage(),
        'data' => [],
        'total_records' => 0
    ];

    echo json_encode($response);
}
?>
