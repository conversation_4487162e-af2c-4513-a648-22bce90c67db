<?php
// Export mapping validasi PAUD untuk Excel
ob_clean();
error_reporting(0);
ini_set('display_errors', 0);

require '../../../koneksi.php';
require '../../../check_session.php';

header('Content-Type: application/json');

// Check if user is Staff IT
if (!isset($_SESSION['level']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['success' => false, 'message' => 'Akses ditolak']);
    exit;
}

try {
    // Simple query first (exact copy dari get_export_simple.php yang berhasil)
    $provinsi_id = $_SESSION['provinsi_id'];

    $query = "SELECT mapping_paud_validasi.*,
                sekolah.npsn,
                sekolah.nama_sekolah,
                jenjang.nm_jenjang,
                kab_kota.nm_kota,
                asesor_1.nia1,
                asesor_1.nm_asesor1,
                asesor_1.no_hp as hp1,
                mapping_paud_validasi_tahun.nama_tahun,
                (SELECT kab_kota.nm_kota from asesor_1 LEFT JOIN kab_kota ON asesor_1.kota_id1=kab_kota.kota_id
                WHERE  mapping_paud_validasi.kd_asesor1=asesor_1.kd_asesor1) as kota1,
                asesor_2.nia2,
                asesor_2.nm_asesor2,
                asesor_2.no_hp as hp2,
                (SELECT kab_kota.nm_kota from asesor_2 LEFT JOIN kab_kota ON asesor_2.kota_id2=kab_kota.kota_id
                WHERE  mapping_paud_validasi.kd_asesor2=asesor_2.kd_asesor2) as kota2
              FROM mapping_paud_validasi
              LEFT JOIN sekolah ON mapping_paud_validasi.sekolah_id=sekolah.sekolah_id
              LEFT JOIN jenjang ON sekolah.jenjang_id=jenjang.jenjang_id
              LEFT JOIN kab_kota ON sekolah.kota_id=kab_kota.kota_id
              LEFT JOIN asesor_1 ON mapping_paud_validasi.kd_asesor1=asesor_1.kd_asesor1
              LEFT JOIN asesor_2 ON mapping_paud_validasi.kd_asesor2=asesor_2.kd_asesor2
              LEFT JOIN mapping_paud_validasi_tahun ON mapping_paud_validasi.tahun_akreditasi=mapping_paud_validasi_tahun.nama_tahun
              WHERE mapping_paud_validasi.tahun_akreditasi = mapping_paud_validasi_tahun.nama_tahun
                AND mapping_paud_validasi.provinsi_id = ?
                AND mapping_paud_validasi_tahun.provinsi_id = ?
                AND sekolah.rumpun = 'paud'
                AND sekolah.soft_delete = '1'
              ORDER BY sekolah.nama_sekolah ASC";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("ii", $provinsi_id, $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $data = [];
    while ($row = $result->fetch_assoc()) {
        // Tentukan status file
        $file_status = 'Belum Unggah';
        if (!empty($row['file_penjelasan_hasil_akreditasi']) && trim($row['file_penjelasan_hasil_akreditasi']) !== '') {
            $file_status = 'Sudah Unggah';
        }

        $data[] = [
            'id_mapping' => $row['id_mapping'],
            'npsn' => $row['npsn'] ?? '-',
            'nama_sekolah' => $row['nama_sekolah'] ?? '-',
            'nm_jenjang' => $row['nm_jenjang'] ?? '-',
            'nm_kota_sekolah' => $row['nm_kota'] ?? '-',
            'nia1' => $row['nia1'] ?? '-',
            'nm_asesor1' => $row['nm_asesor1'] ?? '-',
            'nm_kota_asesor1' => $row['kota1'] ?? '-',
            'nia2' => $row['nia2'] ?? '-',
            'nm_asesor2' => $row['nm_asesor2'] ?? '-',
            'nm_kota_asesor2' => $row['kota2'] ?? '-',
            'tahun_akreditasi' => $row['tahun_akreditasi'] ?? '-',
            'tahap' => $row['tahap'] ?? '-',
            'file_status' => $file_status,
            'nama_asesor_kpa' => $row['nama_asesor_kpa'] ?? '-',
            'catatan_penilaian_asesor_kpa' => $row['catatan_penilaian_asesor_kpa'] ?? '-',
            'nama_asesor_Validasi_a' => $row['nama_asesor_Validasi_a'] ?? '-',
            'catatan_penilaian_asesor_Validasi_a' => $row['catatan_penilaian_asesor_Validasi_a'] ?? '-',
            'nama_asesor_Validasi_b' => $row['nama_asesor_Validasi_b'] ?? '-',
            'catatan_penilaian_asesor_Validasi_b' => $row['catatan_penilaian_asesor_Validasi_b'] ?? '-',
            'nama_validator' => $row['nama_validator'] ?? '-',
            'nilai_validasi' => $row['nilai_validasi'] ?? '-',
            'catatan_penilaian_validator' => $row['catatan_penilaian_validator'] ?? '-',
            'pha' => $row['pha'] ?? '-'
        ];
    }

    echo json_encode([
        'success' => true,
        'message' => 'Data berhasil diambil untuk export',
        'data' => $data,
        'total_records' => count($data)
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
