<?php
/**
 * AJAX handler untuk mengambil semua data sekolah untuk export Excel
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Query untuk mengambil semua data sekolah untuk export
    $query = "SELECT s.sekolah_id, s.npsn, s.nama_sekolah, s.alamat, s.kecamatan,
                     s.desa_kelurahan, s.nama_kepsek, s.no_hp_kepsek, s.no_wa_kepsek,
                     s.nama_operator, s.no_hp_operator, s.no_wa_operator, s.email, s.nama_<PERSON><PERSON><PERSON>, s.no_akte,
                     s.ta<PERSON>_be<PERSON>, s.status_keaktifan_id,
                     j.nm_jenjang, k.nm_kota, ts.nm_tipe_sekolah, ss.nm_status_sekolah, sk.nm_status
              FROM sekolah s
              LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
              LEFT JOIN kab_kota k ON s.kota_id = k.kota_id
              LEFT JOIN tipe_sekolah ts ON s.tipe_sekolah_id = ts.tipe_sekolah_id
              LEFT JOIN status_sekolah ss ON s.status_sekolah_id = ss.status_sekolah_id
              LEFT JOIN status_keaktifan sk ON s.status_keaktifan_id = sk.status_keaktifan_id
              WHERE s.soft_delete = 1 AND s.rumpun = 'kesetaraan' AND s.provinsi_id = ?
              ORDER BY s.nama_sekolah ASC";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $provinsi_id_session);
    $stmt->execute();
    $result = $stmt->get_result();
    
    // Siapkan data untuk response
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = [
            'sekolah_id' => $row['sekolah_id'],
            'npsn' => $row['npsn'],
            'nama_sekolah' => $row['nama_sekolah'],
            'nm_jenjang' => $row['nm_jenjang'],
            'nm_kota' => $row['nm_kota'],
            'kecamatan' => $row['kecamatan'],
            'desa_kelurahan' => $row['desa_kelurahan'],
            'alamat' => $row['alamat'],
            'nm_tipe_sekolah' => $row['nm_tipe_sekolah'],
            'nm_status_sekolah' => $row['nm_status_sekolah'],
            'nama_kepsek' => $row['nama_kepsek'],
            'no_hp_kepsek' => $row['no_hp_kepsek'],
            'no_wa_kepsek' => $row['no_wa_kepsek'],
            'nama_operator' => $row['nama_operator'],
            'no_hp_operator' => $row['no_hp_operator'],
            'no_wa_operator' => $row['no_wa_operator'],
            'email' => $row['email'],
            'nama_yayasan' => $row['nama_yayasan'],
            'no_akte' => $row['no_akte'],
            'tahun_berdiri' => $row['tahun_berdiri'],
            'status_keaktifan_id' => $row['status_keaktifan_id'],
            'nm_status' => $row['nm_status']
        ];
    }
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data berhasil diambil untuk export',
        'data' => $data,
        'total_records' => count($data)
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Log error
    error_log("Export Data Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => 'Terjadi kesalahan saat mengambil data: ' . $e->getMessage(),
        'data' => [],
        'total_records' => 0
    ];
    
    echo json_encode($response);
}
?>
