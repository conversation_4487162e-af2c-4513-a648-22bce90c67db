<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input ID mapping
    if (!isset($_POST['id_mapping']) || empty(trim($_POST['id_mapping']))) {
        throw new Exception('ID mapping harus diisi');
    }
    
    $id_mapping = intval($_POST['id_mapping']);
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Validasi ID mapping
    if ($id_mapping <= 0) {
        throw new Exception('ID mapping tidak valid');
    }
    
    // Sanitasi input (semua field optional)
    $tgl_mulai_visitasi = isset($_POST['tgl_mulai_visitasi']) && !empty($_POST['tgl_mulai_visitasi']) 
                         ? $conn->real_escape_string($_POST['tgl_mulai_visitasi']) 
                         : null;
    $tgl_akhir_visitasi = isset($_POST['tgl_akhir_visitasi']) && !empty($_POST['tgl_akhir_visitasi']) 
                         ? $conn->real_escape_string($_POST['tgl_akhir_visitasi']) 
                         : null;
    $no_surat = isset($_POST['no_surat']) && !empty(trim($_POST['no_surat'])) 
               ? $conn->real_escape_string(trim($_POST['no_surat'])) 
               : null;
    $tgl_surat = isset($_POST['tgl_surat']) && !empty($_POST['tgl_surat']) 
                ? $conn->real_escape_string($_POST['tgl_surat']) 
                : null;
    
    // Cek apakah mapping exists dan milik provinsi yang benar
    $check_query = "SELECT id_mapping, sekolah_id, tahun_akreditasi FROM mapping_paud_visitasi 
                    WHERE id_mapping = ? AND provinsi_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("ii", $id_mapping, $provinsi_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows === 0) {
        throw new Exception('Data mapping tidak ditemukan atau Anda tidak memiliki akses');
    }
    
    $mapping_info = $check_result->fetch_assoc();
    
    // Begin transaction
    $conn->autocommit(false);
    
    // Update tanggal kegiatan
    $update_query = "UPDATE mapping_paud_visitasi 
                     SET tgl_mulai_visitasi = ?, 
                         tgl_akhir_visitasi = ?, 
                         no_surat = ?, 
                         tgl_surat = ?
                     WHERE id_mapping = ? AND provinsi_id = ?";
    
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("ssssii", 
        $tgl_mulai_visitasi, $tgl_akhir_visitasi, $no_surat, $tgl_surat,
        $id_mapping, $provinsi_id
    );
    
    if (!$update_stmt->execute()) {
        throw new Exception('Gagal mengupdate tanggal kegiatan: ' . $conn->error);
    }
    
    // Check if any rows were affected
    if ($conn->affected_rows === 0) {
        // No changes made, but not an error
        error_log("Update Tanggal Kegiatan - No changes: ID $id_mapping, User: " . $_SESSION['nm_user']);
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Get updated data untuk response
    $select_query = "SELECT tgl_mulai_visitasi, tgl_akhir_visitasi, no_surat, tgl_surat 
                     FROM mapping_paud_visitasi 
                     WHERE id_mapping = ? AND provinsi_id = ?";
    $select_stmt = $conn->prepare($select_query);
    $select_stmt->bind_param("ii", $id_mapping, $provinsi_id);
    $select_stmt->execute();
    $select_result = $select_stmt->get_result();
    $updated_data = $select_result->fetch_assoc();
    
    // Log successful update
    error_log("Update Tanggal Kegiatan Success - ID: $id_mapping, " .
              "Mulai: " . ($tgl_mulai_visitasi ?: 'NULL') . ", " .
              "Akhir: " . ($tgl_akhir_visitasi ?: 'NULL') . ", " .
              "No Surat: " . ($no_surat ?: 'NULL') . ", " .
              "Tgl Surat: " . ($tgl_surat ?: 'NULL') . ", " .
              "User: " . $_SESSION['nm_user']);
    
    echo json_encode([
        'success' => true,
        'message' => 'Tanggal kegiatan berhasil diupdate',
        'data' => $updated_data
    ]);
    
} catch (Exception $e) {
    // Rollback transaction
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Update Tanggal Kegiatan Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
