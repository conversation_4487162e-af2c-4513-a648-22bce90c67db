<?php
session_start();
require_once '../../../koneksi.php';

// Cek session dan level
if (!isset($_SESSION['kd_user']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Ambil provinsi_id dari session
$provinsi_id = $_SESSION['provinsi_id'];

if ($_POST) {
    // Baca Variabel Data Form dengan real_escape_string
    $sk_akreditasi_id = intval($_POST['sk_akreditasi_id']);
    $no_sk_akreditasi = $conn->real_escape_string($_POST['no_sk_akreditasi']);
    $tgl_sk_akreditasi = $conn->real_escape_string($_POST['tgl_sk_akreditasi']);
    $tentang = $conn->real_escape_string($_POST['tentang']);
    $tahun_akreditasi = $conn->real_escape_string($_POST['tahun_akreditasi']);
    $nm_lembaga = $conn->real_escape_string($_POST['nm_lembaga']);
    
    // Cek apakah SK ada dan milik provinsi yang sama
    $check_query = "SELECT nama_file FROM e_arsip_sk_akreditasi 
                    WHERE sk_akreditasi_id = $sk_akreditasi_id 
                      AND provinsi_id = $provinsi_id";
    $check_result = $conn->query($check_query);
    
    if (!$check_result || $check_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'SK tidak ditemukan atau tidak memiliki akses']);
        exit;
    }
    
    $current_data = $check_result->fetch_assoc();
    $current_file = $current_data['nama_file'];
    
    // Cek apakah ada file baru yang diupload
    $file_update_query = "";
    if (isset($_FILES['nama_file']) && $_FILES['nama_file']['error'] === UPLOAD_ERR_OK) {
        $nama_file = $conn->real_escape_string($_FILES['nama_file']['name']);
        
        // Cek duplikasi nama file (kecuali file yang sedang diedit)
        $dup_query = "SELECT * FROM e_arsip_sk_akreditasi 
                      WHERE nama_file='$nama_file' 
                        AND sk_akreditasi_id != $sk_akreditasi_id";
        $dup_result = $conn->query($dup_query);
        
        if ($dup_result->num_rows > 0) {
            echo json_encode(['success' => false, 'message' => 'Nama file SK sudah digunakan, silakan upload dengan nama yang berbeda']);
            exit;
        }
        
        // Upload file baru
        $upload_path = '../../../../simak/files/sk_akreditasi/' . $_FILES['nama_file']['name'];
        if (move_uploaded_file($_FILES['nama_file']['tmp_name'], $upload_path)) {
            // Hapus file lama jika ada
            $old_file_path = '../../../../simak/files/sk_akreditasi/' . $current_file;
            if (file_exists($old_file_path)) {
                unlink($old_file_path);
            }
            
            $file_update_query = ", nama_file = '$nama_file'";
        } else {
            echo json_encode(['success' => false, 'message' => 'Gagal mengupload file baru']);
            exit;
        }
    }
    
    // Update data di database
    $update_query = "UPDATE e_arsip_sk_akreditasi 
                     SET no_sk_akreditasi = '$no_sk_akreditasi',
                         tgl_sk_akreditasi = '$tgl_sk_akreditasi',
                         tentang = '$tentang',
                         tahun_akreditasi = '$tahun_akreditasi',
                         nm_lembaga = '$nm_lembaga'
                         $file_update_query
                     WHERE sk_akreditasi_id = $sk_akreditasi_id 
                       AND provinsi_id = $provinsi_id";
    
    $hasil_update = $conn->query($update_query);
    
    if ($hasil_update) {
        echo json_encode(['success' => true, 'message' => 'SK Akreditasi berhasil diupdate']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Data gagal diupdate: ' . $conn->error]);
    }
}

$conn->close();
?>
