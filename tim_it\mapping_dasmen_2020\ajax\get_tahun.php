<?php
session_start();
require_once '../../../koneksi.php';

// Cek session dan level
if (!isset($_SESSION['kd_user']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Ambil provinsi_id dari session
$provinsi_id = $_SESSION['provinsi_id'];

try {
    // Cek koneksi database
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Ambil data tahun akreditasi berdasarkan provinsi
    $query = "SELECT id_mapping_tahun, nama_tahun, provinsi_id
              FROM mapping_tahun 
              WHERE provinsi_id = $provinsi_id";
    
    $result = $conn->query($query);

    if (!$result) {
        throw new Exception("Query failed: " . $conn->error);
    }

    if ($result->num_rows === 0) {
        // Jika belum ada data, buat record baru dengan tahun default
        $current_year = date('Y');
        $insert_query = "INSERT INTO mapping_tahun (nama_tahun, provinsi_id) 
                         VALUES ($current_year, $provinsi_id)";
        
        if ($conn->query($insert_query)) {
            echo json_encode([
                'success' => true,
                'data' => [
                    'id_mapping_tahun' => $conn->insert_id,
                    'nama_tahun' => $current_year,
                    'provinsi_id' => $provinsi_id
                ]
            ]);
        } else {
            throw new Exception("Failed to create default tahun: " . $conn->error);
        }
    } else {
        $data = $result->fetch_assoc();
        echo json_encode([
            'success' => true,
            'data' => $data
        ]);
    }

} catch (Exception $e) {
    error_log("Get Tahun Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Terjadi kesalahan: ' . $e->getMessage()]);
}

$conn->close();
?>
