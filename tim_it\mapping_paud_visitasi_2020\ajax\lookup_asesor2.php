<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input NIA
    if (!isset($_POST['nia']) || empty(trim($_POST['nia']))) {
        throw new Exception('NIA harus diisi');
    }
    
    $nia = $conn->real_escape_string(trim($_POST['nia']));
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Query lookup asesor_2 dengan rumpun yang sesuai
    $query = "SELECT a2.id_asesor2, a2.kd_asesor2, a2.nia2, a2.nm_asesor2,
                     a2.ktp, a2.unit_kerja, a2.kota_id2, a2.provinsi_id,
                     a2.no_sertifikat, a2.no_hp, a2.no_wa, a2.tempat_lahir,
                     a2.tgl_lahir, a2.jabatan, a2.jabatan_struktural, a2.pendidikan,
                     a2.jenjang_id, a2.rumpun, a2.grade, a2.jk,
                     a2.alamat_kantor, a2.alamat_rumah, a2.email,
                     a2.thn_terbit_sertifikat, a2.kegiatan, a2.status_keaktifan_id,
                     kk.nm_kota
              FROM asesor_2 a2
              LEFT JOIN kab_kota kk ON a2.kota_id2 = kk.kota_id
              WHERE a2.nia2 = ?
                AND a2.provinsi_id = ?
                AND a2.soft_delete = '1'
                AND a2.status_keaktifan_id = '1'
              LIMIT 1";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("si", $nia, $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $asesor = $result->fetch_assoc();
        
        echo json_encode([
            'success' => true,
            'data' => $asesor,
            'message' => 'Asesor B ditemukan'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'data' => null,
            'message' => 'Asesor dengan NIA tersebut tidak ditemukan atau tidak aktif'
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'data' => null,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
