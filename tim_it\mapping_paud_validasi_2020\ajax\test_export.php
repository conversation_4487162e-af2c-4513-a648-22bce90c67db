<?php
// Test file untuk debug export mapping validasi
ob_clean();
error_reporting(E_ALL);
ini_set('display_errors', 1);

require '../../../koneksi.php';
require '../../../check_session.php';

echo "=== TEST EXPORT MAPPING VALIDASI ===\n";
echo "Session check: ";
if (isset($_SESSION['level'])) {
    echo "OK - Level: " . $_SESSION['level'] . "\n";
    echo "Provinsi ID: " . ($_SESSION['provinsi_id'] ?? 'Not set') . "\n";
} else {
    echo "FAILED - No session\n";
    exit;
}

echo "\nDatabase connection: ";
if ($conn) {
    echo "OK\n";
} else {
    echo "FAILED\n";
    exit;
}

// Test query sederhana
echo "\nTesting simple query...\n";
$test_query = "SELECT COUNT(*) as total FROM mapping_paud_validasi WHERE provinsi_id = ?";
$stmt = $conn->prepare($test_query);
$stmt->bind_param("i", $_SESSION['provinsi_id']);
$stmt->execute();
$result = $stmt->get_result();
$count = $result->fetch_assoc();
echo "Total mapping records: " . $count['total'] . "\n";

// Test query dengan JOIN
echo "\nTesting JOIN query...\n";
$join_query = "SELECT COUNT(*) as total 
               FROM mapping_paud_validasi mp
               LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
               LEFT JOIN mapping_paud_validasi_tahun mpvt ON mp.tahun_akreditasi = mpvt.nama_tahun
               WHERE mp.provinsi_id = ?
                 AND mp.tahun_akreditasi = mpvt.nama_tahun
                 AND mpvt.provinsi_id = ?
                 AND s.rumpun = 'paud'
                 AND s.soft_delete = '1'";
$stmt2 = $conn->prepare($join_query);
$stmt2->bind_param("ii", $_SESSION['provinsi_id'], $_SESSION['provinsi_id']);
$stmt2->execute();
$result2 = $stmt2->get_result();
$count2 = $result2->fetch_assoc();
echo "Filtered records with JOIN: " . $count2['total'] . "\n";

// Test sample data
echo "\nTesting sample data...\n";
$sample_query = "SELECT 
                    mp.id_mapping,
                    s.nama_sekolah,
                    s.npsn,
                    j.nm_jenjang,
                    mp.tahun_akreditasi
                 FROM mapping_paud_validasi mp
                 LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
                 LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                 LEFT JOIN mapping_paud_validasi_tahun mpvt ON mp.tahun_akreditasi = mpvt.nama_tahun
                 WHERE mp.provinsi_id = ?
                   AND mp.tahun_akreditasi = mpvt.nama_tahun
                   AND mpvt.provinsi_id = ?
                   AND s.rumpun = 'paud'
                   AND s.soft_delete = '1'
                 LIMIT 3";
$stmt3 = $conn->prepare($sample_query);
$stmt3->bind_param("ii", $_SESSION['provinsi_id'], $_SESSION['provinsi_id']);
$stmt3->execute();
$result3 = $stmt3->get_result();

if ($result3->num_rows > 0) {
    echo "Sample records:\n";
    while ($row = $result3->fetch_assoc()) {
        echo "  ID: " . $row['id_mapping'] . 
             ", School: " . $row['nama_sekolah'] . 
             ", NPSN: " . $row['npsn'] . 
             ", Year: " . $row['tahun_akreditasi'] . "\n";
    }
} else {
    echo "No sample records found\n";
}

echo "\n=== TEST COMPLETED ===\n";
?>
