<?php
require_once '../../koneksi.php'; 

// Include session checker dan require level Staff IT
require_once '../../check_session.php';
requireLevel('Staff IT', '../../login.php');

// Include header
include '../header.php';
?>

<!-- Include navbar -->
<?php include '../navbar.php'; ?>

<!-- Include sidebar -->
<?php include '../sidebar.php'; ?>

<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Data Asesor</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../dashboard/dashboard.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="#">Data Master</a></li>
                        <li class="breadcrumb-item active">Data Asesor</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Alert container -->
            <div id="alert-container"></div>
            
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-user-check"></i> Daftar Asesor
                            </h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-success btn-sm mr-2" id="btn-export-excel">
                                    <i class="fas fa-file-excel"></i> Export Excel
                                </button>
                                <button type="button" class="btn btn-primary btn-sm" id="btn-add">
                                    <i class="fas fa-plus"></i> Tambah Asesor
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="table-asesor" class="table table-bordered table-striped table-hover" width="100%">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th width="5%">NO</th>
                                            <th width="12%">NIA</th>
                                            <th width="20%">NAMA ASESOR</th>
                                            <th width="10%">JENIS KELAMIN</th>
                                            <th width="15%">KAB/KOTA</th>
                                            <th width="18%">UNIT KERJA</th>
                                            <th width="8%">RUMPUN</th>
                                            <th width="12%">STATUS KEAKTIFAN</th>
                                            <th width="15%">AKSI</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data akan dimuat via AJAX -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- /.content -->
</div>
<!-- /.content-wrapper -->

<!-- Modal Detail Asesor -->
<div class="modal fade" id="modal-detail" tabindex="-1" role="dialog" aria-labelledby="modal-detail-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info">
                <h5 class="modal-title" id="modal-detail-label">
                    <i class="fas fa-info-circle"></i> Detail Asesor
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="modal-detail-content">
                    <!-- Konten detail akan diisi via AJAX -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah Data Asesor -->
<div class="modal fade" id="modal-tambah" tabindex="-1" role="dialog" aria-labelledby="modal-tambah-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success">
                <h5 class="modal-title" id="modal-tambah-label">
                    <i class="fas fa-plus-circle"></i> Tambah Data Asesor
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="form-tambah-asesor" novalidate>
                <div class="modal-body">
                    <!-- Alert container untuk notifikasi inline -->
                    <div id="alert-container-modal"></div>

                    <!-- Hidden field untuk kd_asesor -->
                    <input type="hidden" name="kd_asesor" id="kd_asesor" value="" />

                    <div class="row">
                        <!-- Kolom Kiri -->
                        <div class="col-md-6">
                            <h6 class="text-primary"><i class="fas fa-user"></i> Data Pribadi</h6>

                            <div class="form-group">
                                <label for="nia">NIA <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nia" name="nia" required maxlength="20">
                            </div>
                            
                            <div class="form-group">
                                <label for="nm_asesor">Nama Asesor <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nm_asesor" name="nm_asesor" required maxlength="100">
                            </div>
                            
                            <div class="form-group">
                                <label for="jk">Jenis Kelamin <span class="text-danger">*</span></label>
                                <select class="form-control" id="jk" name="jk" required>
                                    <option value="">-- Pilih Jenis Kelamin --</option>
                                    <option value="Pria">Pria</option>
                                    <option value="Wanita">Wanita</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="kota_id">Kabupaten/Kota <span class="text-danger">*</span></label>
                                <select class="form-control" id="kota_id" name="kota_id" required>
                                    <option value="">-- Pilih Kabupaten/Kota --</option>
                                    <!-- Options akan dimuat via AJAX -->
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="rumpun">Rumpun <span class="text-danger">*</span></label>
                                <select class="form-control" id="rumpun" name="rumpun" required>
                                    <option value="">-- Pilih Rumpun --</option>
                                    <option value="dasmen">Dasmen</option>
                                    <option value="paud">PAUD</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="jenjang_id">Jenjang</label>
                                <select class="form-control" id="jenjang_id" name="jenjang_id">
                                    <option value="">-- Pilih Jenjang --</option>
                                    <!-- Options akan dimuat via AJAX -->
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="ktp">No. KTP</label>
                                <input type="text" class="form-control" id="ktp" name="ktp" maxlength="20">
                            </div>
                            
                            <div class="form-group">
                                <label for="tempat_lahir">Tempat Lahir</label>
                                <input type="text" class="form-control" id="tempat_lahir" name="tempat_lahir" maxlength="30">
                            </div>
                            
                            <div class="form-group">
                                <label for="tgl_lahir">Tanggal Lahir</label>
                                <input type="date" class="form-control" id="tgl_lahir" name="tgl_lahir">
                            </div>
                        </div>
                        
                        <!-- Kolom Kanan -->
                        <div class="col-md-6">
                            <h6 class="text-primary"><i class="fas fa-briefcase"></i> Data Pekerjaan</h6>
                            
                            <div class="form-group">
                                <label for="unit_kerja">Unit Kerja</label>
                                <textarea class="form-control" id="unit_kerja" name="unit_kerja" rows="2" maxlength="300"></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="jabatan">Jabatan</label>
                                <input type="text" class="form-control" id="jabatan" name="jabatan" maxlength="50">
                            </div>
                            
                            <div class="form-group">
                                <label for="jabatan_struktural">Jabatan Struktural</label>
                                <input type="text" class="form-control" id="jabatan_struktural" name="jabatan_struktural" maxlength="20">
                            </div>
                            
                            <div class="form-group">
                                <label for="pendidikan">Pendidikan</label>
                                <input type="text" class="form-control" id="pendidikan" name="pendidikan" maxlength="15">
                            </div>
                            
                            <div class="form-group">
                                <label for="grade">Grade</label>
                                <select class="form-control" id="grade" name="grade">
                                    <option value="">-- Pilih Grade --</option>
                                    <option value="A">A</option>
                                    <option value="B">B</option>
                                    <option value="C">C</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="no_sertifikat">No. Sertifikat</label>
                                <input type="text" class="form-control" id="no_sertifikat" name="no_sertifikat" maxlength="30">
                            </div>
                            
                            <div class="form-group">
                                <label for="thn_terbit_sertifikat">Tahun Terbit Sertifikat</label>
                                <input type="date" class="form-control" id="thn_terbit_sertifikat" name="thn_terbit_sertifikat">
                            </div>
                            
                            <div class="form-group">
                                <label for="status_keaktifan_id">Status Keaktifan <span class="text-danger">*</span></label>
                                <select class="form-control" id="status_keaktifan_id" name="status_keaktifan_id" required>
                                    <option value="">-- Pilih Status --</option>
                                    <option value="1">Aktif</option>
                                    <option value="0">Tidak Aktif</option>
                                    <option value="2">Tidak Diketahui</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Baris Bawah - Kontak & Alamat -->
                    <div class="row">
                        <div class="col-md-12">
                            <h6 class="text-primary"><i class="fas fa-address-book"></i> Kontak & Alamat</h6>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="no_hp">No. HP</label>
                                <input type="text" class="form-control" id="no_hp" name="no_hp" maxlength="50">
                            </div>
                            
                            <div class="form-group">
                                <label for="no_wa">No. WhatsApp</label>
                                <input type="text" class="form-control" id="no_wa" name="no_wa" maxlength="50">
                            </div>
                            
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" class="form-control" id="email" name="email" maxlength="50">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="alamat_kantor">Alamat Kantor</label>
                                <textarea class="form-control" id="alamat_kantor" name="alamat_kantor" rows="2" maxlength="300"></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="alamat_rumah">Alamat Rumah</label>
                                <textarea class="form-control" id="alamat_rumah" name="alamat_rumah" rows="2" maxlength="300"></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> Batal
                    </button>
                    <button type="submit" class="btn btn-success" id="btn-simpan">
                        <i class="fas fa-save"></i> Simpan Data
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Edit Data Asesor (Konten di-generate via AJAX) -->
<div class="modal fade" id="modal-edit" tabindex="-1" role="dialog" aria-labelledby="modal-edit-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <!-- Konten akan diisi via AJAX dari get_edit_form.php -->
        </div>
    </div>
</div>

<!-- Modal Konfirmasi Hapus -->
<div class="modal fade" id="modal-konfirmasi-hapus" tabindex="-1" role="dialog" aria-labelledby="modal-konfirmasi-hapus-label" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger">
                <h5 class="modal-title text-white" id="modal-konfirmasi-hapus-label">
                    <i class="fas fa-exclamation-triangle"></i> Konfirmasi Hapus Data
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h5>Apakah Anda yakin ingin menghapus asesor ini?</h5>
                <p class="text-muted">
                    <strong id="nama-asesor-hapus"></strong><br>
                    <span id="nia-asesor-hapus"></span><br>
                    Data yang sudah dihapus tidak dapat dikembalikan!
                </p>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Batal
                </button>
                <button type="button" class="btn btn-danger" id="btn-konfirmasi-hapus">
                    <i class="fas fa-trash"></i> Ya, Hapus!
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Notifikasi Sukses -->
<div class="modal fade" id="modal-notifikasi-sukses" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                <h5 class="text-success">Berhasil!</h5>
                <p id="notifikasi-sukses-message"></p>
                <button type="button" class="btn btn-success" data-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Notifikasi Error -->
<div class="modal fade" id="modal-notifikasi-error" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <i class="fas fa-times-circle fa-3x text-danger mb-3"></i>
                <h5 class="text-danger">Error!</h5>
                <p id="notifikasi-error-message"></p>
                <button type="button" class="btn btn-danger" data-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>

<!-- Include footer -->
<?php include '../footer.php'; ?>

<!-- Custom CSS untuk modal detail -->
<style>
.modal-detail-row {
    display: flex;
    margin-bottom: 8px;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.modal-detail-label {
    font-weight: bold;
    min-width: 150px;
    color: #495057;
}

.modal-detail-value {
    flex: 1;
    margin-left: 10px;
    color: #6c757d;
}

.badge-pink {
    color: #fff;
    background-color: #e83e8c;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}
</style>

<!-- JavaScript -->
<script src="js/asesor.js"></script>
