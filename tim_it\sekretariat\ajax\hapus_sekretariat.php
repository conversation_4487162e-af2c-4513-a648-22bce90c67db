<?php
/**
 * AJAX handler untuk menghapus data sekretariat (soft delete) dengan sinkronisasi ke tabel user
 */

// Include koneksi database
require_once '../../../koneksi.php';

// Include session checker
require_once '../../../check_session.php';
requireLevel('Staff IT', '../../../login.php');

// Set header JSON
header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi input required
    if (!isset($_POST['id_sekretariat']) || empty($_POST['id_sekretariat'])) {
        throw new Exception('ID Sekretariat tidak valid');
    }
    
    $id_sekretariat = intval($_POST['id_sekretariat']);
    $nm_staff = isset($_POST['nm_staff']) ? trim($_POST['nm_staff']) : '';
    $kd_sekretariat = isset($_POST['kd_sekretariat']) ? trim($_POST['kd_sekretariat']) : '';
    
    // Ambil provinsi_id dari session user
    $provinsi_id_session = $_SESSION['provinsi_id'];
    
    // Validasi apakah sekretariat ada dan milik provinsi user
    $check_query = "SELECT id_sekretariat, kd_sekretariat, nm_staff, soft_delete 
                    FROM sekretariat 
                    WHERE id_sekretariat = ? AND provinsi_id = ?";
    
    $stmt_check = $conn->prepare($check_query);
    $stmt_check->bind_param("ii", $id_sekretariat, $provinsi_id_session);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();
    
    if ($result_check->num_rows == 0) {
        throw new Exception('Data sekretariat tidak ditemukan atau Anda tidak memiliki akses');
    }
    
    $sekretariat_data = $result_check->fetch_assoc();
    
    // Cek apakah sudah dihapus sebelumnya
    if ($sekretariat_data['soft_delete'] == '0') {
        throw new Exception('Data sekretariat sudah dihapus sebelumnya');
    }
    
    // Begin transaction
    $conn->autocommit(false);
    
    // 1. Update soft_delete menjadi '0' (hapus) di tabel sekretariat
    $delete_sekretariat_query = "UPDATE sekretariat 
                                 SET soft_delete = '0' 
                                 WHERE id_sekretariat = ? AND provinsi_id = ?";
    
    $stmt_delete_sekretariat = $conn->prepare($delete_sekretariat_query);
    $stmt_delete_sekretariat->bind_param("ii", $id_sekretariat, $provinsi_id_session);
    
    if (!$stmt_delete_sekretariat->execute()) {
        throw new Exception('Gagal menghapus data sekretariat: ' . $stmt_delete_sekretariat->error);
    }
    
    // Cek apakah ada baris yang terpengaruh
    if ($stmt_delete_sekretariat->affected_rows == 0) {
        throw new Exception('Tidak ada data sekretariat yang dihapus');
    }
    
    // 2. Update soft_delete menjadi '0' (hapus) di tabel user
    $delete_user_query = "UPDATE user 
                          SET soft_delete = '0' 
                          WHERE kd_user = ?";
    
    $stmt_delete_user = $conn->prepare($delete_user_query);
    $stmt_delete_user->bind_param("s", $sekretariat_data['kd_sekretariat']);
    
    if (!$stmt_delete_user->execute()) {
        throw new Exception('Gagal menghapus data user: ' . $stmt_delete_user->error);
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Response sukses
    $response = [
        'success' => true,
        'message' => 'Data sekretariat "' . $sekretariat_data['nm_staff'] . '" berhasil dihapus',
        'data' => [
            'id_sekretariat' => $id_sekretariat,
            'kd_sekretariat' => $sekretariat_data['kd_sekretariat'],
            'nm_staff' => $sekretariat_data['nm_staff'],
            'action' => 'soft_delete'
        ]
    ];
    
    echo json_encode($response);

} catch (Exception $e) {
    // Rollback transaction jika ada error
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Hapus Sekretariat Error: " . $e->getMessage());
    
    // Response error
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    echo json_encode($response);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
