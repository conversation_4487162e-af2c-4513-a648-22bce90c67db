<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Query untuk mengambil tahun akreditasi aktif
    $query = "SELECT nama_tahun FROM mapping_paud_kpa_tahun WHERE provinsi_id = ? ORDER BY id_mapping_tahun DESC LIMIT 1";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $data = $result->fetch_assoc();
        
        echo json_encode([
            'success' => true,
            'data' => $data
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Data tahun akreditasi tidak ditemukan'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Get Tahun KPA Error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
