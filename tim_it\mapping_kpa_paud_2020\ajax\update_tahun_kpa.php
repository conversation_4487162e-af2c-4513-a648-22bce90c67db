<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    // Validasi method POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Method tidak diizinkan');
    }
    
    // Validasi required fields
    if (!isset($_POST['nama_tahun']) || empty(trim($_POST['nama_tahun']))) {
        throw new Exception('Tahun akreditasi harus diisi');
    }
    
    // Sanitasi input
    $nama_tahun = intval($_POST['nama_tahun']);
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Validasi tahun
    if ($nama_tahun < 2000 || $nama_tahun > 2030) {
        throw new Exception('Tahun akreditasi tidak valid');
    }
    
    // Debug: Log values
    error_log("Update Tahun KPA - Tahun: $nama_tahun, Provinsi: $provinsi_id");
    
    // Cek apakah record sudah ada
    $check_query = "SELECT id_mapping_tahun FROM mapping_paud_kpa_tahun WHERE provinsi_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("i", $provinsi_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    // Begin transaction
    $conn->autocommit(false);
    
    if ($check_result->num_rows > 0) {
        // Update existing record
        $update_query = "UPDATE mapping_paud_kpa_tahun SET nama_tahun = ? WHERE provinsi_id = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("ii", $nama_tahun, $provinsi_id);
        
        if (!$update_stmt->execute()) {
            throw new Exception('Gagal mengupdate tahun akreditasi: ' . $conn->error);
        }
        
        $operation = 'update';
    } else {
        // Insert new record
        $insert_query = "INSERT INTO mapping_paud_kpa_tahun (nama_tahun, provinsi_id) VALUES (?, ?)";
        $insert_stmt = $conn->prepare($insert_query);
        $insert_stmt->bind_param("ii", $nama_tahun, $provinsi_id);
        
        if (!$insert_stmt->execute()) {
            throw new Exception('Gagal menyimpan tahun akreditasi: ' . $conn->error);
        }
        
        $operation = 'insert';
    }
    
    // Commit transaction
    $conn->commit();
    $conn->autocommit(true);
    
    // Log successful operation
    error_log("Update Tahun KPA Success - Operation: $operation, Tahun: $nama_tahun, Provinsi: $provinsi_id, User: " . $_SESSION['nm_user']);
    
    echo json_encode([
        'success' => true,
        'message' => 'Tahun akreditasi berhasil disimpan',
        'data' => [
            'nama_tahun' => $nama_tahun,
            'provinsi_id' => $provinsi_id,
            'operation' => $operation
        ]
    ]);
    
} catch (Exception $e) {
    // Rollback transaction
    if (isset($conn)) {
        $conn->rollback();
        $conn->autocommit(true);
    }
    
    // Log error
    error_log("Update Tahun KPA Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
