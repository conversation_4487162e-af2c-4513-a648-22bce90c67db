<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$id_mapping = isset($_POST['id_mapping']) ? intval($_POST['id_mapping']) : 0;
$nia_asesor1 = isset($_POST['nia_asesor1']) ? trim($_POST['nia_asesor1']) : '';
$nia_asesor2 = isset($_POST['nia_asesor2']) ? trim($_POST['nia_asesor2']) : '';

// Validasi input
if ($id_mapping <= 0) {
    echo json_encode(['success' => false, 'message' => 'ID mapping tidak valid']);
    exit;
}

// Validasi minimal salah satu NIA harus diisi
if (empty($nia_asesor1) && empty($nia_asesor2)) {
    echo json_encode(['success' => false, 'message' => 'Minimal salah satu NIA asesor harus diisi']);
    exit;
}

try {
    // Cek apakah mapping exists dan milik provinsi yang sama
    $checkQuery = "SELECT id_mapping FROM mapping WHERE id_mapping = ? AND provinsi_id = ?";
    $checkStmt = $conn->prepare($checkQuery);
    $checkStmt->bind_param("ii", $id_mapping, $_SESSION['provinsi_id']);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();
    
    if ($checkResult->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'Data mapping tidak ditemukan atau tidak memiliki akses']);
        exit;
    }
    
    $responseData = [];
    $updateFields = [];
    $updateValues = [];
    $types = '';
    
    // Proses Asesor 1
    if (!empty($nia_asesor1)) {
        // Cari asesor 1 berdasarkan NIA
        $asesor1Query = "SELECT a1.kd_asesor1, a1.nia1, a1.nm_asesor1, a1.no_hp, k.nm_kota 
                        FROM asesor_1 a1 
                        LEFT JOIN kab_kota k ON a1.kota_id1 = k.kota_id 
                        WHERE a1.nia1 = ? AND a1.provinsi_id = ? AND a1.status_keaktifan_id = '1'";
        $asesor1Stmt = $conn->prepare($asesor1Query);
        $asesor1Stmt->bind_param("si", $nia_asesor1, $_SESSION['provinsi_id']);
        $asesor1Stmt->execute();
        $asesor1Result = $asesor1Stmt->get_result();
        
        if ($asesor1Result->num_rows === 0) {
            echo json_encode(['success' => false, 'message' => 'Asesor 1 dengan NIA ' . $nia_asesor1 . ' tidak ditemukan atau tidak aktif']);
            exit;
        }
        
        $asesor1Data = $asesor1Result->fetch_assoc();
        $updateFields[] = "kd_asesor1 = ?";
        $updateValues[] = $asesor1Data['kd_asesor1'];
        $types .= 's';
        
        $responseData['asesor1'] = $asesor1Data;
    }
    
    // Proses Asesor 2
    if (!empty($nia_asesor2)) {
        // Cari asesor 2 berdasarkan NIA
        $asesor2Query = "SELECT a2.kd_asesor2, a2.nia2, a2.nm_asesor2, a2.no_hp, k.nm_kota 
                        FROM asesor_2 a2 
                        LEFT JOIN kab_kota k ON a2.kota_id2 = k.kota_id 
                        WHERE a2.nia2 = ? AND a2.provinsi_id = ? AND a2.status_keaktifan_id = '1'";
        $asesor2Stmt = $conn->prepare($asesor2Query);
        $asesor2Stmt->bind_param("si", $nia_asesor2, $_SESSION['provinsi_id']);
        $asesor2Stmt->execute();
        $asesor2Result = $asesor2Stmt->get_result();
        
        if ($asesor2Result->num_rows === 0) {
            echo json_encode(['success' => false, 'message' => 'Asesor 2 dengan NIA ' . $nia_asesor2 . ' tidak ditemukan atau tidak aktif']);
            exit;
        }
        
        $asesor2Data = $asesor2Result->fetch_assoc();
        $updateFields[] = "kd_asesor2 = ?";
        $updateValues[] = $asesor2Data['kd_asesor2'];
        $types .= 's';
        
        $responseData['asesor2'] = $asesor2Data;
    }
    
    // Validasi asesor tidak boleh sama
    if (!empty($nia_asesor1) && !empty($nia_asesor2) && $nia_asesor1 === $nia_asesor2) {
        echo json_encode(['success' => false, 'message' => 'Asesor 1 dan Asesor 2 tidak boleh sama']);
        exit;
    }
    
    // Update mapping table
    if (!empty($updateFields)) {
        // Add id_mapping dan provinsi_id untuk WHERE clause
        $updateValues[] = $id_mapping;
        $updateValues[] = $_SESSION['provinsi_id'];
        $types .= 'ii';
        
        $updateQuery = "UPDATE mapping SET " . implode(', ', $updateFields) . " WHERE id_mapping = ? AND provinsi_id = ?";
        
        $updateStmt = $conn->prepare($updateQuery);
        $updateStmt->bind_param($types, ...$updateValues);
        
        if ($updateStmt->execute()) {
            if ($updateStmt->affected_rows > 0) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Data asesor berhasil diupdate',
                    'data' => $responseData
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Tidak ada perubahan data']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Gagal mengupdate data asesor']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Tidak ada data asesor yang valid untuk diupdate']);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
