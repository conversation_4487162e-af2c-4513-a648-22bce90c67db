<?php
// Test file untuk debug get_detail_mapping.php
ob_clean();
error_reporting(E_ALL);
ini_set('display_errors', 1);

require '../../../koneksi.php';
require '../../../check_session.php';

echo "=== TEST GET DETAIL MAPPING ===\n";
echo "Session check: ";
if (isset($_SESSION['level'])) {
    echo "OK - Level: " . $_SESSION['level'] . "\n";
    echo "Provinsi ID: " . ($_SESSION['provinsi_id'] ?? 'Not set') . "\n";
} else {
    echo "FAILED - No session\n";
    exit;
}

echo "\nDatabase connection: ";
if ($conn) {
    echo "OK\n";
} else {
    echo "FAILED\n";
    exit;
}

// Test query sederhana
echo "\nTesting simple query...\n";
$test_query = "SELECT COUNT(*) as total FROM mapping_paud_validasi WHERE provinsi_id = ?";
$stmt = $conn->prepare($test_query);
$stmt->bind_param("i", $_SESSION['provinsi_id']);
$stmt->execute();
$result = $stmt->get_result();
$count = $result->fetch_assoc();
echo "Total mapping records: " . $count['total'] . "\n";

// Test dengan ID mapping tertentu
echo "\nTesting with specific ID...\n";
$test_id = 1; // Ganti dengan ID yang ada
$detail_query = "SELECT
                    mp.id_mapping,
                    s.nama_sekolah,
                    s.npsn,
                    a1.nm_asesor1,
                    a2.nm_asesor2,
                    k1.nm_kota as nm_kota_asesor1,
                    k2.nm_kota as nm_kota_asesor2
                FROM mapping_paud_validasi mp
                LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
                LEFT JOIN asesor_1 a1 ON mp.kd_asesor1 = a1.kd_asesor1
                LEFT JOIN asesor_2 a2 ON mp.kd_asesor2 = a2.kd_asesor2
                LEFT JOIN kab_kota k1 ON a1.kota_id1 = k1.kota_id
                LEFT JOIN kab_kota k2 ON a2.kota_id2 = k2.kota_id
                WHERE mp.id_mapping = ? AND mp.provinsi_id = ?
                LIMIT 1";
$stmt2 = $conn->prepare($detail_query);
$stmt2->bind_param("ii", $test_id, $_SESSION['provinsi_id']);
$stmt2->execute();
$result2 = $stmt2->get_result();

if ($result2->num_rows > 0) {
    $data = $result2->fetch_assoc();
    echo "Found record:\n";
    echo "  ID: " . $data['id_mapping'] . "\n";
    echo "  School: " . $data['nama_sekolah'] . "\n";
    echo "  NPSN: " . $data['npsn'] . "\n";
    echo "  Validator: " . ($data['nm_asesor1'] ?? 'NULL') . "\n";
    echo "  Validator Kota: " . ($data['nm_kota_asesor1'] ?? 'NULL') . "\n";
    echo "  Verifikator: " . ($data['nm_asesor2'] ?? 'NULL') . "\n";
    echo "  Verifikator Kota: " . ($data['nm_kota_asesor2'] ?? 'NULL') . "\n";
} else {
    echo "No record found with ID $test_id\n";

    // Coba cari ID yang ada
    echo "\nLooking for any available ID...\n";
    $find_query = "SELECT id_mapping FROM mapping_paud_validasi WHERE provinsi_id = ? LIMIT 5";
    $stmt3 = $conn->prepare($find_query);
    $stmt3->bind_param("i", $_SESSION['provinsi_id']);
    $stmt3->execute();
    $result3 = $stmt3->get_result();

    if ($result3->num_rows > 0) {
        echo "Available IDs: ";
        while ($row = $result3->fetch_assoc()) {
            echo $row['id_mapping'] . " ";
        }
        echo "\n";
    } else {
        echo "No mapping records found for this province\n";
    }
}

echo "\n=== TEST COMPLETED ===\n";
?>
