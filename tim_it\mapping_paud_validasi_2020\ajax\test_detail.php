<?php
// Test file untuk debug get_detail_mapping.php
ob_clean();
error_reporting(E_ALL);
ini_set('display_errors', 1);

require '../../../koneksi.php';
require '../../../check_session.php';

echo "=== TEST GET DETAIL MAPPING ===\n";
echo "Session check: ";
if (isset($_SESSION['level'])) {
    echo "OK - Level: " . $_SESSION['level'] . "\n";
    echo "Provinsi ID: " . ($_SESSION['provinsi_id'] ?? 'Not set') . "\n";
} else {
    echo "FAILED - No session\n";
    exit;
}

echo "\nDatabase connection: ";
if ($conn) {
    echo "OK\n";
} else {
    echo "FAILED\n";
    exit;
}

// Test query sederhana
echo "\nTesting simple query...\n";
$test_query = "SELECT COUNT(*) as total FROM mapping_paud_validasi WHERE provinsi_id = ?";
$stmt = $conn->prepare($test_query);
$stmt->bind_param("i", $_SESSION['provinsi_id']);
$stmt->execute();
$result = $stmt->get_result();
$count = $result->fetch_assoc();
echo "Total mapping records: " . $count['total'] . "\n";

// Test dengan ID mapping tertentu
echo "\nTesting with specific ID...\n";
$test_id = 1; // Ganti dengan ID yang ada
$detail_query = "SELECT mp.id_mapping, s.nama_sekolah, s.npsn 
                FROM mapping_paud_validasi mp
                LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
                WHERE mp.id_mapping = ? AND mp.provinsi_id = ?
                LIMIT 1";
$stmt2 = $conn->prepare($detail_query);
$stmt2->bind_param("ii", $test_id, $_SESSION['provinsi_id']);
$stmt2->execute();
$result2 = $stmt2->get_result();

if ($result2->num_rows > 0) {
    $data = $result2->fetch_assoc();
    echo "Found record: ID=" . $data['id_mapping'] . ", School=" . $data['nama_sekolah'] . "\n";
} else {
    echo "No record found with ID $test_id\n";
}

echo "\n=== TEST COMPLETED ===\n";
?>
