<?php
require '../../../koneksi.php';
require '../../../check_session.php';
requireLevel('Staff IT');

header('Content-Type: application/json');

try {
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Get tahun aktif dari mapping_paud_visitasi_tahun
    $tahun_query = "SELECT nama_tahun FROM mapping_paud_visitasi_tahun WHERE provinsi_id = ?";
    $tahun_stmt = $conn->prepare($tahun_query);
    $tahun_stmt->bind_param("i", $provinsi_id);
    $tahun_stmt->execute();
    $tahun_result = $tahun_stmt->get_result();
    
    $tahun_aktif = '';
    if ($tahun_result && $tahun_result->num_rows > 0) {
        $tahun_row = $tahun_result->fetch_assoc();
        $tahun_aktif = $tahun_row['nama_tahun'];
    }
    
    // Query comprehensive untuk export dengan 27 kolom data
    $export_query = "SELECT 
                        mp.id_mapping,
                        s.npsn,
                        s.nama_sekolah,
                        j.nm_jenjang,
                        kk_sekolah.nm_kota as nm_kota_sekolah,
                        a1.nia1,
                        a1.nm_asesor1,
                        kk_asesor1.nm_kota as nm_kota_asesor1,
                        a2.nia2,
                        a2.nm_asesor2,
                        kk_asesor2.nm_kota as nm_kota_asesor2,
                        mp.tahun_akreditasi,
                        mp.tahap,
                        mp.tgl_mulai_visitasi,
                        mp.no_surat,
                        mp.tgl_surat,
                        mp.file_pakta_integritas_1,
                        mp.file_pakta_integritas_2,
                        mp.file_berita_acara_visitasi,
                        mp.file_temuan_hasil_visitasi,
                        mp.file_absen_pembuka,
                        mp.file_absen_penutup,
                        mp.file_foto_visitasi,
                        mp.file_laporan_individu_1,
                        mp.file_laporan_individu_2,
                        mp.file_laporan_kelompok,
                        mp.file_penjelasan_hasil_akreditasi
                     FROM mapping_paud_visitasi mp
                     LEFT JOIN sekolah s ON mp.sekolah_id = s.sekolah_id
                     LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
                     LEFT JOIN kab_kota kk_sekolah ON s.kota_id = kk_sekolah.kota_id
                     LEFT JOIN asesor_1 a1 ON mp.kd_asesor1 = a1.kd_asesor1
                     LEFT JOIN kab_kota kk_asesor1 ON a1.kota_id1 = kk_asesor1.kota_id
                     LEFT JOIN asesor_2 a2 ON mp.kd_asesor2 = a2.kd_asesor2
                     LEFT JOIN kab_kota kk_asesor2 ON a2.kota_id2 = kk_asesor2.kota_id
                     LEFT JOIN mapping_paud_visitasi_tahun mpvt ON mp.tahun_akreditasi = mpvt.nama_tahun AND mp.provinsi_id = mpvt.provinsi_id
                     WHERE mp.provinsi_id = ?";
    
    // Add filter berdasarkan tahun aktif jika ada
    if (!empty($tahun_aktif)) {
        $export_query .= " AND mp.tahun_akreditasi = ?";
    }
    
    $export_query .= " ORDER BY s.nama_sekolah ASC";
    
    $export_stmt = $conn->prepare($export_query);
    
    if (!empty($tahun_aktif)) {
        $export_stmt->bind_param("is", $provinsi_id, $tahun_aktif);
    } else {
        $export_stmt->bind_param("i", $provinsi_id);
    }
    
    $export_stmt->execute();
    $export_result = $export_stmt->get_result();
    
    $data = [];
    if ($export_result && $export_result->num_rows > 0) {
        while ($row = $export_result->fetch_assoc()) {
            $data[] = $row;
        }
    }
    
    // Log export activity
    error_log("Export Mapping Visitasi PAUD - Provinsi: $provinsi_id, Tahun: $tahun_aktif, Records: " . count($data) . ", User: " . $_SESSION['nm_user']);
    
    // Response
    echo json_encode([
        'success' => true,
        'data' => $data,
        'tahun_aktif' => $tahun_aktif,
        'total_records' => count($data),
        'message' => 'Data export berhasil dimuat'
    ]);
    
} catch (Exception $e) {
    error_log("Export Mapping Visitasi Error: " . $e->getMessage() . " - User: " . ($_SESSION['nm_user'] ?? 'Unknown'));
    
    echo json_encode([
        'success' => false,
        'message' => 'Terjadi kesalahan saat mengambil data export: ' . $e->getMessage(),
        'data' => [],
        'total_records' => 0
    ]);
}

$conn->close();
?>
