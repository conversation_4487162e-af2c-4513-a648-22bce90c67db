<?php
session_start();
require_once '../../../koneksi.php';

// Cek session dan level
if (!isset($_SESSION['kd_user']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

// Ambil provinsi_id dari session
$provinsi_id = $_SESSION['provinsi_id'];

// DataTables parameters
$draw = isset($_POST['draw']) ? intval($_POST['draw']) : 1;
$start = isset($_POST['start']) ? intval($_POST['start']) : 0;
$length = isset($_POST['length']) ? intval($_POST['length']) : 25;
$search_value = isset($_POST['search']['value']) ? $_POST['search']['value'] : '';

// Column mapping for ordering
$columns = [
    0 => 'sk_akreditasi_id', // NO (not orderable)
    1 => 'tgl_sk_akreditasi',
    2 => 'no_sk_akreditasi', 
    3 => 'tentang',
    4 => 'tahun_akreditasi',
    5 => 'sk_akreditasi_id' // AKSI (not orderable)
];

$order_column_index = isset($_POST['order'][0]['column']) ? intval($_POST['order'][0]['column']) : 1;
$order_column = isset($columns[$order_column_index]) ? $columns[$order_column_index] : 'tgl_sk_akreditasi';
$order_dir = isset($_POST['order'][0]['dir']) ? $_POST['order'][0]['dir'] : 'desc';

try {
    // Cek koneksi database
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
    
    // Base query
    $base_query = "FROM e_arsip_sk_akreditasi 
                   WHERE provinsi_id = $provinsi_id";

    // Add search condition
    $search_query = "";
    if (!empty($search_value)) {
        $search_value = $conn->real_escape_string($search_value);
        $search_query = " AND (no_sk_akreditasi LIKE '%$search_value%' 
                             OR tentang LIKE '%$search_value%'
                             OR tahun_akreditasi LIKE '%$search_value%'
                             OR nm_lembaga LIKE '%$search_value%')";
    }

    // Count total records
    $total_query = "SELECT COUNT(*) as total $base_query";
    $total_result = $conn->query($total_query);
    if (!$total_result) {
        throw new Exception("Total query failed: " . $conn->error);
    }
    $total_records = $total_result->fetch_assoc()['total'];

    // Count filtered records
    $filtered_query = "SELECT COUNT(*) as total $base_query $search_query";
    $filtered_result = $conn->query($filtered_query);
    if (!$filtered_result) {
        throw new Exception("Filtered query failed: " . $conn->error);
    }
    $filtered_records = $filtered_result->fetch_assoc()['total'];

    // Get data with pagination
    $data_query = "SELECT sk_akreditasi_id,
                          no_sk_akreditasi,
                          tgl_sk_akreditasi,
                          tentang,
                          tahun_akreditasi,
                          nama_file,
                          nm_lembaga
                   $base_query 
                   $search_query
                   ORDER BY $order_column $order_dir
                   LIMIT $start, $length";

    $data_result = $conn->query($data_query);
    if (!$data_result) {
        throw new Exception("Data query failed: " . $conn->error);
    }
    
    $data = [];
    if ($data_result->num_rows > 0) {
        while ($row = $data_result->fetch_assoc()) {
            $data[] = [
                'sk_akreditasi_id' => $row['sk_akreditasi_id'],
                'no_sk_akreditasi' => $row['no_sk_akreditasi'],
                'tgl_sk_akreditasi' => $row['tgl_sk_akreditasi'],
                'tentang' => $row['tentang'],
                'tahun_akreditasi' => $row['tahun_akreditasi'],
                'nama_file' => $row['nama_file'],
                'nm_lembaga' => $row['nm_lembaga']
            ];
        }
    }

    // Response for DataTables
    $response = [
        'draw' => $draw,
        'recordsTotal' => $total_records,
        'recordsFiltered' => $filtered_records,
        'data' => $data
    ];

    // Set proper content type
    header('Content-Type: application/json');
    echo json_encode($response);

} catch (Exception $e) {
    error_log("Get SK Error: " . $e->getMessage());
    header('Content-Type: application/json');
    echo json_encode([
        'draw' => $draw,
        'recordsTotal' => 0,
        'recordsFiltered' => 0,
        'data' => [],
        'error' => 'Terjadi kesalahan saat memuat data: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
