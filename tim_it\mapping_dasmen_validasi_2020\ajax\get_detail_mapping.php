<?php
session_start();
require_once '../../../koneksi.php';

// Cek session dan level
if (!isset($_SESSION['kd_user']) || $_SESSION['level'] !== 'Staff IT') {
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

header('Content-Type: application/json');

try {
    // Validasi parameter
    if (!isset($_GET['id']) || empty($_GET['id'])) {
        throw new Exception('ID mapping validasi tidak valid');
    }
    
    $id_mapping_validasi = intval($_GET['id']);
    $provinsi_id = $_SESSION['provinsi_id'];
    
    // Query detail mapping validasi dengan semua data terkait
    $query = "SELECT 
                mv.id_mapping_validasi,
                mv.sekolah_id,
                mv.kd_asesor1,
                mv.kd_asesor2,
                mv.tgl_mulai_validasi,
                mv.tgl_akhir_validasi,
                mv.no_surat_validasi,
                mv.tgl_surat_validasi,
                mv.tahun_akreditasi,
                mv.tahap,
                mv.file_pakta_integritas_1,
                mv.file_pakta_integritas_2,
                mv.file_berita_acara_validasi_1,
                mv.file_berita_acara_validasi_2,
                
                -- Data Sekolah
                s.npsn,
                s.nama_sekolah,
                s.nama_kepsek,
                s.no_hp_kepsek,
                s.no_wa_kepsek,
                
                -- Data Jenjang
                j.nm_jenjang,
                
                -- Data Kab/Kota Sekolah
                kk.nm_kota,
                
                -- Data Asesor 1
                a1.nia1,
                a1.nm_asesor1,
                a1.no_hp as hp_asesor1,
                
                -- Data Asesor 2
                a2.nia2,
                a2.nm_asesor2,
                a2.no_hp as hp_asesor2,
                
                -- Data Kab/Kota Asesor 1
                kk1.nm_kota as kota_asesor1,
                
                -- Data Kab/Kota Asesor 2
                kk2.nm_kota as kota_asesor2
                
              FROM mapping_validasi mv
              LEFT JOIN sekolah s ON mv.sekolah_id = s.sekolah_id
              LEFT JOIN jenjang j ON s.jenjang_id = j.jenjang_id
              LEFT JOIN kab_kota kk ON s.kota_id = kk.kota_id
              LEFT JOIN asesor_1 a1 ON mv.kd_asesor1 = a1.kd_asesor1
              LEFT JOIN asesor_2 a2 ON mv.kd_asesor2 = a2.kd_asesor2
              LEFT JOIN kab_kota kk1 ON a1.kota_id1 = kk1.kota_id
              LEFT JOIN kab_kota kk2 ON a2.kota_id2 = kk2.kota_id
              WHERE mv.id_mapping_validasi = ? AND mv.provinsi_id = ?";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ii", $id_mapping_validasi, $provinsi_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception('Data mapping validasi tidak ditemukan');
    }
    
    $data = $result->fetch_assoc();
    
    // Format tanggal untuk tampilan
    function formatTanggal($tanggal) {
        if (!$tanggal || $tanggal === '0000-00-00') {
            return '-';
        }
        return date('d/m/Y', strtotime($tanggal));
    }
    
    // Format status file upload dengan clickable support
    function formatFileStatus($filename) {
        if (!empty($filename) && $filename !== '') {
            return [
                'status' => 'uploaded',
                'text' => 'Sudah Upload',
                'class' => 'badge badge-success file-preview-link',
                'filename' => $filename,
                'clickable' => true,
                'file_url' => '../../../simak/files/upload_file_hasil_validasi/' . $filename
            ];
        } else {
            return [
                'status' => 'not_uploaded',
                'text' => 'Belum Upload',
                'class' => 'badge badge-danger',
                'filename' => '',
                'clickable' => false,
                'file_url' => ''
            ];
        }
    }
    
    // Prepare response data
    $response = [
        'success' => true,
        'data' => [
            'id_mapping_validasi' => $data['id_mapping_validasi'],
            'sekolah_id' => $data['sekolah_id'],
            'kd_asesor1' => $data['kd_asesor1'],
            'kd_asesor2' => $data['kd_asesor2'],
            'tahun_akreditasi' => $data['tahun_akreditasi'],
            'tahap' => $data['tahap'],
            
            // Data Sekolah
            'npsn' => $data['npsn'] ?: '-',
            'nama_sekolah' => $data['nama_sekolah'] ?: '-',
            'nm_jenjang' => $data['nm_jenjang'] ?: '-',
            'nm_kota' => $data['nm_kota'] ?: '-',
            'nama_kepsek' => $data['nama_kepsek'] ?: '-',
            'no_hp_kepsek' => $data['no_hp_kepsek'] ?: '-',
            'no_wa_kepsek' => $data['no_wa_kepsek'] ?: '-',
            
            // Data Asesor
            'nia1' => $data['nia1'] ?: '-',
            'nm_asesor1' => $data['nm_asesor1'] ?: '-',
            'hp_asesor1' => $data['hp_asesor1'] ?: '-',
            'kota_asesor1' => $data['kota_asesor1'] ?: '-',
            'nia2' => $data['nia2'] ?: '-',
            'nm_asesor2' => $data['nm_asesor2'] ?: '-',
            'hp_asesor2' => $data['hp_asesor2'] ?: '-',
            'kota_asesor2' => $data['kota_asesor2'] ?: '-',
            
            // Pelaksanaan Kegiatan
            'tgl_mulai_validasi' => formatTanggal($data['tgl_mulai_validasi']),
            'tgl_akhir_validasi' => formatTanggal($data['tgl_akhir_validasi']),
            'no_surat_validasi' => $data['no_surat_validasi'] ?: '-',
            'tgl_surat_validasi' => formatTanggal($data['tgl_surat_validasi']),
            
            // Status File Upload
            'file_pakta_integritas_1' => formatFileStatus($data['file_pakta_integritas_1']),
            'file_pakta_integritas_2' => formatFileStatus($data['file_pakta_integritas_2']),
            'file_berita_acara_validasi_1' => formatFileStatus($data['file_berita_acara_validasi_1']),
            'file_berita_acara_validasi_2' => formatFileStatus($data['file_berita_acara_validasi_2'])
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log("Get Detail Mapping Error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
