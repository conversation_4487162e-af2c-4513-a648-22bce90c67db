<!DOCTYPE html>
<html>
<head>
    <title>Test SheetJS Library</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 20px; margin: 10px; }
    </style>
</head>
<body>
    <h1>Test SheetJS Library</h1>
    
    <div id="library-status"></div>
    
    <button onclick="testBasicExport()">Test Basic Excel Export</button>
    <button onclick="testAjaxExport()">Test AJAX Export</button>
    
    <div id="test-results"></div>

    <!-- Include jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Include SheetJS -->
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    
    <script>
    $(document).ready(function() {
        // Check if XLSX library is loaded
        if (typeof XLSX !== 'undefined') {
            $('#library-status').html('<div class="success">✓ SheetJS Library Loaded Successfully</div>');
            console.log('XLSX version:', XLSX.version);
        } else {
            $('#library-status').html('<div class="error">✗ SheetJS Library NOT Loaded</div>');
        }
    });
    
    function testBasicExport() {
        $('#test-results').html('<div class="info">Testing basic Excel export...</div>');
        
        try {
            // Test data
            var testData = [
                ['NO', 'NAME', 'VALUE'],
                [1, 'Test 1', 'Value 1'],
                [2, 'Test 2', 'Value 2']
            ];
            
            // Create workbook
            var wb = XLSX.utils.book_new();
            var ws = XLSX.utils.aoa_to_sheet(testData);
            
            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, "Test Sheet");
            
            // Save file
            XLSX.writeFile(wb, 'test_export.xlsx');
            
            $('#test-results').html('<div class="success">✓ Basic Excel export successful!</div>');
            
        } catch (error) {
            $('#test-results').html('<div class="error">✗ Basic Excel export failed: ' + error.message + '</div>');
            console.error('Export error:', error);
        }
    }
    
    function testAjaxExport() {
        $('#test-results').html('<div class="info">Testing AJAX export...</div>');
        
        $.ajax({
            url: 'ajax/get_export_simple.php',
            type: 'POST',
            dataType: 'json',
            success: function(response) {
                console.log('AJAX Response:', response);
                
                if (response.success) {
                    try {
                        // Test with real data
                        var excelData = [];
                        
                        // Header
                        excelData.push(['NO', 'NPSN', 'NAMA SEKOLAH', 'JENJANG']);
                        
                        // Data
                        response.data.forEach(function(row, index) {
                            excelData.push([
                                index + 1,
                                row.npsn || '-',
                                row.nama_sekolah || '-',
                                row.nm_jenjang || '-'
                            ]);
                        });
                        
                        // Create Excel
                        var wb = XLSX.utils.book_new();
                        var ws = XLSX.utils.aoa_to_sheet(excelData);
                        XLSX.utils.book_append_sheet(wb, ws, "Test Data");
                        XLSX.writeFile(wb, 'test_ajax_export.xlsx');
                        
                        $('#test-results').html('<div class="success">✓ AJAX Excel export successful! Records: ' + response.total_records + '</div>');
                        
                    } catch (error) {
                        $('#test-results').html('<div class="error">✗ Excel creation failed: ' + error.message + '</div>');
                        console.error('Excel error:', error);
                    }
                } else {
                    $('#test-results').html('<div class="error">✗ AJAX failed: ' + response.message + '</div>');
                }
            },
            error: function(xhr, status, error) {
                $('#test-results').html('<div class="error">✗ AJAX request failed: ' + error + '</div>');
                console.error('AJAX error:', xhr.responseText);
            }
        });
    }
    </script>
</body>
</html>
