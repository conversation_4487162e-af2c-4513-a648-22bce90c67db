/**
 * JavaScript untuk halaman Data User Pengurus
 * Menggunakan DataTables Server-side Processing
 */

$(document).ready(function() {
    
    // Inisialisasi DataTable
    var table = $('#table-user-pengurus').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "ajax/get_user_pengurus.php",
            "type": "POST",
            "error": function(xhr, error, thrown) {
                console.error('DataTables Error:', error);
                showAlert('error', 'Terjadi kesalahan saat memuat data: ' + error);
            }
        },
        "columns": [
            { 
                "data": null,
                "orderable": false,
                "searchable": false,
                "render": function(data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { 
                "data": "nm_user",
                "render": function(data, type, row) {
                    return '<strong>' + data + '</strong>';
                }
            },
            { 
                "data": "sebagai",
                "className": "text-center",
                "render": function(data, type, row) {
                    return data ? '<span class="badge badge-warning">' + data + '</span>' : '-';
                }
            },
            { 
                "data": "username",
                "className": "text-center",
                "render": function(data, type, row) {
                    return '<code>' + data + '</code>';
                }
            },
            { 
                "data": null,
                "orderable": false,
                "searchable": false,
                "className": "text-center",
                "render": function(data, type, row) {
                    return `
                        <button type="button" class="btn btn-warning btn-sm" onclick="showEditPasswordModal(${row.id_user}, '${row.nm_user}', '${row.sebagai}', '${row.username}')" title="Edit Password">
                            <i class="fas fa-edit"></i>
                        </button>
                    `;
                }
            }
        ],
        "order": [[1, "asc"]],
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]],
        "language": {
            "processing": "Sedang memproses...",
            "lengthMenu": "Tampilkan _MENU_ data per halaman",
            "zeroRecords": "Data tidak ditemukan",
            "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            "infoEmpty": "Menampilkan 0 sampai 0 dari 0 data",
            "infoFiltered": "(disaring dari _MAX_ total data)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        }
    });

    // Event handler untuk form submit edit password
    $('#form-edit-password').submit(function(e) {
        e.preventDefault();
        updatePassword();
    });

    // Event handler untuk toggle password visibility
    $('#toggle-password').click(function() {
        var passwordField = $('#new_password');
        var toggleIcon = $('#toggle-icon');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            toggleIcon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            toggleIcon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });

});

/**
 * Fungsi untuk menampilkan modal edit password
 */
function showEditPasswordModal(idUser, namaPengurus, jabatan, username) {
    // Reset form
    $('#form-edit-password')[0].reset();
    
    // Set data ke form
    $('#edit_id_user').val(idUser);
    $('#edit_username').val(username);
    
    // Set informasi pengurus
    $('#info-nama-pengurus').text('Nama: ' + namaPengurus);
    $('#info-jabatan').text('Jabatan: ' + jabatan);
    $('#info-username').text('Username: ' + username);
    
    // Reset password field type
    $('#new_password').attr('type', 'password');
    $('#toggle-icon').removeClass('fa-eye-slash').addClass('fa-eye');
    
    // Tampilkan modal
    $('#modal-edit-password').modal('show');
    
    // Focus ke field password
    setTimeout(function() {
        $('#new_password').focus();
    }, 500);
}

/**
 * Fungsi untuk update password
 */
function updatePassword() {
    // Validasi input
    var newPassword = $('#new_password').val().trim();
    
    if (!newPassword) {
        showAlert('error', 'Password baru harus diisi');
        return false;
    }
    
    // Disable tombol submit
    $('#btn-update-password').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Mengupdate...');

    // Serialize form data
    var formData = $('#form-edit-password').serialize();

    // AJAX request
    $.ajax({
        url: 'ajax/update_password.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Tutup modal
                $('#modal-edit-password').modal('hide');

                // Reload DataTable
                $('#table-user-pengurus').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat mengupdate password');
        },
        complete: function() {
            // Enable tombol submit
            $('#btn-update-password').prop('disabled', false).html('<i class="fas fa-save"></i> Update Password');
        }
    });
}

/**
 * Fungsi untuk menampilkan notifikasi modal
 */
function showNotification(type, message) {
    if (type === 'success') {
        $('#notifikasi-sukses-message').text(message);
        $('#modal-notifikasi-sukses').modal('show');
    } else if (type === 'error') {
        $('#notifikasi-error-message').text(message);
        $('#modal-notifikasi-error').modal('show');
    }
}

/**
 * Fungsi untuk menampilkan alert (fallback untuk loading/info)
 */
function showAlert(type, message) {
    // Untuk success dan error, gunakan modal
    if (type === 'success' || type === 'error') {
        showNotification(type, message);
        return;
    }

    // Untuk info/warning, gunakan alert biasa
    var alertClass = 'alert-info';
    var icon = 'fas fa-info-circle';

    switch(type) {
        case 'warning':
            alertClass = 'alert-warning';
            icon = 'fas fa-exclamation-circle';
            break;
    }

    var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                    '<i class="' + icon + '"></i> ' + message +
                    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                    '<span aria-hidden="true">&times;</span>' +
                    '</button>' +
                    '</div>';

    $('#alert-container').html(alertHtml);

    // Auto hide after 3 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 3000);
}
