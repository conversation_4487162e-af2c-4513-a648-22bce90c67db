/**
 * JavaScript untuk halaman Data Asesor
 * Menggunakan DataTables Server-side Processing
 */

$(document).ready(function() {
    
    // Inisialisasi DataTable
    var table = $('#table-asesor').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "ajax/get_asesor.php",
            "type": "POST",
            "error": function(xhr, error, thrown) {
                console.error('DataTables Error:', error);
                showAlert('error', 'Terjadi kesalahan saat memuat data: ' + error);
            }
        },
        "columns": [
            { 
                "data": null,
                "orderable": false,
                "searchable": false,
                "render": function(data, type, row, meta) {
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            { 
                "data": "nia",
                "className": "text-center",
                "render": function(data, type, row) {
                    return data ? data : '-';
                }
            },
            { 
                "data": "nm_asesor",
                "render": function(data, type, row) {
                    return '<strong>' + data + '</strong>';
                }
            },
            { 
                "data": "jk",
                "className": "text-center",
                "render": function(data, type, row) {
                    if (data === 'Pria') {
                        return '<span class="badge badge-primary">Pria</span>';
                    } else if (data === 'Wanita') {
                        return '<span class="badge badge-pink">Wanita</span>';
                    } else {
                        return '-';
                    }
                }
            },
            { 
                "data": "nm_kota",
                "render": function(data, type, row) {
                    return data ? data : '-';
                }
            },
            { 
                "data": "unit_kerja",
                "render": function(data, type, row) {
                    if (data && data.length > 30) {
                        return data.substring(0, 30) + '...';
                    }
                    return data ? data : '-';
                }
            },
            { 
                "data": "rumpun",
                "className": "text-center",
                "render": function(data, type, row) {
                    if (data === 'dasmen') {
                        return '<span class="badge badge-success">Dasmen</span>';
                    } else if (data === 'paud') {
                        return '<span class="badge badge-warning">PAUD</span>';
                    } else {
                        return '-';
                    }
                }
            },
            { 
                "data": "status_keaktifan_id",
                "className": "text-center",
                "render": function(data, type, row) {
                    if (row.status_keaktifan_id == '1') {
                        return '<span class="badge badge-success">Aktif</span>';
                    } else if (row.status_keaktifan_id == '0') {
                        return '<span class="badge badge-danger">Tidak Aktif</span>';
                    } else if (row.status_keaktifan_id == '2') {
                        return '<span class="badge badge-warning">Tidak Diketahui</span>';
                    } else {
                        return '-';
                    }
                }
            },
            { 
                "data": null,
                "orderable": false,
                "searchable": false,
                "className": "text-center",
                "render": function(data, type, row) {
                    return `
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-info btn-sm mr-2" onclick="showDetail(${row.id_asesor})" title="Detail">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button type="button" class="btn btn-warning btn-sm mr-2" onclick="loadEditForm(${row.id_asesor})" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-danger btn-sm mr-2" onclick="confirmDelete(${row.id_asesor}, '${row.nm_asesor}', '${row.nia}')" title="Hapus">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `;
                }
            }
        ],
        "order": [[6, "asc"]], // Urutkan berdasarkan rumpun (asc)
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]],
        "language": {
            "processing": "Sedang memproses...",
            "lengthMenu": "Tampilkan _MENU_ data per halaman",
            "zeroRecords": "Data tidak ditemukan",
            "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            "infoEmpty": "Menampilkan 0 sampai 0 dari 0 data",
            "infoFiltered": "(disaring dari _MAX_ total data)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        }
    });

    // Load dropdown options saat halaman dimuat
    loadDropdownOptions();

    // Event handler untuk tombol tambah data
    $('#btn-add').click(function() {
        showModalTambah();
    });

    // Event handler untuk form submit tambah
    $('#form-tambah-asesor').submit(function(e) {
        e.preventDefault();
        simpanDataAsesor();
    });

    // Event handler untuk konfirmasi hapus
    $('#btn-konfirmasi-hapus').click(function() {
        var asesorId = $(this).data('asesor-id');
        var namaAsesor = $(this).data('nama-asesor');
        var niaAsesor = $(this).data('nia-asesor');

        // Tutup modal konfirmasi
        $('#modal-konfirmasi-hapus').modal('hide');

        // Proses hapus
        deleteAsesor(asesorId, namaAsesor, niaAsesor);
    });

    // Event handler untuk tombol export excel
    $('#btn-export-excel').click(function() {
        exportToExcel();
    });

});

/**
 * Fungsi untuk load dropdown options
 */
function loadDropdownOptions() {
    // Load dropdown kab/kota
    $.ajax({
        url: 'ajax/get_dropdown_options.php',
        type: 'POST',
        data: { type: 'kab_kota' },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var options = '<option value="">-- Pilih Kabupaten/Kota --</option>';
                $.each(response.data, function(index, item) {
                    options += '<option value="' + item.kota_id + '">' + item.nm_kota + '</option>';
                });
                $('#kota_id').html(options);
            }
        },
        error: function() {
            console.error('Gagal memuat dropdown kab/kota');
        }
    });

    // Load dropdown jenjang
    $.ajax({
        url: 'ajax/get_dropdown_options.php',
        type: 'POST',
        data: { type: 'jenjang' },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var options = '<option value="">-- Pilih Jenjang --</option>';
                $.each(response.data, function(index, item) {
                    options += '<option value="' + item.jenjang_id + '">' + item.nm_jenjang + '</option>';
                });
                $('#jenjang_id').html(options);
            }
        },
        error: function() {
            console.error('Gagal memuat dropdown jenjang');
        }
    });
}

/**
 * Fungsi untuk menampilkan modal tambah
 */
function showModalTambah() {
    // Reset form
    $('#form-tambah-asesor')[0].reset();

    // Clear alert sebelumnya
    $('#modal-tambah .alert').remove();

    // Generate kode asesor baru
    var newKdAsesor = generateUniqueId();
    $('#kd_asesor').val(newKdAsesor);

    // Debug: Log kode asesor yang di-generate
    console.log('Generated kd_asesor:', newKdAsesor);

    // Tampilkan modal
    $('#modal-tambah').modal('show');

    // Focus ke field pertama
    setTimeout(function() {
        $('#nia').focus();
    }, 500);
}

/**
 * Generate unique ID mirip uniqid() PHP
 */
function generateUniqueId() {
    // Mirip uniqid() PHP: timestamp + random
    var timestamp = Math.floor(Date.now() / 1000).toString(16);
    var random = Math.random().toString(16).substr(2, 8);
    return timestamp + random;
}

/**
 * Fungsi untuk menyimpan data asesor
 */
function simpanDataAsesor() {
    // Disable tombol submit
    $('#btn-simpan').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');

    // Serialize form data
    var formData = $('#form-tambah-asesor').serialize();

    // Debug: Log form data yang akan dikirim
    console.log('Form data to send:', formData);
    console.log('kd_asesor value:', $('#kd_asesor').val());

    // AJAX request
    $.ajax({
        url: 'ajax/simpan_asesor.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Tutup modal
                $('#modal-tambah').modal('hide');

                // Reload DataTable
                $('#table-asesor').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat menyimpan data');
        },
        complete: function() {
            // Enable tombol submit
            $('#btn-simpan').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Data');
        }
    });
}

/**
 * Fungsi untuk load form edit via AJAX
 */
function loadEditForm(asesorId) {
    $('#modal-edit').modal('show');
    
    // Reset content
    $('#modal-edit .modal-content').html(
        '<div class="modal-body text-center">' +
        '<i class="fas fa-spinner fa-spin fa-2x"></i>' +
        '<p class="mt-2">Memuat form edit...</p>' +
        '</div>'
    );

    // Load form via AJAX
    $.ajax({
        url: 'ajax/get_edit_form.php',
        type: 'POST',
        data: { id_asesor: asesorId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Replace modal content dengan form yang sudah ter-populate
                $('#modal-edit .modal-content').html(response.html);
                
                // Bind event handler untuk form edit
                bindEditFormEvents();
                
            } else {
                $('#modal-edit .modal-content').html(`
                    <div class="modal-header bg-danger">
                        <h5 class="modal-title text-white">
                            <i class="fas fa-exclamation-triangle"></i> Error
                        </h5>
                        <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                        <p class="mt-2">${response.message}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    </div>
                `);
            }
        },
        error: function(xhr, status, error) {
            $('#modal-edit .modal-content').html(`
                <div class="modal-header bg-danger">
                    <h5 class="modal-title text-white">
                        <i class="fas fa-exclamation-triangle"></i> Error
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                    <p class="mt-2">Terjadi kesalahan saat memuat form edit</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                </div>
            `);
        }
    });
}

/**
 * Fungsi untuk bind event handler form edit
 */
function bindEditFormEvents() {
    // Event handler untuk form submit edit
    $('#form-edit-asesor').off('submit').on('submit', function(e) {
        e.preventDefault();
        updateDataAsesor();
    });

    // Event handler untuk tombol update
    $('#btn-update').off('click').on('click', function(e) {
        e.preventDefault();
        updateDataAsesor();
    });
}

/**
 * Fungsi untuk update data asesor
 */
function updateDataAsesor() {
    // Disable tombol submit
    $('#btn-update').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Mengupdate...');

    // Serialize form data
    var formData = $('#form-edit-asesor').serialize();

    // AJAX request
    $.ajax({
        url: 'ajax/update_asesor.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Tutup modal
                $('#modal-edit').modal('hide');

                // Reload DataTable
                $('#table-asesor').DataTable().ajax.reload(null, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat mengupdate data');
        },
        complete: function() {
            // Enable tombol submit
            $('#btn-update').prop('disabled', false).html('<i class="fas fa-save"></i> Update Data');
        }
    });
}

/**
 * Fungsi untuk konfirmasi hapus data menggunakan modal
 */
function confirmDelete(asesorId, namaAsesor, niaAsesor) {
    // Set data ke tombol konfirmasi
    $('#btn-konfirmasi-hapus').data('asesor-id', asesorId);
    $('#btn-konfirmasi-hapus').data('nama-asesor', namaAsesor);
    $('#btn-konfirmasi-hapus').data('nia-asesor', niaAsesor);

    // Set nama asesor di modal
    $('#nama-asesor-hapus').text(namaAsesor);
    $('#nia-asesor-hapus').text('NIA: ' + niaAsesor);

    // Tampilkan modal konfirmasi
    $('#modal-konfirmasi-hapus').modal('show');
}

/**
 * Fungsi untuk menghapus data asesor (soft delete)
 */
function deleteAsesor(asesorId, namaAsesor, niaAsesor) {
    $.ajax({
        url: 'ajax/hapus_asesor.php',
        type: 'POST',
        data: {
            id_asesor: asesorId,
            nm_asesor: namaAsesor,
            nia: niaAsesor
        },
        dataType: 'json',
        success: function(response) {
            // Debug: Log response
            console.log('Delete response:', response);

            if (response.success) {
                // Force reload DataTable dengan clear cache
                var table = $('#table-asesor').DataTable();
                table.ajax.reload(function() {
                    console.log('DataTable reloaded after delete');
                }, false);

                // Show success message
                showAlert('success', response.message);
            } else {
                // Show error message
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat menghapus data');
        }
    });
}

/**
 * Fungsi untuk menampilkan detail asesor
 */
function showDetail(asesorId) {
    $('#modal-detail').modal('show');

    // Reset content
    $('#modal-detail-content').html(
        '<div class="text-center">' +
        '<i class="fas fa-spinner fa-spin fa-2x"></i>' +
        '<p class="mt-2">Memuat data...</p>' +
        '</div>'
    );

    // Load detail via AJAX
    $.ajax({
        url: 'ajax/get_detail_asesor.php',
        type: 'POST',
        data: { id_asesor: asesorId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                displayDetailContent(response.data);
            } else {
                $('#modal-detail-content').html(
                    '<div class="alert alert-danger">' +
                    '<i class="fas fa-exclamation-triangle"></i> ' +
                    response.message +
                    '</div>'
                );
            }
        },
        error: function(xhr, status, error) {
            $('#modal-detail-content').html(
                '<div class="alert alert-danger">' +
                '<i class="fas fa-exclamation-triangle"></i> ' +
                'Terjadi kesalahan saat memuat detail asesor' +
                '</div>'
            );
        }
    });
}

/**
 * Fungsi untuk menampilkan konten detail asesor
 */
function displayDetailContent(data) {
    var html = '<div class="row">';

    // Kolom Kiri - Data Pribadi
    html += '<div class="col-md-6">';
    html += '<h6 class="text-primary"><i class="fas fa-user"></i> Data Pribadi</h6>';
    html += createDetailRow('ID Asesor', data.id_asesor);
    html += createDetailRow('Kode Asesor', data.kd_asesor);
    html += createDetailRow('NIA', data.nia);
    html += createDetailRow('Nama Asesor', data.nm_asesor);
    html += createDetailRow('Jenis Kelamin', data.jk);
    html += createDetailRow('No. KTP', data.ktp);
    html += createDetailRow('Tempat Lahir', data.tempat_lahir);
    html += createDetailRow('Tanggal Lahir', data.tgl_lahir);
    html += createDetailRow('Kabupaten/Kota', data.nm_kota);
    html += '</div>';

    // Kolom Kanan - Data Pekerjaan
    html += '<div class="col-md-6">';
    html += '<h6 class="text-primary"><i class="fas fa-briefcase"></i> Data Pekerjaan</h6>';
    html += createDetailRow('Unit Kerja', data.unit_kerja);
    html += createDetailRow('Jabatan', data.jabatan);
    html += createDetailRow('Jabatan Struktural', data.jabatan_struktural);
    html += createDetailRow('Pendidikan', data.pendidikan);
    html += createDetailRow('Jenjang', data.nm_jenjang);
    html += createDetailRow('Rumpun', data.rumpun);
    html += createDetailRow('Grade', data.grade);
    html += createDetailRow('No. Sertifikat', data.no_sertifikat);
    html += createDetailRow('Tahun Terbit Sertifikat', data.thn_terbit_sertifikat);
    html += createDetailRow('Status Keaktifan', data.nm_status);
    html += '</div>';

    html += '</div>';

    // Baris Bawah - Kontak & Alamat
    html += '<div class="row mt-3">';
    html += '<div class="col-md-12">';
    html += '<h6 class="text-primary"><i class="fas fa-address-book"></i> Kontak & Alamat</h6>';
    html += '</div>';
    html += '<div class="col-md-6">';
    html += createDetailRow('No. HP', data.no_hp);
    html += createDetailRow('No. WhatsApp', data.no_wa);
    html += createDetailRow('Email', data.email);
    html += '</div>';
    html += '<div class="col-md-6">';
    html += createDetailRow('Alamat Kantor', data.alamat_kantor);
    html += createDetailRow('Alamat Rumah', data.alamat_rumah);
    html += '</div>';
    html += '</div>';

    $('#modal-detail-content').html(html);
}

/**
 * Fungsi helper untuk membuat baris detail
 */
function createDetailRow(label, value) {
    return '<div class="modal-detail-row">' +
           '<div class="modal-detail-label">' + label + ':</div>' +
           '<div class="modal-detail-value">' + (value ? value : '-') + '</div>' +
           '</div>';
}

/**
 * Fungsi untuk menampilkan notifikasi modal
 */
function showNotification(type, message) {
    if (type === 'success') {
        $('#notifikasi-sukses-message').text(message);
        $('#modal-notifikasi-sukses').modal('show');
    } else if (type === 'error') {
        $('#notifikasi-error-message').text(message);
        $('#modal-notifikasi-error').modal('show');
    }
}

/**
 * Fungsi untuk menampilkan alert (fallback untuk loading/info)
 */
function showAlert(type, message) {
    // Untuk error di dalam modal form, gunakan alert inline
    if (type === 'error' && ($('#modal-tambah').hasClass('show') || $('#modal-edit').hasClass('show'))) {
        showInlineAlert(type, message);
        return;
    }

    // Untuk success dan error di luar modal, gunakan modal notification
    if (type === 'success' || type === 'error') {
        showNotification(type, message);
        return;
    }

    // Untuk info/warning, gunakan alert biasa
    var alertClass = 'alert-info';
    var icon = 'fas fa-info-circle';

    switch(type) {
        case 'warning':
            alertClass = 'alert-warning';
            icon = 'fas fa-exclamation-circle';
            break;
    }

    var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                    '<i class="' + icon + '"></i> ' + message +
                    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                    '<span aria-hidden="true">&times;</span>' +
                    '</button>' +
                    '</div>';

    $('#alert-container').html(alertHtml);

    // Auto hide after 3 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 3000);
}

/**
 * Fungsi untuk menampilkan alert inline di dalam modal
 */
function showInlineAlert(type, message) {
    var alertClass = type === 'error' ? 'alert-danger' : 'alert-warning';
    var icon = type === 'error' ? 'fas fa-exclamation-triangle' : 'fas fa-exclamation-circle';

    var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show mt-2" role="alert">' +
                    '<i class="' + icon + '"></i> ' + message +
                    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                    '<span aria-hidden="true">&times;</span>' +
                    '</button>' +
                    '</div>';

    // Tampilkan di dalam modal yang sedang aktif
    if ($('#modal-tambah').hasClass('show')) {
        $('#modal-tambah .modal-body').prepend(alertHtml);
    } else if ($('#modal-edit').hasClass('show')) {
        $('#modal-edit .modal-body').prepend(alertHtml);
    }

    // Auto hide after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow', function() {
            $(this).remove();
        });
    }, 5000);
}

/**
 * Fungsi untuk export data ke Excel menggunakan SheetJS
 */
function exportToExcel() {
    // Show loading
    showAlert('info', 'Sedang memproses export data asesor aktif...');

    // Get all data from server (not just current page)
    $.ajax({
        url: 'ajax/get_export_data.php',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Prepare data for Excel
                var excelData = [];

                // Add header row
                excelData.push([
                    'NO',
                    'NIA',
                    'NAMA ASESOR',
                    'JENIS KELAMIN',
                    'KAB/KOTA',
                    'UNIT KERJA',
                    'RUMPUN',
                    'JENJANG',
                    'STATUS KEAKTIFAN',
                    'NO. KTP',
                    'TEMPAT LAHIR',
                    'TANGGAL LAHIR',
                    'JABATAN',
                    'JABATAN STRUKTURAL',
                    'PENDIDIKAN',
                    'GRADE',
                    'NO. SERTIFIKAT',
                    'TAHUN TERBIT SERTIFIKAT',
                    'NO. HP',
                    'NO. WHATSAPP',
                    'EMAIL',
                    'ALAMAT KANTOR',
                    'ALAMAT RUMAH'
                ]);

                // Add data rows
                response.data.forEach(function(row, index) {
                    excelData.push([
                        index + 1,
                        row.nia || '',
                        row.nm_asesor || '',
                        row.jk || '',
                        row.nm_kota || '',
                        row.unit_kerja || '',
                        row.rumpun || '',
                        row.nm_jenjang || '',
                        row.nm_status || '',
                        row.ktp || '',
                        row.tempat_lahir || '',
                        row.tgl_lahir || '',
                        row.jabatan || '',
                        row.jabatan_struktural || '',
                        row.pendidikan || '',
                        row.grade || '',
                        row.no_sertifikat || '',
                        row.thn_terbit_sertifikat || '',
                        row.no_hp || '',
                        row.no_wa || '',
                        row.email || '',
                        row.alamat_kantor || '',
                        row.alamat_rumah || ''
                    ]);
                });

                // Create workbook and worksheet
                var wb = XLSX.utils.book_new();
                var ws = XLSX.utils.aoa_to_sheet(excelData);

                // Set column widths
                ws['!cols'] = [
                    {wch: 5},   // NO
                    {wch: 15},  // NIA
                    {wch: 25},  // NAMA ASESOR
                    {wch: 12},  // JENIS KELAMIN
                    {wch: 15},  // KAB/KOTA
                    {wch: 25},  // UNIT KERJA
                    {wch: 10},  // RUMPUN
                    {wch: 12},  // JENJANG
                    {wch: 15},  // STATUS KEAKTIFAN
                    {wch: 18},  // NO. KTP
                    {wch: 15},  // TEMPAT LAHIR
                    {wch: 12},  // TANGGAL LAHIR
                    {wch: 20},  // JABATAN
                    {wch: 18},  // JABATAN STRUKTURAL
                    {wch: 12},  // PENDIDIKAN
                    {wch: 8},   // GRADE
                    {wch: 15},  // NO. SERTIFIKAT
                    {wch: 18},  // TAHUN TERBIT SERTIFIKAT
                    {wch: 15},  // NO. HP
                    {wch: 15},  // NO. WHATSAPP
                    {wch: 20},  // EMAIL
                    {wch: 25},  // ALAMAT KANTOR
                    {wch: 25}   // ALAMAT RUMAH
                ];

                // Add worksheet to workbook
                XLSX.utils.book_append_sheet(wb, ws, "Data Asesor Aktif");

                // Generate filename with current date
                var today = new Date();
                var dateStr = today.getFullYear() + '-' +
                             String(today.getMonth() + 1).padStart(2, '0') + '-' +
                             String(today.getDate()).padStart(2, '0');
                var filename = 'Data_Asesor_Aktif_' + dateStr + '.xlsx';

                // Save file
                XLSX.writeFile(wb, filename);

                // Show success message
                showAlert('success', 'File Excel berhasil didownload: ' + filename);

            } else {
                showAlert('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showAlert('error', 'Terjadi kesalahan saat export data');
        }
    });
}
